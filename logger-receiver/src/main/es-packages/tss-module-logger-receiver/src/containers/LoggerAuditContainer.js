import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { intlShape, injectIntl } from 'react-intl';
import { bindActionCreators } from 'redux';
import { fetchAuditTrail, fetchLogger } from 'tss-lib-data';
import { UserShape, AuditEntryShapeList, LoggerShapeList, historyShape } from 'tss-lib-common';
import { Loading, FadeOut } from 'tss-lib-components';

import AuditPage from '../pages/AuditPage';
import { LoggerListRoute, LoggerDetailsRoute } from '../routes';

class LoggerAuditContainer extends Component {
  static contextTypes = {};

  static propTypes = {
    intl: intlShape.isRequired,
    user: UserShape.isRequired,
    history: historyShape.isRequired,
    match: PropTypes.shape( {
      params: PropTypes.shape( {
        id: PropTypes.string.isRequired,
      } ).isRequired,
    } ).isRequired,

    loggers: LoggerShapeList.isRequired, // eslint-disable-line
    auditEntries: AuditEntryShapeList.isRequired,
    fetchLogger: PropTypes.func.isRequired, // eslint-disable-line
    fetchAuditTrail: PropTypes.func.isRequired,
  };

  static defaultProps = {};

  state = { dataInitialized: false };

  async componentWillMount() {
    const id = parseInt( this.props.match.params.id, 10 );
    if ( Number.isNaN( id ) ) {
      this.props.history.push( '/error/404' );
      return;
    }

    let logger = this.props.loggers.find( ( l ) => l.id === id );
    if ( !logger ) {
      await this.props.fetchLogger( id );
    }
    logger = this.props.loggers.find( ( l ) => l.id === id );

    if ( !logger ) {
      this.props.history.push( '/error/404' );
      return;
    }

    if ( logger.auditUuid && logger.auditUuid.length ) {
      await this.props.fetchAuditTrail( { uuid: logger.auditUuid, action: 'LOGGER_MANAGEMENT' }, { clear: true } );
    }
    this.setState( { dataInitialized: true, logger } );
  }

  render() {
    if ( !this.state.dataInitialized ) {
      return <FadeOut><Loading fill /></FadeOut>;
    }

    let { dateTimeFormat } = this.props.user;
    // Remove the timezone formatter, if it exists
    dateTimeFormat = `${ dateTimeFormat.replace( /ZZ\s*$/, '' ) } [UTC]`;

    const auditEntries = this.props.auditEntries.filter( ( { uuid } ) => uuid === this.state.logger.auditUuid );
    const breadcrumbs = [
      { location: { pathname: '/' }, label: 'TSS' },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.monitor.administration' } ) },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.loggers.logger.list.heading' } ) },
      { location: { pathname: LoggerListRoute.url() }, label: this.props.intl.formatMessage( { id: 'module.loggers.adjustment.overview' } ) },
      { location: { pathname: LoggerDetailsRoute.url( { id: this.state.logger.id } ) }, label: this.state.logger.unitSerialNo },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.loggers.details.audit.page.crumb' } ) },
    ];

    return (
      <AuditPage
        pageHeading={ 'module.loggers.details.audit.page.heading' }
        pageHeadingNoEntries={
          'module.loggers.details.audit.page.no.entries.found'
        }
        auditEntries={ auditEntries }
        breadcrumbs={ breadcrumbs }
        dateTimeFormat={ dateTimeFormat }
      />
    );
  }
}

function mapStateToProps( state ) {
  return {
    user: state.authState.user,
    loggers: state.loggerState.loggers,
    auditEntries: state.auditState.entries,
  };
}

function mapDispatchToProps( dispatch ) {
  return bindActionCreators(
    {
      fetchAuditTrail,
      fetchLogger,
    },
    dispatch,
  );
}

export default connect( mapStateToProps, mapDispatchToProps )( injectIntl( LoggerAuditContainer ) );
