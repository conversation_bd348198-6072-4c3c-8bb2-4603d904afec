/*
ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
*/
import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment-timezone';
import { injectIntl, intlShape } from 'react-intl';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { recordFromJs } from 'redux-immutable-tools';
import {
  Logger,
  UserShape,
  LoggerShapeList,
  LoggerType<PERSON>hape<PERSON>ist,
  LoggerModelShapeList,
  TrialUnitShapeList,
  TrialUnit,
  historyShape,
  diff,
  isAfterDate,
  validate<PERSON>anufacturer,
  DATE_FORMAT,
  locationShape,
  CompanyShape,
  addCurrentLoggerModelToModels,
} from 'tss-lib-common';
import {
  fetchApprovedTrialUnits,
  fetchTrialUnit,
  fetchLogger,
  fetchLoggerTypes,
  fetchLoggerModels,
  sendUpdateLogger,
  sendDeactivateLogger,
  sendReactivateLogger,
  sendCancelReplaceLogger,
  setFlashMessage,
  fetchGlobalProbeSlots,
} from 'tss-lib-data';
import {
  Loading,
  withConfirmDialog,
  MessageDialog,
  FadeOut,
  Prompt,
} from 'tss-lib-components';

import LoggerDetailsPage from '../pages/LoggerDetailsPage';
import SaveLoggerDialog from '../components/SaveLoggerDialog';
import { recentLoggerToTableRow } from '../transforms';
import {
  LoggerListRoute,
  ReplaceLoggerRoute,
  LoggerDetailsRoute,
  LoggerAuditRoute,
} from '../routes';

function addValidation( logger ) {
  if ( !logger ) {
    return null;
  }
  return logger
    .addLocalValidator( 'expiryDate', ( value ) =>
      // eslint-disable-line
      isAfterDate( moment.utc().endOf( 'day' ) )( value ),
    );
}

class LoggerDetailsContainer extends Component {
  static contextTypes = {};

  static propTypes = {
    intl: intlShape.isRequired,
    user: UserShape.isRequired,
    company: CompanyShape.isRequired,
    history: historyShape.isRequired,
    match: PropTypes.shape( {
      params: PropTypes.shape( {
        id: PropTypes.string.isRequired,
      } ).isRequired,
    } ).isRequired,
    location: locationShape.isRequired,
    loggers: LoggerShapeList.isRequired,
    loggersInitialized: PropTypes.bool.isRequired, // eslint-disable-line
    types: LoggerTypeShapeList.isRequired,
    models: LoggerModelShapeList.isRequired,
    typesInitialized: PropTypes.bool.isRequired,
    modelsInitialized: PropTypes.bool.isRequired,
    trialUnits: TrialUnitShapeList.isRequired,
    trialUnitsInitialized: PropTypes.bool.isRequired,

    confirm: PropTypes.func.isRequired,

    fetchLogger: PropTypes.func.isRequired,
    fetchLoggerTypes: PropTypes.func.isRequired, // eslint-disable-line
    fetchLoggerModels: PropTypes.func.isRequired, // eslint-disable-line
    fetchApprovedTrialUnits: PropTypes.func.isRequired, // eslint-disable-line
    fetchTrialUnit: PropTypes.func.isRequired,
    sendUpdateLogger: PropTypes.func.isRequired,
    sendDeactivateLogger: PropTypes.func.isRequired,
    sendReactivateLogger: PropTypes.func.isRequired,
    sendCancelReplaceLogger: PropTypes.func.isRequired,
    fetchGlobalProbeSlots: PropTypes.func.isRequired,
  };

  static defaultProps = {};

  state = {
    initialized: false,
    loggerChanged: false,
  };

  async componentDidMount() {
    const id = parseInt( this.props.match.params.id, 10 );
    if ( Number.isNaN( id ) ) {
      this.props.history.push( '/error/404' );
      return;
    }
    const getLogger = await this.props.fetchLogger( id );
    const logger = getLogger.logger;
    await this.props.fetchApprovedTrialUnits();
    await this.props.fetchLoggerTypes();
    await this.props.fetchLoggerModels( true );
    await this.checkIfAnyPreOrSuccessorLogger( logger );
    const upDatedModels = await addCurrentLoggerModelToModels( Logger.normalize( logger ), this.props.models );
    this.initialized( upDatedModels );
    const availableSlots = await this.getAvailableSLots( logger );
    // eslint-disable-next-line react/no-did-mount-set-state
    this.setState( { logger: addValidation( availableSlots ) } );
  }
  async componentWillReceiveProps( nextProps ) {
    if ( this.props.loggers !== nextProps.loggers ) {
      this.setState( { logger: addValidation( this.findLogger( nextProps ) ) } );
    }
    if ( nextProps.history.location.pathname !== this.props.location.pathname ) {
      this.setState( { loggerChanged: false } );
    }
  }

  getAvailableSLots =async ( logger ) => {
    let slots;
    if ( logger.sensorParentLoggerSlotLink && logger.sensorParentLoggerSlotLink.parentGlobalLoggerId && logger.sensorParentLoggerSlotLink.parentSensorSlotLinkDto.parentModelId ) {
      if ( !logger.uploads.length ) {
        slots = await this.props.fetchGlobalProbeSlots( logger.sensorParentLoggerSlotLink.parentGlobalLoggerId, logger.sensorParentLoggerSlotLink.parentSensorSlotLinkDto.parentModelId );
      }
    }
    const models = this.state.models.find( ( { manufacturerDisplayName } ) => manufacturerDisplayName === 'Tss' );
    const loggerModelCesorSLots = models.sensorSlots;
    let loggerState = this.state.logger;
    const getAvailableSensorSlot = loggerModelCesorSLots.reduce( ( acc, curr ) => {
      const res = slots && slots.filter( ( { loggerModelSensorSlotId } ) => loggerModelSensorSlotId === curr.id );
      if ( res ) {
        res[ 0 ].displayName = res[ 0 ].unitSerialNo ? `${ curr.displayName }[${ res[ 0 ].unitSerialNo }]` : curr.displayName;
        acc.push( res[ 0 ] );
      }
      return acc;
    }, [] );
    loggerState = loggerState.set( 'globalProbeSlots', getAvailableSensorSlot );
    if ( loggerState.globalProbeSlots.length ) {
      const selectedOne = loggerState.globalProbeSlots.find( ( { loggerModelSensorSlotId } ) => loggerState.sensorParentLoggerSlotLink.parentSensorSlotLinkDto.id === loggerModelSensorSlotId );
      if ( selectedOne ) {
        selectedOne.displayName = `${ loggerState.sensorParentLoggerSlotLink.parentSensorSlotLinkDto.displayName }[${ loggerState.sensorParentLoggerSlotLink.childUnitSerialNo }]`;
        selectedOne.calibrationExpiryDate = loggerState.expiryDate;
        selectedOne.unitSerialNo = loggerState.sensorParentLoggerSlotLink.childUnitSerialNo;
        selectedOne.calCertificateUrl = `${ loggerState.calCertificateUrl }`;
        selectedOne.loggerModelSensorSlotId = loggerState.sensorParentLoggerSlotLink.parentSensorSlotLinkDto.id;
        selectedOne.registered = false;
        loggerState = loggerState.set( 'selectedProbeSlot', selectedOne );
      }
    }
    return loggerState;
  }
  async checkIfAnyPreOrSuccessorLogger( logger ) {
    if ( logger.predecessorLoggerId ) {
      const predecessorLogger = await this.props.fetchLogger(
        logger.predecessorLoggerId,
      );
      this.setState( {
        predecessorLogger: recordFromJs( Logger, predecessorLogger.logger ),
      } );
    }

    if ( logger.successorLoggerId ) {
      await this.props.fetchLogger( logger.successorLoggerId );
      const successorLogger = await this.props.fetchLogger(
        logger.successorLoggerId,
      );
      this.setState( { successorLogger } );
    }
  }

  initialized( upDatedModels ) {
    this.setState( { initialized: true, models: upDatedModels } );
    if ( !this.state.logger ) {
      this.setState( { logger: addValidation( this.findLogger( this.props ) ) } );
    }
  }

  findLogger = ( props ) => {
    const loggerId = parseInt( props.match.params.id, 10 );
    return props.loggers.find( ( { id } ) => id === loggerId );
  };

  findSuccessorLogger = ( props, successorLoggerId ) =>
    props.loggers.find( ( { id } ) => id === successorLoggerId );

  /**
   * After a logger collision (E.g. logger modified by another user) we need to updated current logger
   * to get a new updateIdentifier and any changes made. If logger has been replaced the successor logger
   * needs to be fetched.
   */
  handleStateConflict = async () => {
    let logger = this.findLogger( this.props );
    // refresh logger
    await this.props.fetchLogger( logger.id );
    logger = this.findLogger( this.props );
    // Has logger has been replaced
    if ( logger.successorLoggerId ) {
      // fetch successor logger
      await this.props.fetchLogger( logger.successorLoggerId );
    }
    this.setState( { loggersStateConflict: false, loggerChanged: false } );
  };

  handleLoggerChange = ( logger ) => {
    const originalLogger = this.findLogger( this.props );
    // After 'serialize' patchDiff has 'clinicalLoggerModelId' instead of 'parentModelId'
    const patchDiff = diff( originalLogger.serialize(), logger.serialize() );
    const trialUnitDiffModified = [];
    logger.clinicalTrialUnits.forEach( ( { id } ) => {
      if ( logger.isAssociatedToTrialUnit( id ) && originalLogger.isAssociatedToTrialUnit( id ) ) {
        if ( logger.getAssociationStatus( id ) !== originalLogger.getAssociationStatus( id ) ) {
          trialUnitDiffModified.push(
            this.props.trialUnits.find( ( i ) => i.id === id ),
          );
        }
      }
      if ( logger.getAssociationStatus( id ) === 'FUTURE_ASSOCIATION' && originalLogger.getAssociationStatus( id ) === 'FUTURE_ASSOCIATION' ) {
        if ( logger.getClinicalTrialUnitAssociation( id ).futureAssociationDate !== originalLogger.getClinicalTrialUnitAssociation( id ).futureAssociationDate ) {
          trialUnitDiffModified.push(
            this.props.trialUnits.find( ( i ) => i.id === id ),
          );
        }
      }
    } );
    const trialUnitDiff = [];
    logger.clinicalTrialUnits.forEach( ( { id } ) => {
      if (
        logger.isAssociatedToTrialUnit( id ) &&
        !originalLogger.isAssociatedToTrialUnit( id )
      ) {
        trialUnitDiff.push(
          this.props.trialUnits.find( ( i ) => i.id === id ),
        );
      }
    } );
    const trialUnitDiffRemoved = [];
    originalLogger.clinicalTrialUnits.forEach( async ( { id } ) => {
      if (
        !logger.isAssociatedToTrialUnit( id ) &&
        originalLogger.isAssociatedToTrialUnit( id )
      ) {
        let removedTrialUnit = this.props.trialUnits.find( ( i ) => i.id === id );
        if ( !removedTrialUnit ) {
          const trialUnitTemp = await this.props.fetchTrialUnit( id );
          removedTrialUnit = recordFromJs( TrialUnit, TrialUnit.normalize( trialUnitTemp ) );
        }
        trialUnitDiffRemoved.push(
          removedTrialUnit,
        );
      }
    } );
    if ( !trialUnitDiff.length && !trialUnitDiffModified.length ) {
      delete patchDiff.trialUnitAssociations;
    } else {
      patchDiff.trialUnitAssociations = trialUnitDiff.concat( trialUnitDiffModified );
    }
    if ( !trialUnitDiffRemoved.length ) {
      delete patchDiff.trialUnitsToDisassociate;
    } else {
      patchDiff.trialUnitsToDisassociate = trialUnitDiffRemoved;
    }

    if ( moment( originalLogger.expiryDate ).isSame( logger.expiryDate, 'date' ) ) {
      delete patchDiff.expiryDate;
    }

    if ( Object.keys( patchDiff ).length ) {
      const trialUnitDiffs = {
        trialUnitDiffModified,
        trialUnitDiff,
        trialUnitDiffRemoved,
      };
      this.setState( { logger, loggerChanged: true, trialUnitDiffs, patchDiff } );
    } else {
      this.setState( { logger, loggerChanged: false, trialUnitDiffs: null, patchDiff: null } );
    }
  };

  pushResultToState = ( logger ) => {
    const loggerType = this.props.types.find(
      ( { id } ) => id === logger.clinicalLoggerTypeId,
    );
    const expiryDate = moment
      .utc( logger.expiryDate )
      .format( this.props.user.dateFormat || DATE_FORMAT );
    const detailsUrl = LoggerDetailsRoute.url( { id: logger.id } );
    this.props.history.push( {
      pathname: LoggerListRoute.url(),
      state: {
        recentChangedData: recentLoggerToTableRow(
          logger,
          this.props.intl,
          detailsUrl,
          logger.clinicalLoggerModelName,
          loggerType.type,
          expiryDate,
        ),
      },
    } );
  };

  handleSaveLogger = async () => {
    const { logger } = this.state;
    const validatedLogger = this.state.logger;
    this.setState( { validatedLogger, saveLoggerFlow: null } );
    if ( validatedLogger.isValid() ) {
      const originalLogger = this.findLogger( this.props );
      const patchDiff = diff( originalLogger.serialize(), logger.serialize() );
      if ( Object.keys( patchDiff ).length ) {
        this.setState( {
          loading: true,
          logger: validatedLogger,
          loggerChanged: false,
        } );
        patchDiff.updateIdentifier = originalLogger.updateIdentifier;
        if ( logger.hasSensorSlots && ( logger.sensorParentLoggerSlotLink.parentSensorLoggerId !== originalLogger.sensorParentLoggerSlotLink.parentSensorLoggerId ) ) {
          patchDiff.parentSensorLoggerId = logger.sensorParentLoggerSlotLink.parentSensorLoggerId;
        }
        try {
          this.props.sendUpdateLogger( logger.id, patchDiff ).then( ( ) => {
            this.props.fetchLogger( logger.id ).then( ( result ) => {
              this.pushResultToState( recordFromJs( Logger, result.logger ) );
            } );
          } ).catch( ( response ) => {
            if ( response.data && response.data.message ) {
              if ( response.data.message === 'module.loggers.update.not.allowed.trialAssociation.absentLoggerType' ) {
                this.setState( {
                  absentLoggerTypeError: true,
                  errorTypeTrialNames: response.data.values,
                  absentLoggerTypeMessage: response.data.message,
                  saveLoggerFlow: null,
                  loading: false,
                } );
              } else {
                this.setState( {
                  messageNew: true,
                  new: {
                    heading: 'module.loggers.not.allowed.heading',
                    content: response.data.message,
                  },
                } );
              }
            }
          } );
        } catch ( { data, response } ) {
          const state = { loading: false, loggerChanged: true };
          if ( data && data.message && data.attribute ) {
            if ( [ 'activeTrialNotCovered' ].includes( data.attribute ) ) {
              const setStateMessage = () => {
                this.setState( {
                  loggersStateNotAllowedConflict: true,
                  notAllowedMessage: `${ 'module.loggers.state.not.allowed.' }${
                    data.attribute
                  }`,
                } );
              };
              switch ( data.attribute ) {
                case 'activeTrialNotCovered':
                  setStateMessage( data.attribute );
                  break;
                case 'alreadyReplaced':
                  setStateMessage( data.attribute );
                  break;
                case 'scheduledToReplace':
                  setStateMessage( data.attribute );
                  break;
                default:
                  // no default
                  break;
              }
            }
            // Match on 'clinicalTrialUnitIds'
            const attribute = data.attribute.match( /^clinicalTrialUnit/ )
              ? 'clinicalTrialUnits'
              : data.attribute;
            state.logger = this.state.logger.setValidationError(
              attribute,
              data.message,
            );
          } else if ( data && data.message === 'module.loggers.state.conflict' ) {
            this.setState( { loggersStateConflict: true } );
          } else {
            this.setState( { unknownErrorDialog: true } );
          }
          this.setState( state );
        } finally {
          this.setState( { loading: false } );
        }
      }
    }
    this.setState( { logger: validatedLogger } );
  };

  handleReplaceLogger = () =>
    this.props.history.push(
      ReplaceLoggerRoute.url( { id: this.props.match.params.id } ),
    );

  handleStartStartSaveLoggerFlow = () => {
    const { logger } = this.state;
    const validatedLogger = logger.validateAndStore( true );
    if ( this.props.company.validateLoggerId ) {
      const error = validateManufacturer( logger.loggerModel.manufacturerDisplayName, logger.unitSerialNo );
      if ( error ) {
        validatedLogger.setValidationError(
          'unitSerialNo',
          error,
        );
      }
    }
    const originalLogger = this.findLogger( this.props );
    this.setState( { validatedLogger } );
    if ( validatedLogger.isValid() && this.state.loggerChanged ) {
      this.setState( {
        loading: false,
        logger: validatedLogger,
        saveLoggerFlow: 1,
        originalLogger,
      } );
    }
    this.setState( { logger: validatedLogger } );
    return Promise.resolve();
  };

  handleCancelSaveLogger = () =>
    this.setState( { saveLoggerFlow: null, loading: false } );

  handleGenericError = ( data, header ) => {
    if ( data && data.message ) {
      this.setState( {
        genericErrorDialog: true,
        stateGenericErrorHeader: header,
        stateGenericErrorContent: data.message,
      } );
    } else {
      this.setState( { unknownErrorDialog: true } );
    }
  };

  handleCancelReplaceLogger = async () => {
    const confirmed = await this.props.confirm( {
      subject: this.props.intl.formatMessage(
        { id: 'module.loggers.subject.logger' },
        { logger: this.state.logger.unitSerialNo },
      ),
      heading: this.props.intl.formatMessage( {
        id: 'module.loggers.cancel.replace.logger.confirm.heading',
      } ),
      content: this.props.intl.formatMessage( {
        id: 'module.loggers.cancel.replace.logger.confirm.content',
      } ),
      okLabel: this.props.intl.formatMessage( { id: 'label.yes' } ),
    } );
    if ( confirmed ) {
      try {
        this.setState( { loading: true } );
        const result = await this.props.sendCancelReplaceLogger(
          this.state.logger.id,
          this.state.logger.updateIdentifier,
        );
        this.setState( { loggerChanged: false }, () => {
          this.pushResultToState( result );
        } );
      } catch ( { data, response } ) {
        if ( data && data.message === 'module.loggers.state.conflict' ) {
          this.setState( {
            loggersStateConflict: true,
            stateConflictHeader: 'module.loggers.state.conflict.cancel.heading',
          } );
        } else {
          this.handleGenericError( data, 'module.loggers.state.conflict.cancel.heading' );
        }
      } finally {
        this.setState( { loading: false } );
      }
    }
  };

  handleInactivateLogger = async () => {
    this.setState( { loading: true, deactivatingLogger: true } );
    const logger = this.findLogger( this.props );
    const confirmed = await this.props.confirm( {
      subject: this.props.intl.formatMessage(
        { id: 'module.loggers.subject.logger' },
        { logger: this.state.logger.unitSerialNo },
      ),
      heading: this.props.intl.formatMessage( {
        id: 'module.loggers.inactivate.logger.confirm.heading',
      } ),
      content: this.props.intl.formatMessage( {
        id: 'module.loggers.inactivate.logger.confirm.content',
      } ),
      okLabel: this.props.intl.formatMessage( { id: 'label.yes' } ),
    } );
    if ( confirmed ) {
      try {
        await this.props.sendDeactivateLogger(
          this.state.logger.id,
          this.state.logger.updateIdentifier,
        );
        const result = await this.props.fetchLogger( logger.id );
        this.setState(
          { inactivateLoggerFlow: null, loggerChanged: false },
          () => {
            this.pushResultToState( recordFromJs( Logger, result.logger ) );
          },
        );
      } catch ( { data, response } ) {
        if ( data && data.message === 'module.loggers.state.conflict' ) {
          this.setState( { loggersStateConflict: true } );
        } else {
          this.handleGenericError( data, 'module.loggers.inactivate.failure.trial.active.heading' );
        }
      } finally {
        this.setState( { loading: false } );
      }
    }
  };

  handleReactivateLogger = async () => {
    const confirmed = await this.props.confirm( {
      subject: this.props.intl.formatMessage(
        { id: 'module.loggers.subject.logger' },
        { logger: this.state.logger.unitSerialNo },
      ),
      heading: this.props.intl.formatMessage( {
        id: 'module.loggers.reactivate.logger.confirm.heading',
      } ),
      content: this.props.intl.formatMessage( {
        id: 'module.loggers.reactivate.logger.confirm.content',
      } ),
      okLabel: this.props.intl.formatMessage( { id: 'label.yes' } ),
    } );
    if ( confirmed ) {
      try {
        this.setState( { loading: true } );
        await this.props.sendReactivateLogger(
          this.state.logger.id,
          this.state.logger.updateIdentifier,
        );
        const result = await this.props.fetchLogger( this.state.logger.id );
        this.pushResultToState( recordFromJs( Logger, result.logger ) );
      } catch ( { data } ) {
        if ( data && data.message === 'module.loggers.state.conflict' ) {
          this.setState( { loggersStateConflict: true } );
        } else {
          this.handleGenericError( data, 'module.loggers.reactivate.logger.failure.message' );
        }
      } finally {
        this.setState( { loading: false } );
      }
    }
  };


  render() {
    const { logger, predecessorLogger } = this.state;
    if ( !this.state.initialized || !logger ) {
      // We're most likely arriving here on page load
      return (
        <FadeOut>
          <Loading fill />
        </FadeOut>
      );
    }
    const successorLogger = this.findSuccessorLogger(
      this.props,
      logger.successorLoggerId,
    );
    const breadcrumbs = [
      { location: { pathname: '/' }, label: 'TSS' },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.monitor.administration' } ) },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.loggers.logger.list.heading' } ) },
      { location: { pathname: LoggerListRoute.url() }, label: this.props.intl.formatMessage( { id: 'module.loggers.adjustment.overview' } ) },
      {
        location: { pathname: LoggerDetailsRoute.url( { id: logger.id } ) },
        label: logger.unitSerialNo,
      },
    ];

    return (
      <Fragment>
        <LoggerDetailsPage
          key="details"
          user={ this.props.user }
          logger={ logger }
          predecessorLogger={ predecessorLogger }
          successorLogger={ successorLogger }
          types={ this.props.types }
          models={ this.state.models }
          trialUnits={ this.props.trialUnits }
          typesInitialized={ this.props.typesInitialized }
          modelsInitialized={ this.props.modelsInitialized }
          trialUnitsInitialized={ this.props.trialUnitsInitialized }
          timezone={ this.props.user.timezone }
          dateTimeFormat={ this.props.user.dateTimeFormat }
          dateFormat={ this.props.user.dateFormat }
          loggerChanged={ this.state.loggerChanged }
          breadcrumbs={ breadcrumbs }
          auditLogUrl={ LoggerAuditRoute.url( { id: logger.id } ) }
          validateLoggerId={ this.props.company.validateLoggerId }
          onLoggerChange={ this.handleLoggerChange }
          onSaveLogger={ this.handleStartStartSaveLoggerFlow }
          onReplaceLogger={ this.handleReplaceLogger }
          onStartInactivateLoggerFlow={ this.handleInactivateLogger }
          onReactivateLogger={ this.handleReactivateLogger }
          onCancelReplaceLogger={ this.handleCancelReplaceLogger }
        />
        { this.state.saveLoggerFlow === 1 ? (
          <SaveLoggerDialog
            key="savedialog"
            user={ this.props.user }
            loading={ false }
            heading={ 'module.loggers.update.dialog.heading' }
            buttonLabel={ 'module.loggers.update.logger.button' }
            logger={ this.state.logger }
            types={ this.props.types }
            models={ this.props.models }
            trialUnitDiffs={ this.state.trialUnitDiffs }
            patchDiff={ this.state.patchDiff }
            onSubmit={ this.handleSaveLogger }
            onCancel={ this.handleCancelSaveLogger }
          />
        ) : null }
        { this.state.activeTrialsInactivateFailure ? (
          <MessageDialog
            error
            heading={ this.props.intl.formatMessage( {
              id: 'module.loggers.inactivate.failure.trial.active.heading',
            } ) }
            subject={ `Logger: ${ this.state.logger.unitSerialNo }` }
            onOk={ () => this.setState( { activeTrialsInactivateFailure: false } ) }
            content={ this.props.intl.formatMessage( {
              id: 'module.loggers.inactivate.failure.trial.active',
            } ) }
          />
        ) : null }
        { this.state.loggersStateConflict ? (
          <MessageDialog
            key="conflict"
            error
            heading={ this.props.intl.formatMessage( {
              id: this.state.stateConflictHeader
                ? this.state.stateConflictHeader
                : 'module.loggers.state.conflict.update.heading',
            } ) }
            onOk={ this.handleStateConflict }
            content={ this.props.intl.formatMessage( {
              id: 'module.loggers.state.conflict',
            } ) }
          />
        ) : null }
        {
          this.state.absentLoggerTypeError ? (
            <MessageDialog
              key="absentLoggerType"
              error
              heading={ this.props.intl.formatMessage( {
                id: this.state.absentLoggerTypeMessage
                  ? `${ this.state.absentLoggerTypeMessage }.heading`
                  : 'module.loggers.update.not.allowed.trialAssociation.absentLoggerType.heading',
              } ) }
              onOk={ () => this.setState( { absentLoggerTypeError: false, absentLoggerTypeMessage: null, errorTypeTrialNames: null } ) }
              content={ this.props.intl.formatMessage( {
                id: this.state.absentLoggerTypeMessage
                  ? this.state.absentLoggerTypeMessage
                  : 'module.loggers.update.not.allowed.trialAssociation.absentLoggerType',
              },
              { trialNames: this.state.errorTypeTrialNames, temperatureType: this.state.logger.clinicalLoggerTypeName } ) }
            />
          ) : null
        }
        { this.state.loggersStateNotAllowedConflict ? (
          <MessageDialog
            key="notAllowed"
            error
            heading={ this.props.intl.formatMessage( {
              id: 'module.loggers.not.allowed.heading',
            } ) }
            onOk={ () =>
              this.setState( { loggersStateNotAllowedConflict: false } )
            }
            content={ this.props.intl.formatMessage( {
              id: this.state.notAllowedMessage || 'Message not found',
            } ) }
          />
        ) : null }
        { this.state.messageNew ? (
          <MessageDialog
            key="notAllowedNew"
            error
            heading={ this.props.intl.formatMessage( {
              id: this.state.new.heading || '',
            } ) }
            onOk={ () =>
              this.props.fetchLogger( logger.id ).then( ( ) => {
                this.handleStateConflict();
                this.setState( {
                  messageNew: false },
                );
              } )
            }
            content={ this.props.intl.formatMessage( {
              id: this.state.new.content || '',
            } ) }
          />
        ) : null }
        { this.state.unknownErrorDialog ? (
          <MessageDialog
            key="unknown"
            error
            heading={ this.props.intl.formatMessage( {
              id: 'module.loggers.unknown.error.dialog.heading',
            } ) }
            onOk={ () => this.setState( { unknownErrorDialog: false } ) }
            content={ this.props.intl.formatMessage( {
              id: 'An unknown error occurred',
            } ) }
          />
        ) : null }
        { this.state.genericErrorDialog ? (
          <MessageDialog
            key="generic"
            error
            heading={ this.props.intl.formatMessage( {
              id: this.state.stateGenericErrorHeader
                ? this.state.stateGenericErrorHeader
                : 'module.loggers.unknown.error.dialog.heading',
            } ) }
            onOk={ () => this.setState( { genericErrorDialog: false } ) }
            content={ this.props.intl.formatMessage( {
              id: this.state.stateGenericErrorContent
                ? this.state.stateGenericErrorContent
                : 'An unknown error occurred',
            } ) }
          />
        ) : null }
        <Prompt
          key="prompt"
          when={ this.state.loggerChanged }
          message={ this.props.intl.formatMessage( {
            id: 'module.common.navigation.warning',
          } ) }
        />
      </Fragment>
    );
  }
}

function mapStateToProps( state ) {
  return {
    user: state.authState.user,
    company: state.companyState.company,
    loggers: state.loggerState.loggers,
    loggersInitialized: state.loggerState.loggersInitialized,
    types: state.loggerState.types,
    models: state.loggerState.models,
    trialUnits: state.trialState.trialUnits,
    trialUnitsInitialized: state.trialState.trialUnitsInitialized,
    typesInitialized: state.loggerState.typesInitialized,
    modelsInitialized: state.loggerState.modelsInitialized,
  };
}

function mapDispatchToProps( dispatch ) {
  return bindActionCreators(
    {
      fetchLogger,
      fetchLoggerTypes,
      fetchLoggerModels,
      fetchApprovedTrialUnits,
      fetchTrialUnit,
      sendUpdateLogger,
      sendDeactivateLogger,
      sendReactivateLogger,
      sendCancelReplaceLogger,
      setFlashMessage,
      fetchGlobalProbeSlots,
    },
    dispatch,
  );
}

export default connect( mapStateToProps, mapDispatchToProps )(
  withConfirmDialog( injectIntl( LoggerDetailsContainer ) ),
);
