{"module.loggers.temperature.min.or.max.or.both.input.label": "Temperature (ENTER MIN OR MAX TEMPERATURE OR BOTH)", "module.loggers.logger.elpro": "ELPRO Libero", "module.loggers.logger.berlinger": "<PERSON><PERSON>-<PERSON>", "module.loggers.logger.other": "Other", "module.loggers.logger.tss": "TSS BLE", "module.loggers.list.id": "Logger ID", "module.loggers.list.expires": "Expiry Date", "module.loggers.list.filter.fromlastupload": "From last upload", "module.loggers.list.expirydate": "Expiry Date", "module.loggers.list.description": "Description", "module.loggers.list.status": "Status", "module.loggers.list.active": "Status", "module.loggers.list.replaced": "Replaced on date", "module.loggers.list.manufacturer": "Manufacturer", "module.loggers.list.type": "Type", "module.loggers.list.trial.site": "Trial/Site", "module.loggers.list.trial": "Trial", "module.loggers.list.site": "Site", "module.loggers.list.uploaddate": "Last upload", "module.missions.list.excursion": "Excursion", "module.missions.list.upload.excursion.header": "manual excursion registration", "module.missions.list.upload.gap.header": "manual gap registration", "module.missions.list.upload.result": "Upload result", "module.missions.list.upload.result.view": "View upload result", "module.missions.list.upload.result.noResult": "N / A", "module.missions.list.upload.result.noMission": "N / A", "module.missions.list.upload.files.view": "View upload files", "module.missions.list.upload.date": "Uploaded time", "module.missions.list.uploaded.by": "Uploaded by", "module.missions.list.upload.export.csv": "CSV", "module.missions.list.upload.export": "Export mission data", "module.missions.list.last.upload": "Last upload", "module.missions.list.first.measurement": "Start", "module.missions.list.last.measurement": "End", "module.missions.list.comment": "Comment", "module.missions.list.comment.excursion": "Excursion Comment", "module.missions.list.comment.gap": "Gap Comment", "module.loggers.list.multiple.trial.site": "{count} Trial/Sites", "module.loggers.in.one.week": "In 1 week", "module.loggers.in.one.month": "In 1 month", "module.loggers.in.one.quarter": "In 3 months", "module.loggers.in.one.year": "In 1 year", "module.loggers.last.week": "Last week", "module.loggers.last.month": "Last month", "module.loggers.last.quarter": "Last 3 months", "module.loggers.last.year": "Last year", "module.loggers.subject.logger": "Logger: {logger}", "module.loggers.logger.list.heading": "Loggers", "module.loggers.logger.list.filter.button": "Filter", "module.loggers.list.site.filter.disabled.reason": "You must first select a trial", "module.loggers.logger.list.add.new.logger.button": "Add new logger", "module.loggers.add.new.logger.page.heading": "Add new logger", "module.loggers.add.new.logger.page.submit.button": "Add logger", "module.loggers.add.dialog.heading": "Add new logger", "module.loggers.add.dialog.content": "Are you sure you want to add this logger?", "module.loggers.add.validation.error.invalid.unit.serial.no": "Invalid ID", "module.loggers.duplicate.dialog.heading": "This logger is already associated with a Trial/Site!", "module.loggers.duplicate.dialog.paragraph": "Do you want to update the logger?", "module.loggers.ambiguous.trialunit.office": "A logger can't be associated to sites that are in different locations", "module.loggers.update.dialog.heading": "Are you sure you want to update this logger?", "module.loggers.update.save.dialog.removed.trial.site.heading": "This logger will no longer monitor product(s) at below trial/site, ensure new logger is associated prior to disassociation if required.", "module.loggers.update.save.dialog.removed.trial.site": "Trial/Site disassociated:", "module.loggers.update.save.dialog.added.trial.site": "Trial/Site associated:", "module.loggers.update.save.dialog.modified.trial.site": "Trial/Site modified:", "module.loggers.replace.dialog.heading": "Are you sure you want to replace this logger?", "module.loggers.replace.dialog.paragraph": "With logger: {newLogger}\nas of {date}", "module.loggers.not.allowed.heading": "Not allowed", "module.loggers.update.logger.button": "Update", "module.loggers.save.logger.button": "Save", "module.loggers.replace.logger.link": "Replace", "module.loggers.replace.logger.label": "With an existing logger", "module.loggers..replace.new.logger.label": "Or with a new logger", "module.loggers..replace.new.logger.button.label": "Create new logger", "module.loggers.cancel.replace.new.logger.button.label": "Cancel replace with new logger", "module.loggers.cancel.replace.existing.logger.button.label": "Cancel replace with existing logger", "module.loggers.inactivate.logger.link": "Inactivate", "module.loggers.replace.logger.heading": "Replace logger", "module.loggers.replace.logger.button": "Replace", "module.loggers.replace.create.logger.button": "Create & replace", "module.loggers.reaplce.logger.new.logger.radio": "New logger", "module.loggers.replace.from.date.label": "Replacement date", "module.loggers.cancel.replace.logger.button": "Cancel replacement", "module.loggers.reactivate.logger.button": "Reactivate", "module.loggers.cancel.replace.logger.confirm.heading": "Cancel logger replacement", "module.loggers.cancel.replace.logger.confirm.content": "Are you sure you want to cancel the replacement of this logger?", "module.loggers.inactivate.logger.confirm.heading": "Inactivate logger", "module.loggers.inactivate.logger.confirm.content": "Are you sure you want to inactivate this logger?", "module.loggers.cancel.replace.logger.success.message": "Logger replacement successfully canceled", "module.loggers.cancel.replace.logger.failure.message": "Logger replacement could not be canceled", "module.loggers.reactivate.logger.confirm.heading": "Reactivate logger", "module.loggers.reactivate.logger.confirm.content": "Are you sure you want to reactivate this logger?", "module.loggers.reactivate.logger.success.message": "<PERSON><PERSON> successfully reactivated", "module.loggers.reactivate.logger.failure.message": "Logger could not be reactivated", "module.loggers.details.page.heading": "Logger details", "module.loggers.details.unit.serial.label": "Logger ID", "module.loggers.details.description.label": "Name/Description", "module.loggers.details.type.label": "Type", "module.loggers.details.manufacturer.label": "Manufacturer", "module.loggers.details.Certificate.label": "Certificate", "module.loggers.details.Certificate.download.label": "Download", "module.loggers.details.expires.label": "Expiry date", "module.loggers.details.valid.from.label": "<PERSON>id from", "module.loggers.details.probe.slot.label": "Probe slot", "module.loggers.details.latest.uploaded.temp.point.timeValue.label": "Time of latest temperature data point", "module.loggers.details.latest.uploaded.temp.point.tempValue.label": "Value of latest temperature data point.", "module.loggers.details.low.temp.label": "Low", "module.loggers.details.high.temp.label": "High", "module.loggers.details.trial.site.label": "Trial / Site", "module.loggers.details.readonly.trial.site.label": "Trial / Site (Association/Disassociation date)", "module.loggers.details.audit.page.heading": "Logger audit log", "module.loggers.details.audit.page.crumb": "Audit log", "module.loggers.details.audit.page.no.entries.found": "No audit log entries found", "module.loggers.warning.expiry.heading": "This logger expires in {days, number} {days, plural, one {day} other{days}}", "module.loggers.warning.expiry.sub.day.heading": "This logger expires in less than one day", "module.loggers.warning.expiry.expired.heading": "This logger has expired", "module.loggers.warning.expiry.content": "Replace this logger with a new one.", "module.loggers.warning.expiry.is.replaced": "<PERSON><PERSON> has been replaced by logger {newLogger} as of {date}", "module.loggers.warning.will.replace": "<PERSON><PERSON> will replace {<PERSON><PERSON><PERSON><PERSON>} as of {date}", "module.loggers.warning.has.replaced": "<PERSON><PERSON> has replaced {<PERSON><PERSON><PERSON><PERSON>} as of {date}", "module.loggers.warning.expiry.will.replaced": "Logger will be replaced by logger {newLogger} as of {date}", "module.loggers.warning.is.discontinued": "Logger has been inactivated as of {date}", "module.loggers.warning.logger.with.no.access": "a logger you do not have access to", "module.loggers.warning.batch.adjustment.batch.number.exists": "The batch for this adjustment has arrived, adjustment will be applied to all  Kits (DUNs) arriving after this approval", "module.loggers.inactivate.dialog.heading": "Are you sure you want to inactivate this logger?", "module.loggers.inactivate.dialog.paragraph": "Enter your password to confirm that the logger will no longer be in use.", "module.loggers.inactivate.dialog.inactivate.button": "Inactivate", "module.loggers.inactivate.failure.dialog.heading": "The logger can not be inactivated", "module.loggers.inactivate.failure.dialog.paragraph": "Since the logger belongs to a running trial, you need to replace with a new.", "module.loggers.inactivate.failure.dialog.replace.button": "Replace", "module.loggers.update.not.allowed.activeTrialNotCovered": "Trial / Site not covered by other logger", "module.loggers.update.not.allowed.alreadyReplaced": "<PERSON><PERSON> already replaced", "module.loggers.update.not.allowed.scheduledToReplace": "<PERSON><PERSON> already scheduled to replace another logger", "module.loggers.update.not.allowed.typeChangeAfterUpload": "Type can not be changed after upload has been done", "module.loggers.update.not.allowed.validFromDateChangeAfterUpload": "Association date can not be changed after upload has been done", "module.loggers.inactivate.failure.trial.active.heading": "Could not inactivate logger", "module.loggers.inactivate.failure.trial.active": "Logger still has active trials associated with it", "module.loggers.loading.trial.units": "Loading trial units...", "module.loggers.loading.logger.manufacturer": "Loading logger manufacturer...", "module.loggers.loading.logger.type": "Loading logger type...", "module.loggers.associated.to.active.trial": "Can't inactivate a logger when it's connected to an active trial", "module.trial.mngmt.submit.failed.dialog.heading.submitDraft": "Failed to send for review", "module.trial.invalid.status": "An unexpected error occurred preventing this trial to change status", "module.loggers.duplicate.logger": "A logger with this serial number and manufacturer already exists", "module.loggers.conflict": "A logger with this serial number and manufacturer already exists", "module.loggers.association.added.on": "Trial / Site associated on {date}", "module.loggers.association.removed.on": "Trial / Site disassociated on {date}", "module.loggers.future.association.added.on": "Trial / Site will be associated on {date}", "module.loggers.future.disassociation.removed.on": "Trial / Site will be disassociated on {date}", "'module.loggers.association.not.found'": "Association status not found", "module.loggers.trial.site.association.heading": "Select association date for", "module.loggers.trial.site.disassociation.heading": "Select disassociation date for", "module.loggers.trial.site.association.detail": "Trial / Site: {trialSite}", "module.loggers.trial.site.association.selected": "New association date", "module.loggers.trial.site.disassociation.selected": "New disassociation date", "module.loggers.disassociation.in.future": "Disassociation must not be in the future", "module.loggers.too.early.disassociation": "Disassociation date is too early", "module.loggers.association.too.early": "Association date is too early", "module.loggers.no.open.associations.to.disassociate": "No open associations to disassociate", "module.loggers.modifying.replacement.associations": "Association date cannot be changed for inherited trial / site", "module.loggers.moving.affected.associations": "Association date before last upload cannot be modified", "module.loggers.update.not.allowed.validFromDateChangeAfterAssociation": "Unexpected error prevent association date being changed", "module.loggers.upload.file.type.error.heading": "Incorrect file", "module.loggers.upload.file.type.error.content": "File must be of pdf-, jpg- or png-format", "module.loggers.upload.shipments.list.heading": "Shipments", "module.loggers.upload.shipmentpending.list.heading": "Pending Shipments", "module.loggers.upload.shipmentpending.list.search..placeholder": "Shipment number", "module.loggers.upload.shipmentpending.list.search.exact.match.lead.text": "You have { numPendingShipments } pending shipments. Please enter the full shipment number in the search box below to perform a shipment upload", "module.loggers.upload.shipmentpending.list.search.exact.match.no.match": "This shipment number cannot be found, please check your input", "module.loggers.upload.shipmentpending.list.search.exact.match.multiple.match": "This shipment number exists on multiple trials, please select the shipment of the correct trial below", "module.loggers.upload.shipmentpending.list.search.exact.match.only.digits": "Only digits are allowed, please check your input", "module.loggers.upload.shipmentrepository.list.heading": "Shipment Repository", "module.loggers.loggers.shipment.repository.detail.heading": "Shipment Details", "module.loggers.loggers.shipment.repository.detail.shipmentNo": "Shipment number", "module.loggers.loggers.shipment.repository.detail.trail": "Trial / Site", "module.loggers.loggers.shipment.repository.detail.products": "Products", "module.loggers.loggers.shipment.repository.detail.uploadLink": "Additional upload", "module.loggers.shipment.repository.detail.view.chart": "View chart", "module.loggers.upload.shipments.list.shipment.number": "Shipment number", "module.loggers.upload.shipments.list.trial.site": "Trial/Site", "module.loggers.upload.shipments.list.products": "Products", "module.loggers.upload.shipments.list.files": "Files", "module.loggers.upload.shipments.detail.list.files": "FILE ( FILE TYPE )", "module.loggers.upload.shipments.list.status": "Status", "module.loggers.upload.shipments.status.pending": "Pending", "module.loggers.upload.shipments.status.processed": "Processed", "module.loggers.upload.shipments.disabled.reason": "This trial is inactive, no additional documents can be uploaded to the trial.", "module.loggers.upload.shipments.fileType.UNKNOWN": "N / A", "module.loggers.upload.shipments.fileType.LOGGER_STORAGE_DATA": "L", "module.loggers.upload.shipments.fileType.LOGGER_SHIPMENT_DATA": "L", "module.loggers.upload.shipments.fileType.LOGGER_ADDITIONAL": "A", "module.loggers.upload.shipments.fileType.ADJUSTMENT_ATTACHMENT": "ADJ", "module.loggers.upload.shipments.hooverFileType.UNKNOWN": "File type not available", "module.loggers.upload.shipments.hooverFileType.LOGGER_STORAGE_DATA": "<PERSON><PERSON>", "module.loggers.upload.shipments.hooverFileType.LOGGER_SHIPMENT_DATA": "<PERSON><PERSON>", "module.loggers.upload.shipments.hooverFileType.LOGGER_ADDITIONAL": "Additional", "module.loggers.upload.shipments.hooverFileType.ADJUSTMENT_ATTACHMENT": "Adjustment attachment", "module.loggers.upload.data.for": "Upload data for", "module.loggers.upload.storages": "Storage", "module.loggers.upload.shipments": "Shipment", "module.loggers.upload.add.new.logger": "Upload logger data file(s) from all loggers", "module.loggers.upload.add.new.logger.note.1": "Note there can be multiple loggers for one shipment!", "module.loggers.upload.add.new.logger.note.2": "Upload all before confirming.", "module.loggers.upload.upload.logger.data.for": "Upload logger data file for {subject}", "module.loggers.upload.browse.button.label": "Browse", "module.loggers.upload.shipment.instructions": "Drag and drop your logger data file(s) here", "module.loggers.upload.shipment.instructions.or": "or", "module.loggers.upload.select.batches.for.this.logger": "Select product(s) for logger", "module.loggers.upload.selected.batches.for.this.logger": "Selected product(s) for logger", "module.loggers.upload.selected.batches.system.selected": "Products for this shipment is automatically selected by the system and cannot be changed", "module.loggers.upload.available.batches.for.this.shipment": "Available product(s)", "module.loggers.upload.no.available.batches.for.this.shipment": "No product available", "module.loggers.upload.file.added": "File added", "module.loggers.upload.file.added.successfully": "File added successfully", "module.loggers.upload.logger.with.id": "Logger {id}", "module.loggers.upload.status.processing": "Processing...", "module.loggers.upload.status.failed": "Failed", "module.loggers.upload.failed.to.process": "Failed to process file", "module.loggers.upload.remove.file": "Please remove and try again. If the problem persists, contact support.", "module.loggers.upload.step.1.2": "Step 1/2", "module.loggers.upload.step.2.2": "Step 2/2", "module.loggers.upload.upload.additional.documents": "Upload additional documents?", "module.loggers.upload.upload.additional.instructions": "Drag and drop an additional file here", "module.loggers.upload.remove.logger.button.label": "Remove", "module.loggers.upload.download.logger.file.button.label": "View", "module.loggers.upload.modify.batches.for.this.logger.label": "Change products", "module.loggers.upload.view.batches.for.this.logger.label": "View products", "module.loggers.upload.list.heading": "This is an already processed shipment. Shipment details are available in the shipment repository.", "module.loggers.upload.unknown.error.dialog.heading": "Unknown error", "module.loggers.upload.unknown.error.dialog.content": "An unknown error occurred", "module.loggers.upload.wrong.logger.type.error.dialog.content": "Go to the Logger Overview page and ensure that a logger of the correct type {tempType} is associated to the site with an association date before the shipment arrival date.", "module.loggers.upload.no.existing.mission.data.heading": "Shipment Upload Error", "module.loggers.upload.no.existing.mission.data.content": "Logger data file not possible to upload for this shipment, please select Additional files upload.", "module.loggers.upload.error.dialog.heading": "Shipment upload error", "module.loggers.upload.cannot.fetch.shipments": "Server error - could not fetch shipments", "module.loggers.state.not.allowed.activeTrialNotCovered": "Can't disassociate logger from an active trial/site if there are no other loggers associated to them", "module.loggers.state.not.allowed.alreadyReplaced": "Can't edit a logger if it's been replaced", "module.loggers.state.not.allowed.scheduledToReplace": "Can't edit a logger scheduled to replace another logger before the replacement has taken place", "module.loggers.upload.exFromDate.equalsToDate.heading": "Illegal time period", "module.loggers.upload.exFromDate.equalsToDate.content": "Excursion end date must be after the start date.", "module.loggers.upload.shipment.ambiguous.heading": "Incorrect shipment file", "module.loggers.upload.shipment.ambiguous.content": "This logger file belongs to another shipment.", "module.loggers.upload.shipment.ambiguous.short": "This logger file belongs to another shipment.", "module.loggers.upload.delete.fail.dialog.heading": "Failed to remove logger!", "module.loggers.upload.delete.fail.dialog.content": "The logger could not be removed. Please try again. If problem persists, please restart the entire upload process", "module.loggers.upload.bad.file.dialog.heading": "Incorrect file", "module.loggers.upload.bad.file.dialog.content": "The file you tried to upload is not supported", "module.loggers.upload.bad.group.dialog.heading": "Upload session expired!", "module.loggers.upload.bad.group.dialog.content": "Too much time has elapsed since your last upload. You need to start over.", "module.loggers.upload.bad.shipment.dialog.heading": "Shipment not found", "module.loggers.upload.bad.shipment.dialog.content": "The shipment cannot be found, or you do not have access to it.", "module.loggers.upload.shipment.no.found.dialog.heading": "Shipment not found!", "module.loggers.upload.shipment.no.found.dialog.content": "This logger has not been registered on a shipment. Please select the shipment manually under Pending Shipments.", "module.loggers.upload.shipment.no.found.dialog.message": "Logger: {additional}", "module.loggers.upload.missing.some.launched.dialog.heading": "Missing expected loggers!", "module.loggers.upload.missing.some.launched.dialog.content": "Not all launched loggers of this shipment are included. Please upload the file(s) of the missed logger(s).", "module.loggers.upload.missing.some.launched.dialog.message": "Missed logger(s): {additional}", "module.loggers.upload.bad.confirm.dialog.heading": "Unknown error", "module.loggers.upload.bad.confirm.dialog.content": "An unknown error occurred.", "module.loggers.upload.shipment.trialunit.noStorageLoggerType.heading": "Upload failed", "module.loggers.upload.shipment.trialunit.noStorageLoggerType.content": "No storage logger of correct type is associated to this trial/site", "module.loggers.upload.shipment.trialunit.noStorageLoggerType.message": "{additional}", "module.loggers.upload.missionDuplicateError.heading": "Logger file(s) already uploaded", "module.loggers.upload.missionDuplicateError.content": "The following logger file(s) were previously uploaded and cannot be uploaded again.", "module.loggers.upload.toDate.inFuture.heading": "Illegal time period", "module.loggers.upload.toDate.inFuture.content": "\"To date\" must not be in the future. Your computer's clock seems to be drifting. Current server time is {0} - adjust your clock accordingly.", "module.loggers.upload.fromDate.beforeStoredDate.heading": "Illegal time period", "module.loggers.upload.fromDate.beforeStoredDate.content": "\"From date\" must no be set before the last upload for this logger. Please make sure your computer's clock is accurately configured", "module.loggers.upload.fromDate.equalsToDate.heading": "Illegal time period", "module.loggers.upload.fromDate.equalsToDate.content": "Excursion end date must be after the start date", "module.loggers.upload.fromDate.gap.warning.heading": "This will trigger a gap in the continuous temperature data", "module.loggers.upload.fromDate.gap.warning.content": "Data previously uploaded until", "module.loggers.upload.no.new.data.heading": "Upload failed", "module.loggers.upload.no.new.data.content": "File contains no new temperature data.", "module.loggers.upload.save.batches.saving": "Saving...", "module.loggers.upload.save.batches.saving.failed": "Failed to save!", "module.loggers.upload.save.batches.saving.success": "Saved!", "module.loggers.upload.confirm.shipment.dialog.heading": "Have you registered all logger file(s) for shipment: {shipmentNo}?", "module.loggers.upload.confirm.processed.shipment.dialog.heading": "Have you registered all file(s) for this shipment: {shipmentNo}?", "module.loggers.upload.confirm.shipment.dialog.paragraph": "Please verify that the number of logger file(s) for this shipment is {count}.", "module.loggers.upload.confirm.processed.shipment.dialog.paragraph": "Please verify that the number of additional logger file(s) for this shipment is {count}.", "module.loggers.upload.confirm.shipment.dialog.warning.paragraph": "All products have not been selected, are you sure you wish to proceed?", "module.loggers.upload.confirm.storage.dialog.heading": "Commit uploaded file(s)?", "module.loggers.upload.confirm.storage.dialog.paragraph": "Are you ready to commit these files?", "module.loggers.upload.confirm.dialog.submit.button": "Yes, confirm {count, number} {count, plural, one {logger} other{loggers}}", "module.loggers.upload.confirm.dialog.associated.paragraph": "For logger { unitSerialNo } associated to:", "module.loggers.upload.only.opaque.confirm.dialog.submit.button": "Yes", "module.loggers.upload.confirm.dialog.cancel.button": "No, I want to change this", "module.loggers.upload.status.success.heading": "Products are OK to use", "module.loggers.upload.status.success.shipment.trial": "Shipment #{shipmentId} | {trialId}", "module.loggers.upload.status.success.ok.button": "Ok", "module.loggers.loggers.in.shipment.heading": "Shipment Upload", "module.loggers.upload.shipment.button.processed": "Move to Processed", "module.loggers.upload.shipment.button.activated": "Manually Activate", "module.loggers.upload.shipment.modal.processed.header": "Move kits to processed", "module.loggers.upload.shipment.modal.processed.text": "Are you sure you want to move the shipment to the repository without activating the kits?", "module.loggers.upload.shipment.date.activated.placeholder": "Select a Date and Time", "module.loggers.upload.shipment.date.error.message": "Please select a date before proceeding", "module.loggers.upload.shipment.date.typing.error.message": "Please select a date and time from the calendar using the mouse", "module.loggers.upload.shipment.modal.activated.header": "Select Kits Activation Date", "module.loggers.upload.shipment.modal.activated.text": "By selecting the activation date, all kits in this shipment will be set to activate on that chosen date.", "module.loggers.upload.shipment.modal.activated.confirm.header": "Activate Kits ", "module.loggers.upload.shipment.modal.activated.confirm.text": "Are you certain you want to manually activate all kits starting from {date}, and relocate this shipment to the repository?", "module.loggers.loggers.in.shipment.info": "This shipment is associated to trial/site: {trialName} / {siteNumber}", "module.loggers.products.in.shipment.info": "Products in shipment:", "module.loggers.upload.upload.label": "Upload", "module.loggers.upload.shipments.label": "Shipment", "module.loggers.upload.shipments.pending.label": "Pending", "module.loggers.upload.shipments.launched.label": "Launched", "module.loggers.upload.shipments.repository.label": "Repository", "module.loggers.upload.logger.label": "Shipment", "module.loggers.shipment.shipment.no.label": "Shipment #{shipmentNo}", "module.loggers.shipment.no.shipments.found": "No shipments found", "module.loggers.shipment.too.early.data.heading": "Upload failed", "module.loggers.shipment.too.early.data.content": "The file contains data for a period which cannot be associated to this shipment.", "module.loggers.status.inactive": "Inactive", "module.loggers.status.expired": "Expired", "module.loggers.status.active": "Active", "module.loggers.upload.shipment.trialunit.noStorageLoggers.heading": "Upload failed", "module.loggers.upload.shipment.trialunit.noStorageLoggers.content": "No storage logger associated for trial/site.", "module.dun.state.conflict.heading": " Kit (DUN) status conflict", "module.dun.state.conflict.content": " Kits (DUNs) on this site is currently being updated by another source. Please try again after a few seconds.", "module.loggers.upload.shipment.missingExpectedLoggers.heading": "Upload failed", "module.loggers.upload.shipment.missingExpectedLoggers.content": "Missing loggers that was expected to come with this shipment", "module.loggers.upload.upload.additional.documents.virus.disclaimer": "ATTENTION! Uploaded files will not be submitted to virus scans.", "module.loggers.upload.no.loggers.added.error": "No loggers uploaded yet", "module.loggers.upload.missing.some.launched.error": "Missing expected logger {missedLoggers}", "module.loggers.upload.all.batches.not.covered.error": "Please remember to click \"Save\".", "module.loggers.upload.no.batches.selected.error": "No product selected", "module.loggers.upload.file.has.failed.error": "One or more files have errors", "module.loggers.upload.processed.no.files.added.error": "No files uploaded yet", "module.loggers.upload.no.document.added.error": "No documents uploaded yet", "module.loggers.upload.shipment.status.quarantine.heading": "Quarantine products", "module.loggers.upload.shipment.status.quarantine.instructions": "Please put the affected products in quarantine and await further instructions.", "module.loggers.upload.shipment.status.profile.header": "Profile", "module.loggers.upload.shipment.status.dun.id.header": "Kit ID (DUN)", "module.loggers.upload.shipment.status.status.header": "Status", "module.loggers.upload.storage.label": "Storage", "module.loggers.upload.storage.manual.label": "Manual", "module.loggers.upload.storage.repository.label": "Repository", "module.loggers.loggers.in.storage.logger": "<PERSON><PERSON>", "module.loggers.loggers.in.storage.heading": "Loggers in storage", "module.loggers.upload.storage.logger.heading": "Storage logger upload", "module.loggers.upload.generic.file.heading": "Additional files upload", "module.loggers.upload.shipment.generic.file.heading": "Other shipment documentation upload", "module.loggers.upload.shipment.generic.instructions": "Drag and drop your other file(s) here", "module.loggers.upload.storage.instructions": "Drag and drop your logger data file here", "module.loggers.upload.generic.instructions": "Drag and drop your additional files here", "module.loggers.upload.no.files.selected.for.upload": "No files selected for upload", "module.loggers.upload.old.data.heading": "Upload failed", "module.loggers.upload.old.data.content": "The file contains data for a period before the logger's valid from date", "module.loggers.upload.create.unregistered.logger": "Logger not registered", "module.loggers.upload.create.unregistered.logger.now": "Register this logger now", "module.loggers.upload.badFileSizeError.heading": "Incorrect file", "module.loggers.upload.badFileSizeError.content": "The file you tried to upload is not supported", "module.loggers.upload.badFileSizeError.short": "File size out of range", "module.loggers.upload.fileSuccessMessage.short": "File ok", "module.loggers.upload.badFileTypeError.heading": "Incorrect file", "module.loggers.upload.badFileTypeError.content": "The file you just tried to upload is not supported and is of the wrong type", "module.loggers.upload.badFileTypeError.short": "Unsupported file type", "module.loggers.upload.badFileTypeError.info": "File must be of {fileTypes}", "module.loggers.upload.loggerDeviceTypeUnknown.heading": "Unknown logger type", "module.loggers.upload.loggerDeviceTypeUnknown.content": "The make of the logger upload is known but the type of logger is unknown and not supported.", "module.loggers.upload.loggerDeviceTypeUnknown.short": "Unknown logger type", "module.loggers.upload.loggerTypeNotExpected.heading": "Wrong type of logger uploaded", "module.loggers.upload.loggerTypeNotExpected.content": "You have uploaded a storage logger when you should have uploaded a shipment logger, or vice versa.", "module.loggers.upload.loggerTypeNotExpected.short": "Wrong type of logger", "module.loggers.upload.loggerModelUploadDisabled.short": "Upload for this logger model is not allowed.", "module.loggers.upload.loggerDuplicateError": "Duplicate of previously uploaded logger", "module.loggers.upload.loggerDuplicateError.short": "Duplicate of previously uploaded logger", "module.loggers.upload.dbWriteError": "Upload failed", "module.loggers.upload.dbWriteError.short": "Upload failed", "module.loggers.upload.missionDuplicateError": "Logger file already uploaded", "module.loggers.upload.missionDuplicateError.short": "Logger file already uploaded", "module.loggers.upload.conversionError": "File not recognized", "module.loggers.upload.conversionError.short": "File not recognized", "module.loggers.upload.loggerNotRegistered.short": "Unregistered logger", "module.loggers.upload.loggerTvnIdInvalid.short": "Invalid logger", "module.loggers.upload.uploadTimeoutError.short": "Invalid file", "module.loggers.upload.uploadError.short": "Upload failed", "module.loggers.upload.manual.label": "Manual", "module.loggers.upload.manual.logger.list.heading": "Upload logger data for:", "module.loggers.upload.manual.logger.data.heading": "Upload logger data for period:", "module.loggers.upload.manual.file.form.heading": "Temperature data file upload", "module.loggers.upload.manual.instructions": "Drag and drop temperature data file here ", "module.loggers.upload.manual.date.from.label": "From", "module.loggers.upload.manual.date.to.label": "To", "module.loggers.upload.manual.temp.in.range.desc": "Was the temperature in range for the entire period?", "module.loggers.upload.manual.gap.desc": "Was there any gap(s)?", "module.loggers.upload.manual.excursion.index": "Excursion details", "module.loggers.upload.manual.gap.index": "Gap details", "module.loggers.upload.manual.gap.add.disable.reason": "Start date and end date must be set", "module.loggers.upload.manual.start.date.label": "Start date", "module.loggers.upload.manual.end.date.label": "End date", "module.loggers.upload.manual.temperature.c": "<PERSON><PERSON><PERSON>", "module.loggers.upload.manual.temperature.f": "Fahrenheit", "module.loggers.upload.manual.temperature.min": "Min", "module.loggers.upload.manual.temperature.max": "Max", "module.loggers.upload.manual.excursion.comment": "Comment (optional)", "module.loggers.upload.manual.add.excursion": "Add excursion", "module.loggers.upload.manual.excursion.dates.out.of.range.heading": "Dates out of range", "module.loggers.upload.manual.excursion.dates.out.of.range.content": "The start date and/or end date of one or several excursions are out of range. These dates have been reset and need to be set again.", "module.loggers.upload.manual.confirm.message": "Click \"Confirm\" to confirm that the uploaded temperature data has been reviewed and that the entered data is correct. ", "module.loggers.upload.manual.validation.error.missing.from.date": "Period from date must be set", "module.loggers.upload.manual.validation.error.missing.to.date": "Period to date must be set", "module.loggers.upload.manual.validation.error.same.date": "The upload to date must be after upload from date", "module.loggers.upload.manual.validation.error.missing.temp.in.range": "Temperature in range must be set", "module.loggers.upload.manual.validation.error.missing.gap": "Gap must be set", "module.loggers.upload.manual.validation.error.missing.excursions": "At least one excursion must be recorded if temperature was out of range", "module.loggers.upload.manual.validation.error.missing.gaps": "At least one gap must be recorded if having gap", "module.loggers.upload.manual.validation.error.missing.excursion.missing.data": "Excursion missing mandatory data", "module.loggers.upload.manual.validation.error.missing.gap.missing.data": "Gap missing mandatory data", "module.loggers.upload.manual.validation.error.missing.data.file": "At least one temperature data file must be uploaded", "module.loggers.upload.manual.validation.error.excursion.time.to.short": "Excursions below { minExcursionTime } minutes should not be reported", "module.loggers.upload.manual.validation.error.gap.time.to.short": "Gap 0 minute should not be reported", "module.loggers.upload.manual.validation.error.excursion.gap.time.overlap": "Excursions/Gaps must not have overlapping times", "module.loggers.upload.manual.excursions.tooSmall.heading": "Manual upload failed.", "module.loggers.upload.manual.excursions.tooSmall.content": "Excursion interval is too short.", "module.loggers.upload.fromDate.beforeLoggerValidFromDate.heading": "Manual upload failed.", "module.loggers.upload.fromDate.beforeLoggerValidFromDate.content": "Entered data was before logger association date.", "module.loggers.upload.result.upload.successful": "Upload successful", "module.loggers.upload.exOverlapping.heading": "Manual upload failed", "module.loggers.upload.exOverlapping.content": "Excursions/Gaps must not have overlapping times", "module.loggers.upload.result.upload.view.pdf": "View upload result", "module.loggers.upload.result.upload.expand": "Expand all", "module.loggers.upload.result.upload.collapse": "Collapse all", "module.loggers.upload.result.no.data": "Result not available", "module.loggers.upload.result.upload.intervening.missions.calculated": "Please note that storage data also has been applied to this upload.", "module.loggers.upload.result.excursions.detected": "Excursions detected, no action required", "module.loggers.upload.result.excursions.detected.ongoing": "Excursions detected", "module.loggers.upload.result.deviations.detected": "Deviations have been detected { message  }", "module.loggers.upload.result.deviations.detected.temperature": "Temperature deviations have been detected { message  }", "module.loggers.upload.result.deviations.detected.gap": "Gap deviations have been detected { message  }", "module.loggers.upload.result.deviations.detected.temperature.gap": "Temperature and gap deviations have been detected { message  } ", "module.loggers.upload.result.excursions.detected.ongoing.temperature": "Ongoing temperature excursion, the last measured temperature is outside the allowed temperature range. Please check storage conditions and make sure that the temperature is back in the allowed range and upload temperature data again. Do not dispense any trial product before action is taken. ", "module.loggers.upload.result.excursions.detected.ongoing.Gap": "There is an ongoing gap at the end of the uploaded logger data, please investigate and upload temperature data again.", "module.loggers.upload.result.deviations.add.comment": "Click here to enter a deviation comment and next dispensing visit date if applicable.", "module.loggers.upload.result.no.excursions.detected": "No excursions detected", "module.loggers.upload.result.logger.files": "Uploaded files: ", "module.loggers.upload.result.no.duns.affected": "Please note that there are no Kits (DUNs) at site, therefore the system is not concluding on the uploaded data.", "module.loggers.upload.result.trial.status.site": "Trial / Site", "module.loggers.upload.shipment.intervening.storageUpload.heading": "Upload failed", "module.loggers.upload.shipment.intervening.storageUpload.content": "This shipment cannot be uploaded, associated storage logger(s) have already been uploaded.", "module.deviation.batch.already.exists.heading": "Batch exists", "module.deviation.batch.already.exists.content": "This batch can not be pre adjusted since it is already available in the system", "module.deviation.invalid.adjustment.invalid.gap.closure.heading": "Failed to send for review", "module.deviation.invalid.adjustment.invalid.gap.closure.content": "Gap is not closed correctly", "module.deviation.invalid.adjustment.openEndedInterval.reset.invalid.content": "The reset of the open-ended interval was invalid", "module.deviation.invalid.adjustment.openEndedInterval.reset.invalid.heading": "Invalid reset", "module.deviation.invalid.adjustment.openEndedInterval.revertReset.invalid.content": "The revert of the original reset of the open-ended interval was invalid", "module.deviation.invalid.adjustment.openEndedInterval.revertReset.invalid.heading": "Invalid revert reset", "module.deviation.invalid.adjustment.postAdj.notAllowed.heading": "Deviation already adjusted", "module.deviation.invalid.adjustment.postAdj.notAllowed.content": "This deviation has already undergone re-evaluation", "module.loggers.upload.result.product.table.product.name": "Product name", "module.loggers.upload.result.product.table.product.conclusion": "Conclusion", "module.loggers.upload.result.product.table.product.conclusion.all.failed": "Quarantine - All Kits (DUNs)", "module.loggers.upload.result.product.table.product.conclusion.some.failed": "Some OK - Some NOT OK", "module.loggers.upload.result.product.table.product.conclusion.unknown.failed": "Some Unknown - Some NOT OK", "module.loggers.upload.result.product.table.product.conclusion.no.failed": "OK - All Kits (DUNs)", "module.loggers.upload.result.product.table.view.duns": "View Kits (DUNs)", "module.loggers.upload.result.batch.table.batchnr": "Batch No.", "module.loggers.upload.result.batch.table.dun": "Kit ID (DUN)", "module.loggers.upload.result.batch.table.shipmentnumber": "Shipment No.", "module.loggers.upload.result.batch.table.conclusion": "Conclusion", "module.loggers.upload.result.batch.table.conclusion.ok": "OK", "module.loggers.upload.result.batch.table.conclusion.ongoing": "Unknown - Incomplete temperature data", "module.loggers.upload.result.batch.table.conclusion.not.active.ongoing.dispensed": "Unknown – Incomplete temperature data - Kit (DUN) Dispensed", "module.loggers.upload.result.batch.table.conclusion.not.ok": "Quarantine product - await manual deviation handling", "module.loggers.upload.result.batch.table.conclusion.not.active.not.ok": "Kit (DUN) Dispensed/Damaged - await manual deviation handling", "module.loggers.upload.result.batch.table.conclusion.not.active.not.ok.dispensed.action": "Kit (DUN) Dispensed - take immediate action", "module.loggers.upload.result.batch.table.conclusion.not.active.not.ok.dispensed": "Kit (DUN) Dispensed - await manual deviation handling", "module.loggers.upload.result.batch.table.conclusion.not.active.not.ok.damaged": "Kit (DUN) Damaged - await manual deviation handling", "module.loggers.upload.result.batch.table.conclusion.not.active.damaged": "Kit (DUN) Damaged", "module.loggers.upload.result.batch.table.conclusion.not.active.ok": "OK - Kit (DUN) Dispensed/Damaged", "module.loggers.upload.result.batch.table.conclusion.not.active.ongoing.ok": "Unknown - Incomplete temperature data - Kit (DUN) Dispensed/Damaged", "module.loggers.upload.result.batch.table.conclusion.not.active.ok.damaged": "OK - Kit (DUN) Damaged", "module.loggers.upload.result.batch.table.conclusion.not.active.ok.dispensed": "OK - Kit (DUN) Dispensed", "module.loggers.upload.result.product.table.product.print.overview": "Print this overview", "module.loggers.upload.result.product.table.product.close.overview": "Close overview", "module.loggers.upload.repository.chart.header": "Chart", "module.loggers.upload.repository.chart.no.temperature.data.for.mission": "Mission contains no temperature data", "module.loggers.upload.repository.view.in.chart": "View in chart", "module.loggers.upload.repository.list.header": "Repository", "module.loggers.upload.repository.list.missions.header": "Uploads", "module.loggers.upload.repository.upload.crumb": "Additional files", "module.loggers.upload.repository.no.files.added.error": "No files uploaded yet", "module.loggers.upload.repository.delete.fail.dialog.heading": "Failed to remove file!", "module.loggers.upload.repository.delete.fail.dialog.content": "The file could not be removed. Please try again. If problem persists, please restart the entire upload process", "module.loggers.nested.data.selector.no.data.message": "No data found", "module.loggers.deviation.list.heading": "Deviations", "module.loggers.deviations.list.trial": "Trial", "module.loggers.deviations.list.site": "Site", "module.loggers.deviations.list.trial.site": "Trial / Site", "module.loggers.deviations.list.presentation.id": "ID", "module.loggers.deviations.list.prioritisation.date": "Prioritisation Date", "module.loggers.deviations.list.updated.by": "Latest updated by", "module.loggers.deviations.list.type": "Type", "module.loggers.deviations.list.status": "Status", "module.loggers.deviations.list.deviationdate": "Date", "module.loggers.deviations.list.site.filter.disabled.reason": "Select a trial first", "module.loggers.deviations.list.type.temperature": "Temperature", "module.loggers.deviations.list.type.gap": "Gap", "module.loggers.deviation.details.heading": "Deviation details", "module.loggers.deviation.blocked.info": "Another deviation is blocking this deviation from being adjusted. Click here to adjust that deviation.", "module.loggers.manual.adjustment.page.heading": "Adjustments", "module.loggers.manual.adjustment.adjustment.no": "Adjustment #{index}", "module.loggers.manual.adjustment.selector.shipmentid": "Shipment ID", "module.loggers.manual.adjustment.selector.profileid": "Profile ID", "module.loggers.manual.adjustment.selector.batchid": "Batch ID", "module.loggers.manual.adjustment.selector.dunid": "Kit ID (DUN)", "module.loggers.manual.adjustment.add.new.heading": "Add new manual adjustment", "module.loggers.manual.adjustment.dun.count": "Selected Kits (DUNs) {count, number} / {totalCount, number}", "module.loggers.manual.adjustment.stability.interval.duration": "{sign}{days, number} d {hours, number} h {minutes, number} m", "module.loggers.manual.adjustment.stability.interval.unlimited.duration": "Unlimited (Cumulative)", "module.loggers.manual.adjustment.reset.interval": "({sign}{days, number} d {hours, number} h {minutes, number} m)", "module.loggers.manual.adjustment.profile.alarm.type.consecutive": "(Single)", "module.loggers.manual.adjustment.profile.alarm.type.cumulative": "(Cumulative)", "module.loggers.manual.adjustment.profile.unlimited": "Unlimited", "module.loggers.manual.adjustment.stability.interval.time.label": "Time adjustment", "module.loggers.manual.adjustment.stability.interval.reset.label": "Reset", "module.loggers.manual.adjustment.stability.interval.allowed.time": "Allowed total time", "module.loggers.manual.adjustment.stability.interval.remaining.time": "Remaining time", "module.loggers.adjustment.details.heading": "Adjustment details", "module.loggers.adjustment.details.reevaluated.heading": "Post adjustment details", "module.loggers.adjustment.details.previous.heading": "Original adjustment details", "module.loggers.adjustment.details.status.date.label": "Status date", "module.loggers.manual.adjustment.create.type.pre.adjustment": "Create new pre adjustment", "module.loggers.manual.adjustment.create.type.batch.pre.adjustment": "Pre - Batch adjustment", "module.loggers.manual.adjustment.type.pre_batch": "Pre - Batch", "module.loggers.manual.adjustment.create.type.dun.pre.adjustment": "Pre - Kit (DUN) adjustment", "module.loggers.manual.adjustment.type.pre_dun": "Pre - Kit (DUN)", "module.loggers.manual.adjustment.create.type.manual.adjustment": "Manual adjustment", "module.loggers.manual.adjustment.create.type.logger.adjustment": "Logger adjustment", "module.loggers.manual.adjustment.type.logger": "<PERSON><PERSON>", "module.loggers.manual.adjustment.type.manual": "Manual", "module.loggers.manual.adjustment.type.deviation_manual": "Temperature", "module.loggers.manual.adjustment.type.deviation_gap": "Gap", "module.loggers.manual.adjustment.type.deviation_post_manual": "Temperature", "module.loggers.manual.adjustment.type.deviation_post_gap": "Gap", "module.loggers.manual.adjustment.add.adjustment.button.label": "Add another adjustment", "module.loggers.manual.adjustment.auto.close.gap.button.label": "Auto fill gaps", "module.loggers.manual.adjustment.cancel.auto.close.gap.button.label": "Cancel auto fill gaps", "module.loggers.manual.adjustment.auto.close.gap.dialog.subject": "Deviation: { deviation }", "module.loggers.manual.adjustment.auto.close.gap.dialog.heading": "Auto fill gaps?", "module.loggers.manual.adjustment.auto.close.gap.dialog.content": "Previous adjustments will be removed.", "validation.error.temperatureAdjustments.no.value": "Temperature is required", "validation.error.temperatureAdjustments.temperature.min.or.max.value": "Min, max or both temperature is required", "validation.error.temperatureAdjustments.duplicate.value": "Duplicate temperature adjustment is not allowed", "validation.error.temperatureAdjustments.no.time": "Time is required", "validation.error.temperatureAdjustments.no.from.or.to.date": "Start date and end date is required", "validation.error.temperatureAdjustments.from.date.to.date.same": "End date must be after start date", "validation.error.temperatureAdjustments.excursion.time.too.short": "Excursions below { minExcursionTime } minutes should not be reported", "validation.error.temperatureAdjustments.duplicate.date.value": "Duplicate dates are not allowed", "validation.error.adjustment.no.trial": "Trial is required", "validation.error.adjustment.no.duns": "No Kits (DUNs) uploaded", "validation.error.adjustment.no.logger.selected": "Logger is required", "validation.error.adjustment.no.manual.adjustment": "Must adjust at least one Kit (DUN).", "module.deviation.invalid.adjustment.min.and.max.temperature.null.heading": "Failed to save adjustment", "module.deviation.invalid.adjustment.min.and.max.temperature.null.content": "Min, max or both temperature is required", "module.deviation.reevaluate": "Re-evaluate", "module.loggers.manual.adjustment.trial.dropdown.label": "Trial", "module.loggers.manual.adjustment.use.file.adjustment.label": "Use file for adjustment", "module.loggers.manual.adjustment.site.dropdown.label": "Site", "module.loggers.manual.adjustment.shipment.dropdown.label": "Shipment", "module.loggers.manual.adjustment.dropdown.null.label": " -- -- ", "module.loggers.manual.adjustment.profile.dropdown.label": "Profile", "module.loggers.manual.adjustment.batch.input.label": "Batch no.", "module.loggers.manual.adjustment.batch.input.select.profile.first.tooltip": "Select a profile first", "module.loggers.manual.adjustment.reference.input.label": "Reference", "module.loggers.manual.adjustment.comment.input.label": "Comment", "module.loggers.manual.adjustment.comment.duplicate.deviation.label": "duplicate deviation", "module.loggers.manual.adjustment.comment.duplicate.deviation.text": "Duplicate deviation", "module.loggers.manual.adjustment.dun.excursion.details.header.heading": "Kit (DUN) details", "module.loggers.manual.adjustment.dun.excursion.details.heading": "Stability data overview", "module.loggers.manual.adjustment.dun.excursion.details.deviations.label": "Deviation(s)", "module.loggers.manual.adjustment.dun.excursion.details.adjustments.label": "Adjustment(s)", "module.loggers.manual.adjustment.dun.excursion.details.product.name.label": "Product name", "module.loggers.manual.adjustment.dun.excursion.allowed.label": "Allowed: ", "module.loggers.manual.adjustment.dun.excursion.used.label": "Used: ", "module.loggers.manual.adjustment.dun.table.kit.id": "Kit <PERSON> (Dun)", "module.loggers.manual.adjustment.dun.table.kit.desc": "Description", "module.loggers.manual.adjustment.some.kits.not.in.trial.header": "kits (Duns) not found", "module.loggers.manual.adjustment.some.kits.not.in.trial.content": "Not all kits (Duns) in the uploaded file on trial {trial}", "module.loggers.manual.adjustment.no.kits.in.trial.header": "Kits (DUNS) not found", "module.loggers.manual.adjustment.no.kits.in.trial.content": "Kits (DUNS) in the uploaded file not found on trial {trial}", "module.loggers.manual.adjustment.kits.not.same.profile.header": "Kits (DUNs) validation failed", "module.loggers.manual.adjustment.kits.not.same.profile.content": "The kits (DUNs) are on different profiles", "module.loggers.manual.adjustment.no.kits.in.trial": "No kits (DUNS) found in trial", "module.loggers.manual.adjustment.file.upload.error.max.data.header": "Invalid CSV file", "module.loggers.manual.adjustment.file.upload.error.max.data.content": "Kit (DUN) import is limited to max 2000 Kits (DUNs) ", "module.loggers.manual.adjustment.file.upload.error.no.data.header": "File parsing failed", "module.loggers.manual.adjustment.file.upload.error.no.data.content": "CSV file did not contain any Kits (DUNs) ", "module.loggers.manual.adjustment.file.upload.error.invalid.data.header": "File parsing failed", "module.loggers.manual.adjustment.file.upload.error.invalid.data.content": "Invalid file, CSV file has incorrect syntax", "module.loggers.manual.adjustment.file.upload.error..header": "File parsing failed", "module.loggers.manual.adjustment.file.upload.validation.error.adjusted.kit.null.heading": "Adjustment details", "module.loggers.manual.adjustment.file.upload.validation.error.adjusted.kit.null.content": "Adjusted kits (DUNs) must not be empty", "module.loggers.manual.adjustment.file.upload.validation.error.header": "Adjustment details", "module.loggers.manual.adjustment.file.upload.validation.error.content": "Adjustment details validate error", "module.loggers.manual.adjustment.file.upload.generic.header": "Upload Kits (DUNs) failed ", "module.loggers.manual.adjustment.file.upload.generic.content": "An unknown error occurred", "module.adjustment.manual.no.duns.heading": "Adjustment details", "module.adjustment.manual.no.duns.content": "You have not adjusted all Kits (DUNs). All Kits (DUNs) need to be selected for adjustment even if no time is to be added", "module.deviation.invalid.adjustment.duns.uncovered.heading": "Adjustment details", "module.deviation.invalid.adjustment.duns.uncovered.content": "You have not adjusted all Kits (DUNs). All Kits (DUNs) need to be selected for adjustment even if no time is to be added", "module.adjustment.manual.no.intervaladjustments.heading": "Adjustment details", "module.adjustment.manual.no.intervaladjustments.content": "Adjustment could not be saved, no stability data adjusted", "module.loggers.deviation.adjustment.sub.heading": "Create manual adjustment for trial {trial}, site {site}", "module.loggers.deviation.adjustment.step1.heading": "Step 1: Select Batches and/or Kits (DUNs)", "module.loggers.deviation.adjustment.step2.heading": "Step 2: Adjust temperature stability data", "module.loggers.manual.adjustment.profile.label": "Profile", "module.loggers.manual.adjustment.batch.label": "Batch (Product name)", "module.loggers.manual.adjustment.dun.label": "Kit ID (DUN)", "module.loggers.manual.adjustment.type.label": "Type", "module.loggers.manual.adjustment.comment.label": "Comment", "module.loggers.manual.adjustment.status.label": "Status", "module.loggers.manual.adjustment.documents.label": "Documents", "module.loggers.manual.adjustment.trial.site.label": "Trial/Site", "module.loggers.manual.adjustment.trial.shipment.label": "Shipment(s)", "module.loggers.manual.adjustment.trial.site.value": "{trial}/{site}", "module.loggers.manual.adjustment.date.label": "Date/Time", "module.loggers.manual.adjustment.concluded.by.label": "Concluded by", "module.loggers.manual.adjustment.prioritisation.date.label": "Prioritisation date", "module.loggers.manual.adjustment.trigger.ref.label": "<PERSON><PERSON> (Reference)", "module.loggers.manual.adjustment.trigger.ref.type.logger.storage.prefix": "STOR.", "module.loggers.manual.adjustment.trigger.ref.type.logger.shipment.prefix": "SHIP.", "module.loggers.manual.adjustment.id.label": "Id", "module.loggers.manual.adjustment.description.label": "Description", "module.loggers.manual.adjustment.gap.label": "Gap time", "module.loggers.manual.adjustment.gap.deviation.label": "Deviation gap time", "module.loggers.manual.adjustment.site.dropdown.disabled.reason": "Trial is not selected or shipment is selected", "module.loggers.manual.adjustment.shipment.dropdown.disabled.reason": "Trial is not selected or site is selected", "module.loggers.manual.adjustment.review.disabled": "You have changed data for this adjustment. Save as draft before sending for review", "module.loggers.manual.adjustment.add.selected.duns": "Adjust selected", "module.loggers.manual.adjustment.add.selected.duns.disabled": "You must select at least one Kit (DUN)/batch", "module.loggers.manual.adjustment.must.select.duns": "You must select at least one batch or Kit (DUN) before you can continue", "module.loggers.manual.adjustment.not.all.adjustments.commited": "You must commit all adjustments to continue", "module.loggers.manual.adjustment.different.gap.time": "You can only adjust Kits (DUNs) with same gap time", "module.loggers.manual.adjustment.gap.not.adjusted": "The adjusted time does not match gap time: {sign}{days, number} d {hours, number} h {minutes, number} m", "module.loggers.manual.adjustment.commit.intervals": "Commit stability data changes", "module.loggers.manual.adjustment.add.new.temperature.level": "Add temperature level", "module.loggers.manual.adjustment.time.adjustments.heading": "Temperature Adjustments", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload": "CSV file upload", "module.loggers.manual.adjustment.dun.pre.adjustment.uploaded.data": "Uploaded Kits (DUNs) ", "module.loggers.upload.adjustment.no.file.uploaded": "No file uploaded yet", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.instructions": "Drag and drop a CSV file here", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.error.bad.data.header": "Could not upload CSV file", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.error.no.data.content": "CSV file did not contain any Kits (DUNs)", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.error.invalid.data.content": "CSV file has incorrect syntax", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.error.max.data.content": "Kit (DUN) import is limited to max 5000 Kits (DUNs) ", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.error.generic.content": "An unknown error occurred", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.type.error.heading": "Incorrect file", "module.loggers.manual.adjustment.dun.pre.adjustment.file.upload.type.error.content": "File must be in csv-format", "module.loggers.manual.adjustment.dun.pre.adjustment.unblinded.label": "Unblinded", "module.loggers.manual.adjustment.dun.pre.adjustment.blinded.label": "Blinded", "module.loggers.manual.adjustment.logger.adjustment.logger.drop.down.label": "Select logger to adjust", "module.loggers.manual.adjustment.logger.adjustment.logger.disclaimer.label": "The entered data will result in an adjustment to corresponding interval for the affected time", "module.loggers.manual.adjustment.logger.adjustment.confirm.dialog.heading": "Are you sure you want to adjust this logger?", "module.deviation.invalid.adjustment.overlapping.times.heading": "Overlapping dates", "module.deviation.invalid.adjustment.overlapping.times.content": "The start date and/or end date of one or several excursions are overlapping. These dates have been reset and need to be set again.", "module.loggers.manual.adjustment.logger.adjustment.no.uploads.disclaimer.heading": "The selected logger has no uploaded data to adjust", "module.loggers.manual.adjustment.logger.adjustment.no.uploads.disclaimer.content": "Select another logger to adjust", "module.loggers.manual.adjustment.logger.adjustment.last.upload.adjusted": "Last sample has been adjusted", "module.deviation.invalid.adjustment.invalid.duns.heading": "Failed to save adjustment", "module.deviation.invalid.adjustment.invalid.duns.content": "Invalid Kits (DUNs)", "module.adjustment.no.logger.missions.during.times.heading": "Failed to save adjustment", "module.adjustment.no.logger.missions.during.times.content": "Logger adjustment times was not during the uploaded logger mission times", "module.adjustment.empty.heading": "Failed to save adjustment", "module.adjustment.empty.content": "No logger adjustment provided", "module.adjustment.ambiguous.openEndedInterval.adjustment.heading": "Failed to save adjustment", "module.adjustment.ambiguous.openEndedInterval.adjustment.content": "Selected Kits (DUNs) have different time used in this interval", "module.loggers.adjustment.sets.list.heading": "Adjustments", "module.loggers.adjustment.overview": "Overview", "module.loggers.adjustment.add": "Add", "module.loggers.adjustment.attach.file.label": "Attach File(s)", "module.loggers.adjustment.view.attached.file.label": "Attached File(s)", "module.loggers.adjustment.sets.list.trial": "Trial", "module.loggers.adjustment.sets.list.site": "Site", "module.loggers.adjustment.sets.list.presentation.id": "ID", "module.loggers.adjustment.sets.list.user": "Updated by", "module.loggers.adjustment.sets.list.type": "Type", "module.loggers.adjustment.sets.list.status": "Status", "module.loggers.adjustment.sets.list.type.manual": "Manual", "module.loggers.adjustment.sets.list.type.deviation_manual": "Deviation - Manual", "module.loggers.adjustment.sets.list.type.deviation_gap": "Deviation - Gap", "module.loggers.adjustment.sets.list.type.deviation_post_manual": "Deviation - Post - Manual", "module.loggers.adjustment.sets.list.type.deviation_post_gap": "Deviation - Post - Gap", "module.loggers.adjustment.sets.list.type.logger": "Logger adjustment", "module.loggers.adjustment.sets.list.type.pre_batch": "Pre - Batch", "module.loggers.adjustment.sets.list.type.pre_dun": "Pre - Kit (DUN)", "module.loggers.adjustment.audit.page.heading": "Adjustment audit log", "module.loggers.adjustment.upload.label": "Upload files", "module.loggers.adjustment.upload.instructions": "Drag and drop your files here", "module.loggers.manual.adjustment.nr.heading": "Adjustment #{nr}", "module.loggers.manual.adjustment.dun.list.heading": "Adjusted Kits (DUNs)", "module.loggers.manual.adjustment.interval.adjustments.heading": "Interval adjustments", "module.loggers.manual.adjustment.result.heading": "Results", "module.loggers.manual.adjustment.result.product.conclusion.no.failed": "OK  - All Kits (DUNs)", "module.loggers.manual.adjustment.result.product.conclusion.unknown": "Unknown - <PERSON> (DUNs)", "module.loggers.manual.adjustment.result.product.conclusion.some.failed": "Some OK - Some NOT OK", "module.loggers.manual.adjustment.result.product.conclusion.all.failed": "Quarantine - All Kits (DUNs)", "module.loggers.manual.adjustment.result.product.conclusion.failed.some.unknown": "Some Unknown - Some NOT OK", "module.loggers.manual.adjustment.result.product.conclusion.some.failed.some.unknown": "Some OK, Some NOT OK, Some Unknown", "module.loggers.manual.adjustment.result.product.conclusion.some.unknown": "Some OK - Some Unknown", "module.loggers.manual.adjustment.result.dun.conclusion.ok": "OK", "module.loggers.manual.adjustment.result.dun.conclusion.not.ok": "NOT OK", "module.loggers.manual.adjustment.result.dun.conclusion.pending": "Unknown - await manual deviation handling", "module.loggers.manual.adjustment.result.dun.conclusion.pending.inactive": "Unknown - await manual deviation handling - Kit (DUN) Dispensed/Damaged", "module.loggers.manual.adjustment.result.dun.conclusion.pending.dispensed": "Unknown - await manual deviation handling - Kit (DUN) Dispensed", "module.loggers.manual.adjustment.result.dun.conclusion.pending.damaged": "Kit (DUN) Damaged", "module.loggers.manual.adjustment.csv.file.upload": "CSV file upload", "module.loggers.manual.adjustment.csv.file.upload.instructions": "Drag and drop a CSV file here", "module.loggers.state.conflict.replace.heading": "Failed to replace logger!", "module.loggers.state.conflict.cancel.heading": "Failed to cancel logger replacement!", "module.loggers.state.conflict.update.heading": "Failed to update logger!", "module.loggers.unknown.error.dialog.heading": "Unknown error", "module.loggers.unknown.error.dialog.content": "An unknown error occurred", "module.loggers.replacement.failed": "An unknown error occurred", "module.missions.list.heading": "Archive", "module.missions.list.serial": "Logger id", "module.missions.list.model": "Model", "module.missions.list.ref": "Mission id", "module.missions.list.uploaddate": "upload date", "module.missions.list.username": "Username", "module.missions.files": "Files", "module.missions.loggers": "<PERSON><PERSON> ( Associated Products )", "module.missions.download.failure": "Failed to download file", "module.loggers.replacement.no.replacement.to.cancel": "No logger replacement to cancel found", "module.loggers.replacement.already.occurred": "Can't cancel a replacement that has already occurred", "module.loggers.replacement.before.scheduled.replacement": "The first logger is scheduled to replace another logger, so it can't be replaced before that.", "module.loggers.replacement.not.in.future": "Replacement date must be in the future", "module.loggers.replacement.before.prev.logger.validFrom": "Not possible to replace before previous loggers association date", "module.loggers.replacement.before.prev.logger.association": "Replacement date cannot be earlier than replaced logger first trial/site association", "module.loggers.replacement.before.new.logger.validFrom": "Not possible to replace before new loggers association date", "module.loggers.replacement.before.upload": "Logger cannot be replaced before last upload", "module.loggers.replacement.before.next.logger.upload": "The replacement date/time cannot be set earlier than the date/time for the last registered temperature on the replacement logger.", "module.loggers.replacement.new.logger.future.disassociation.for.inherited.trialunit": "The replacement cannot be completed since the replacement logger have disassociation(s) after the replacement date", "module.loggers.replacement.new.logger.discontinued": "New logger already discontinued", "module.loggers.replacement.prev.logger.already.replaced": "Previous logger is already replaced", "module.loggers.replacement.new.logger.already.replaced.other": "New logger has already replaced another logger", "module.loggers.replacement.different.offices": "Only possible to replace logger that belongs to the same office.", "module.loggers.reactivate.not.discontinued": "Only possible to reactivate discontinued loggers", "module.loggers.deviation.comment.deviation.type.label": "Deviation type", "module.loggers.deviation.comment.trial.site.label": "Trial/Site", "module.loggers.deviation.comment.next.visit.label": "Next dispensing visit", "module.loggers.deviation.comment.info.text": "Deviation(s) detected during the upload. Please add a comment to the deviation and register the next dispensing visit date if applicable.", "module.loggers.deviation.comment.info.label": "Add deviation comment", "module.loggers.deviation.comment.validation.error.label": "At least one value has to be entered", "module.loggers.deviation.success.dialog.heading": "Deviation comment added", "module.loggers.deviation.error.dialog.heading": "Failed to add comment", "module.loggers.deviation.error.dialog.content": "Deviation comment could not be saved", "module.loggers.shipment.upload.noduns.heading": "Upload failed", "module.loggers.shipment.upload.noduns.content": "There are no pending Kits (DUNs) for this shipment.", "module.loggers.shipment.upload.noduns.message": "{additional}", "module.loggers.shipment.inactive.trial.heading": "Upload failed", "module.loggers.shipment.inactive.trial.content": "Trial is inactive", "module.deviation.invalid.adjustment.not.chronological.heading": "Failed to submit adjustment", "module.deviation.invalid.adjustment.not.chronological.content": "An unexpected error occurred preventing this trial to change status", "module.loggers.livedata.subscribe.failed.heading": "Failed to add logger", "module.loggers.livedata.subscribe.failed": "Couldn't subscribe to live data", "module.loggers.add.not.allowed.trialAssociation.absentLoggerType.heading": "Failed to add logger", "module.loggers.update.not.allowed.trialAssociation.absentLoggerType.heading": "Failed to update logger", "module.loggers.update.not.allowed.trialAssociation.absentLoggerType": "Logger of type {temperatureType} cannot be associated to Trial {trialNames} as it does not match in Temperature Type", "module.loggers.duplicate.logger.slot.link": "<PERSON><PERSON> already associated with this slot", "module.loggers.no.logger.slot.available": "No available sensor slots", "module.loggers.search.global.logger.placeholder": "Type to search available loggers", "module.loggers.invalid.creation.conflicting.unitSerialNo.of.globalLogger": "Logger Id sync error", "module.loggers.kaa.search.failed.heading": "Search failed", "module.loggers.kaa.search.failed.content": "Couldn't not communicate with backend system", "module.loggers.model.not.allowed.to.register.heading": "Register new logger", "module.loggers.model.not.allowed.to.register.content": "Not allowed to register new logger of this type.", "module.loggers.upload.shipment.notReady.short": "Shipment is not ready."}