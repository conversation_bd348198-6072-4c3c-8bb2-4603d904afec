/*
ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
*/
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { css, StyleSheet } from 'aphrodite';
import { FormattedMessage } from 'react-intl';
import {
  BatchShapeList,
  STANDARD_TEXT,
  H1,
  H4,
  TSS_DARK_BLUE,
  TSS_ORANGE,
  TSS_BLACK,
  TSS_RED,
  TSS_GREEN,
  hexgen,
} from 'tss-lib-common';
import {
  Icon,
  Label,
  Checkbox,
  PrimaryButton,
  TertiaryButton,
  SecondaryButton,
  IMG_ICON_FILE_TXT_BLUE_LARGE,
  IMG_ICON_THREE_DOTS,
  IMG_ICON_CROSS_SMALL,
  IMG_ICON_CHECKMARK,
  Tooltip,
} from 'tss-lib-components';

import {
  STATUS_UPLOAD_PROCESSING,
  STATUS_UPLOAD_FAILED,
  STATUS_UPLOAD_SUCCESSFUL,
} from '../constants';
import { uniqueProductNamesFromBatches } from '../transforms';

let styles;

class LoggerUploadDetails extends Component {
  static propTypes = {
    batches: BatchShapeList,
    batchIds: PropTypes.object.isRequired, // eslint-disable-line
    status: PropTypes.number.isRequired,
    disabled: PropTypes.bool.isRequired,
    serialNumber: PropTypes.string,
    errorMessage: PropTypes.string,
    selectBatchesManually: PropTypes.bool,
    onDownload: PropTypes.func,
    onBatchSelectedChange: PropTypes.func.isRequired,
    onSaveSelectedBatches: PropTypes.func.isRequired,
    onRemoveLoggerClick: PropTypes.func.isRequired,
    onCancel: PropTypes.func.isRequired,
    expectedBatchIds: PropTypes.arrayOf( PropTypes.number ),
  };

  static defaultProps = {
    serialNumber: '',
    selectBatchesManually: true,
    onDownload: null,
    errorMessage: null,
    batches: null,
    expectedBatchIds: [],
  };

  state = {};

  componentWillUnmount() {}

  handleSaveSelectedBatches = async () => {
    if ( this.props.selectBatchesManually ) {
      // manual select
      if ( this.props.batchIds.size ) {
        this.setState( { isSavingBatches: true } );
        try {
          await this.props.onSaveSelectedBatches();
          this.setState( { isSavingBatches: false, savingBatchesSuccess: true } );
        } catch ( e ) {
          this.setState( { isSavingBatches: false, savingBatchesFailed: true } );
        }
      } else {
        this.setState( {
          isSavingBatches: false,
          savingBatchesSuccess: false,
        } );
      }
    } else {
      // auto select
      try {
        const sameTypeBatches = this.props.expectedBatchIds.length > 0 ? this.props.expectedBatchIds :
          this.props.batches.map( ( batch ) => (
            batch.id ) ).filter( ( batch ) => batch !== null ).toArray();

        await this.props.onBatchSelectedChange( sameTypeBatches, true );
        await this.props.onSaveSelectedBatches();
        this.setState( { isSavingBatches: false, savingBatchesSuccess: true } );
      } catch ( e ) {
        this.setState( { isSavingBatches: false, savingBatchesFailed: true } );
      }
    }
  };

  handleBatchSelectedChange = ( batchId, selected ) => {
    const selectedBatch = this.props.batches.find( ( batch ) => batch.id === batchId );
    const sameTypeBatches = this.props.batches.map( ( batch ) =>
      ( batch.packType === selectedBatch.packType ? batch.id : null ) ).filter( ( batch ) => batch !== null ).toArray();
    this.setState( { batchChanged: true } );
    this.props.onBatchSelectedChange( sameTypeBatches, selected );
  };

  handleExpectedBatchIds = ( ) => {
    this.props.onBatchSelectedChange( this.props.expectedBatchIds, true );
    this.setState( { batchChanged: true } );
  }


  handleWithExpectedBatchIds = () => !this.state.batchChanged &&
      ( !this.props.batchIds || this.props.batchIds.size <= 0 ) &&
      this.props.expectedBatchIds.length > 0 &&
      this.props.selectBatchesManually

  manuallySelected = ( batchId ) => ( this.props.expectedBatchIds.length > 0 && !this.props.selectBatchesManually ?
    this.props.expectedBatchIds.includes( batchId ) : !this.props.selectBatchesManually )

  render() {
    const { status, selectBatchesManually } = this.props;
    const noBatchSelected = this.props.batchIds && this.props.batchIds.size <= 0;
    if ( this.handleWithExpectedBatchIds() ) {
      this.handleExpectedBatchIds();
    }

    // Only display unique packTypes
    const uniqueBatches = this.props.batches ? uniqueProductNamesFromBatches( this.props.batches ) : [];
    return (
      <div className={ css( styles.container ) }>
        <div>
          { status === STATUS_UPLOAD_FAILED && this.props.errorMessage
            ? [
              <div
                key="primary-failed"
                className={ css(
                  H1,
                  styles.headingInfoPrimaryError,
                ) }
              >
                <FormattedMessage id="module.loggers.upload.failed.to.process" />
                  :
                <span className={ css( styles.headingInfoPrimaryReason ) }>
                  <FormattedMessage id={ `${ this.props.errorMessage }.short` } />
                </span>
              </div>,
            ]
            : null }
          { status === STATUS_UPLOAD_FAILED &&
          !this.props.errorMessage
            ? [
              <div
                key="primary-failed"
                className={ css(
                  H1,
                  styles.headingInfoPrimaryError,
                ) }
              >
                <FormattedMessage id="module.loggers.upload.failed.to.process" />
              </div>,
            ]
            : null }
          { status === STATUS_UPLOAD_SUCCESSFUL
            ? [
              <div
                key="primary-success"
                className={ css( H1 ) }
              >
                <FormattedMessage id="module.loggers.upload.file.added.successfully" />
              </div>,
            ]
            : null }
        </div>
        <div className={ css( styles.heading ) }>
          <div className={ css( styles.iconContainer ) }>
            <Icon src={ IMG_ICON_FILE_TXT_BLUE_LARGE } />
            { status === STATUS_UPLOAD_PROCESSING ? (
              <div
                className={ css( styles.statusBlob, styles.statusProcessingBlob ) }
              >
                <Icon
                  src={ IMG_ICON_THREE_DOTS }
                  fill="white"
                  className={ css( styles.statusBlobBlink ) }
                />
              </div>
            ) : null }
            { status === STATUS_UPLOAD_FAILED ? (
              <div className={ css( styles.statusBlob, styles.statusFailedBlob ) }>
                <Icon
                  src={ IMG_ICON_CROSS_SMALL }
                  stroke="white"
                  className={ css( styles.statusFailedIcon ) }
                />
              </div>
            ) : null }
            { status === STATUS_UPLOAD_SUCCESSFUL ? (
              <div className={ css( styles.statusBlob, styles.statusSuccessBlob ) }>
                <Icon
                  src={ IMG_ICON_CHECKMARK }
                  stroke="white"
                  className={ css( styles.statusSuccessIcon ) }
                />
              </div>
            ) : null }
          </div>
          <div className={ css( styles.headingInfo ) }>
            { status === STATUS_UPLOAD_PROCESSING
              ? [
                <div
                  key="primary-processing"
                  className={ css( styles.headingInfoPrimary ) }
                >
                  <FormattedMessage id="module.loggers.upload.file.added" />
                </div>,
                <div
                  key="secondary-processing"
                  className={ css( styles.headingInfoSecondary ) }
                >
                  <FormattedMessage id="module.loggers.upload.status.processing" />
                </div>,
              ]
              : null }
            { status === STATUS_UPLOAD_FAILED &&
            this.props.errorMessage
              ? [
                <div
                  key="secondary-failed"
                  className={ css( styles.headingInfoSecondary ) }
                >
                  <FormattedMessage id="module.loggers.upload.remove.file" />
                </div>,
              ]
              : null }
            { status === STATUS_UPLOAD_FAILED &&
            !this.props.errorMessage
              ? [
                <div
                  key="primary-failed"
                  className={ css( styles.headingInfoPrimary ) }
                >
                  <FormattedMessage id="module.loggers.upload.failed.to.process" />
                </div>,
              ]
              : null }
            { status === STATUS_UPLOAD_SUCCESSFUL
              ? [
                <div
                  key="secondary-success"
                  className={ css( styles.headingInfoSecondary ) }
                >
                  { this.props.serialNumber }
                </div>,
              ]
              : null }
          </div>
          <div className={ css( styles.headingActions ) }>
            { status === STATUS_UPLOAD_SUCCESSFUL ||
            status === STATUS_UPLOAD_FAILED ? (
                <TertiaryButton
                  disabled={ this.props.disabled }
                  style={ styles.removeButton }
                  onClick={ this.props.onRemoveLoggerClick }
                >
                  <FormattedMessage id="module.loggers.upload.remove.logger.button.label" />
                </TertiaryButton>
              ) : null }
            { this.props.onDownload ? (
              <TertiaryButton
                style={ styles.removeButton }
                onClick={ this.props.onDownload }
              >
                <FormattedMessage id="module.loggers.upload.download.logger.file.button.label" />
              </TertiaryButton>
            ) : null }
          </div>
        </div>
        { status !== STATUS_UPLOAD_FAILED
          ? [
            <div key="batches" className={ css( styles.batchesContainer ) }>
              <h4 className={ css( H4, styles.batchesHeading ) }>
                <FormattedMessage id={ selectBatchesManually ? 'module.loggers.upload.select.batches.for.this.logger' : 'module.loggers.upload.selected.batches.for.this.logger' } />
              </h4>
              <div className={ css( styles.batchesList ) }>
                { this.props.batches && this.props.batches.size ? (
                  uniqueBatches.map( ( item ) => {
                    const hexId = hexgen();
                    return (
                      <div
                        key={ item.id }
                        className={ css( styles.batchContainer ) }
                      >
                        <Label
                          htmlFor={ `#batch-${ hexId }` }
                          label={ item.packType }
                          style={ [ STANDARD_TEXT, styles.checkboxLabel ] }
                          row
                          succeed
                        >
                          <Checkbox
                            id={ `#batch-${ hexId }` }
                            checked={ this.props.batchIds.has( item.id ) || this.manuallySelected( item.id ) }
                            disabled={ this.props.disabled || !selectBatchesManually }
                            value={ item.id }
                            onChange={ this.handleBatchSelectedChange }
                          />
                          { !selectBatchesManually ? (
                            <Tooltip>
                              <FormattedMessage id="module.loggers.upload.selected.batches.system.selected" />
                            </Tooltip>
                          ) : null }
                        </Label>
                      </div>
                    );
                  } )
                ) : (
                  <FormattedMessage id="module.loggers.upload.no.available.batches.for.this.shipment" />
                ) }
              </div>
            </div>,
          ]
          : null }
        <div key="actions" className={ css( styles.actionContainer ) }>
          <div key="actions-buttons">
            { status === STATUS_UPLOAD_SUCCESSFUL
              ? [
                <PrimaryButton
                  key="actions-buttons-save"
                  loading={ this.state.isSavingBatches }
                  disabled={
                    this.state.isSavingBatches ||
                    this.props.disabled ||
                    noBatchSelected && selectBatchesManually
                  }
                  onClick={ this.handleSaveSelectedBatches }
                >
                  <FormattedMessage id="label.save" />
                  { noBatchSelected && selectBatchesManually ? (
                    <Tooltip>
                      <FormattedMessage id="module.loggers.upload.no.batches.selected.error" />
                    </Tooltip>
                  ) : null }
                </PrimaryButton>,
                <SecondaryButton
                  key="actions-buttons-cancel"
                  disabled={ this.state.isSavingBatches }
                  onClick={ this.props.onCancel }
                >
                  <FormattedMessage id="label.cancel" />
                </SecondaryButton>,
              ]
              : null }
            { !status || status === STATUS_UPLOAD_FAILED
              ? [
                <PrimaryButton
                  key="actions-buttons-ok"
                  loading={ this.state.isSavingBatches }
                  disabled={ this.state.isSavingBatches || this.props.disabled }
                  onClick={ this.props.onRemoveLoggerClick }
                >
                  <FormattedMessage id="label.ok" />
                </PrimaryButton>,
              ]
              : null }
          </div>
        </div>
      </div>
    );
  }
}

const blinkKeyframes = {
  '0%': {
    opacity: 1,
  },
  '50%': {
    opacity: 0.2,
  },
  '100%': {
    opacity: 1,
  },
};

styles = StyleSheet.create( {
  container: {
    width: '63rem',
  },

  heading: {
    padding: '2.3rem 2.3rem 2.5rem 2.3rem',
    display: 'flex',
  },

  headingInfo: {
    margin: '0 0 0 2rem',
    padding: '0.2rem 0',
    display: 'flex',
    textAlign: 'left',
    justifyContent: 'space-between',
    flexDirection: 'column',
  },

  headingInfoPrimary: {
    fontSize: '1.4rem',
    fontWeight: 600,
  },
  headingInfoPrimaryError: {
    color: TSS_RED(),
  },
  headingInfoPrimaryReason: {
    marginLeft: '0.2rem',
    color: TSS_RED(),
  },
  headingInfoSecondary: {
    fontSize: '1.4rem',
    color: TSS_DARK_BLUE( 0.6 ),
  },

  headingActions: {
    flex: '1 0 auto',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },

  iconContainer: {
    position: 'relative',
  },

  statusBlob: {
    position: 'absolute',
    right: '-0.9rem',
    bottom: '-0.5rem',
    width: '1.9rem',
    height: '1.9rem',
    border: '2px solid white',
    borderRadius: '50%',
    zIndex: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },

  statusBlobBlink: {
    animationName: blinkKeyframes,
    animationDuration: '1s',
    animationIterationCount: 'infinite',
  },

  statusProcessingBlob: {
    backgroundColor: TSS_ORANGE(),
  },

  statusFailedBlob: {
    backgroundColor: TSS_RED(),
  },

  statusSuccessBlob: {
    backgroundColor: TSS_GREEN(),
  },

  statusFailedIcon: {
    width: 7,
    height: 8,
  },

  statusSuccessIcon: {
    width: 9,
    height: 10,
  },

  batchesContainer: {
    borderTop: `1px solid ${ TSS_DARK_BLUE( 0.1 ) }`,
    padding: '2.3rem 0 0 0',
  },

  batchesHeading: {
    margin: '1rem 0 2rem 2.3rem',
  },

  batchesList: {
    display: 'flex',
    flexDirection: 'column',
    margin: '0 0 0 3.3rem',

    '>:last-child': {
      margin: '0 auto 0 0',
    },
  },

  batchContainer: {
    margin: '0 auto 2rem 0',
  },

  actionContainer: {
    padding: '0 0 2.5rem 0',
    alignItems: 'center',
  },

  removeButton: {
    height: 'auto',
    color: TSS_BLACK( 0.5 ),
  },
} );

export default LoggerUploadDetails;
