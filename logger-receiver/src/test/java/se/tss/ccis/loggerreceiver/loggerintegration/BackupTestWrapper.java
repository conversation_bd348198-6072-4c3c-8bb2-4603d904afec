/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.loggerreceiver.loggerintegration;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

/**
 * Simple wrapper to be able to fetch the name of the latest file stored (for unit tests)
 */
public class BackupTestWrapper implements LoggerFileBackup {

    private final LoggerFileBackup backup;
    private String latestFilename;

    public BackupTestWrapper(LoggerFileBackup backup) {
        this.backup = backup;
    }
    
    @Override
    public CompletableFuture<Integer> writeFileToBackupAsync(InputStream fileInputStream, String filename) {
        latestFilename = filename;
        return backup.writeFileToBackupAsync(fileInputStream, filename);
    }

    @Override
    public int writeFileToBackup(InputStream fileInputStream, String filename) {
        latestFilename = filename;
        return backup.writeFileToBackup(fileInputStream, filename);
    }

    @Override
    public InputStream openInputStream(String filename) throws IOException {
        return backup.openInputStream(filename);
    }

    @Override
    public boolean exists(String filename) {
        return backup.exists(filename);
    }

    @Override
    public void delete(String filename) {
        backup.delete(filename);
    }
    
    public String getLatestFilename() {
        return latestFilename;
    }
}
