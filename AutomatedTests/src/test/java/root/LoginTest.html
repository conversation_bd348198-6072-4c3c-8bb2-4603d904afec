<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head profile="http://selenium-ide.openqa.org/profiles/test-case">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<link rel="selenium.base" href="https://development.ccis.se/" />
<title>mySettings</title>
</head>
<body>
<table cellpadding="1" cellspacing="1" border="1">
<thead>
<tr><td rowspan="1" colspan="3">mySettings</td></tr>
</thead><tbody>
<tr>
	<td>open</td>
	<td>/login.action</td>
	<td></td>
</tr>
<tr>
	<td>type</td>
	<td>id=username</td>
	<td>johane</td>
</tr>
<tr>
	<td>type</td>
	<td>id=newPassword1</td>
	<td>280_CCIS</td>
</tr>
<tr>
	<td>clickAndWait</td>
	<td>css=button.fv-hidden-submit</td>
	<td></td>
</tr>
<tr>
	<td>click</td>
	<td>css=i.fa.fa-bars</td>
	<td></td>
</tr>
<tr>
	<td>click</td>
	<td>//ul[@id='side-menu']/li[3]/a/span</td>
	<td></td>
</tr>
<tr>
	<td>click</td>
	<td>css=#menu-my-settings &gt; span.nav-label</td>
	<td></td>
</tr>
<tr>
	<td>waitForElementPresent</td>
	<td>css=h2</td>
	<td></td>
</tr>
<tr>
	<td>assertText</td>
	<td>css=h2</td>
	<td>General</td>
</tr>

</tbody></table>
</body>
</html>
