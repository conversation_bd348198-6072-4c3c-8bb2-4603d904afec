package ui.login;

import framework.Screenshot;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import ui.AutomatedTestBase;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.openqa.selenium.By.id;

public class WelcomeTextShownAfterLogin extends AutomatedTestBase {

  @Before
  public void setUp() throws Exception {
    setUp("WelcomeTextShownAfterLogin", "https://development1.ccis.se");
  }



  @After
  public void tearDown() {
   super.tearDown();
  }

  @Test
  public void welcomeTextShowVersion() throws Exception {
    int idx = 0;
    driver.get(baseUrl + "/login.action");
    driver.findElement(id("username")).clear();
    driver.findElement(id("username")).sendKeys(username);
    driver.findElement(id("newPassword1")).clear();
    driver.findElement(id("newPassword1")).sendKeys(password);
    Screenshot.capture(driver, testRun, (idx++)+".png");
    driver.findElement(id("login-button")).click();
    Thread.sleep(2000);
    Screenshot.capture(driver, testRun, (idx++)+".png");
    try {
      String text =driver.getTitle();
      assertEquals("CCIS Portal v2.7.6", text);
      Screenshot.capture(driver, testRun, (idx)+".png");
    } catch (Error e) {
      fail(e.toString());
    }
  }

}
