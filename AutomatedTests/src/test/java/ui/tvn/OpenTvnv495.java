package ui.tvn;

import framework.Screenshot;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.openqa.selenium.By;
import ui.AutomatedTestBase;

import static org.junit.Assert.assertEquals;

public class OpenTvnv495  extends AutomatedTestBase{



  @Before
  public void setUp() throws Exception {
   super.setUp("openTvnV4_9_5", "https://development1.ccis.se");
  }

  @Test
  public void testOpenTvnv495() throws Exception {
    int idx = 0;
    driver.get(baseUrl + "/login.action");
    driver.findElement(By.id("username")).clear();
    driver.findElement(By.id("username")).sendKeys(username);
    driver.findElement(By.id("newPassword1")).clear();
    driver.findElement(By.id("newPassword1")).sendKeys(password);
    driver.findElement(By.id("login-button")).click();
    Thread.sleep(2000);
    Screenshot.capture(driver, testRun, (idx++)+".png");
    assertEquals("CCIS Portal v2.7.6", driver.getTitle());
    driver.findElement(By.cssSelector("div.menuItem > img")).click();
    Thread.sleep(500);
    assertEquals("CCIS TempViaNet v4.9.5", driver.getTitle());
    Screenshot.capture(driver, testRun, (idx++)+".png");
    driver.findElement(By.linkText("CLOSE")).click();
    Thread.sleep(500);
    driver.findElement(By.cssSelector("div.logout > img")).click();
    Screenshot.capture(driver, testRun, (idx)+".png");
  }

  @After
  public void tearDown() {
    super.tearDown();
  }
}
