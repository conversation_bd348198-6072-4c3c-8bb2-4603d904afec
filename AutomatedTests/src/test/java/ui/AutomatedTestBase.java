package ui;

import org.junit.Ignore;
import org.openqa.selenium.Platform;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.net.URL;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

@Ignore
public abstract class AutomatedTestBase {
    protected static Path testRunParent;
    protected Path testRun;
    protected WebDriver driver;
    protected String baseUrl;
    private Properties properties;
    protected String password;
    protected String username;
    protected String testCase;

    public void setUp(String testCase, String serverUrl) throws Exception {
        this.testCase = testCase;
        properties = new Properties();
        String confFile = systemPropertyOrDefault("ui.test.properties", "firefox_linux.properties");
        if(confFile != null) {
            properties.load(ClassLoader.getSystemResourceAsStream(confFile));
            System.out.println("Loaded properties from " + confFile);
            System.out.println(properties);
        }
        password = getPropertyOrDefault("password", "ccis276");
        username = getPropertyOrDefault("username", "autotest");


        if (testRunParent == null) {
            testRunParent = Paths.get("AutomatedTests"
                    , new SimpleDateFormat("YYYY-MM-dd_HHmm").format(new Date())
                    );

            Files.createDirectories(testRunParent);
        }

        Path path = Paths.get(testRunParent.toFile().toString(), testCase);
        if(!Files.exists(path, LinkOption.NOFOLLOW_LINKS) ){
            Files.createDirectory(path);
        }
        testRun = path;

        String browserType = getPropertyOrDefault("browserType", "firefox");
        String platform = getPropertyOrDefault("platform", "LINUX");


        if(platform.equals("local")){
            System.setProperty("webdriver.chrome.driver", getPropertyOrDefault("chromedriver", "c:\\devtools\\chromedriver.exe"));
            System.setProperty("webdriver.gecko.driver", getPropertyOrDefault("geckodriver" , "C:\\devtools\\geckodriver.exe"));
            if(browserType.equals("chrome")){
                driver = new ChromeDriver();
            }
        } else {
            DesiredCapabilities capabilities =
                    new DesiredCapabilities(
                            browserType
                            , getPropertyOrDefault("browserVersion", "")
                            , Platform.valueOf(platform));

            capabilities.setCapability("marionette", Boolean.valueOf(getPropertyOrDefault("marionette", "false")));
            driver = new RemoteWebDriver(new URL("http://10.99.10.4:4444/wd/hub"), capabilities);
        }
        baseUrl = systemPropertyOrDefault("ui.test.url", serverUrl);
        driver.manage().timeouts().implicitlyWait(30, TimeUnit.SECONDS);
    }

    private String systemPropertyOrDefault(String property, String defaultValue) {
        String value = System.getProperty(property);
        return value!= null?value:defaultValue;
    }

    private String getPropertyOrDefault(String property, String defaultValue) {
        Object value = properties.get(property);
        return value!=null?value.toString():defaultValue;
    }

    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }


    public Path getTestRun() {
        return testRun;
    }

    public WebDriver getDriver() {
        return driver;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public String getPassword() {
        return password;
    }

    public String getUsername() {
        return username;
    }
}
