package ui.companysettings;

import framework.flow.NavigationFlow;
import framework.page.CompanySettingsPage;
import framework.page.LoginPage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import ui.AutomatedTestBase;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;

public class ShowAuditTrailForUser extends AutomatedTestBase {

  @Before
  public void setUp() throws Exception {
    super.setUp("ShowAuditTrailForUser", "https://development1.ccis.se");
  }

  @Test
  public void testAuditTrailUser() throws Exception {
    CompanySettingsPage companySettingsPage = NavigationFlow.navigateToAuditTrail(this);
    companySettingsPage.showAuditTrailFor("USER");
    assertThat(companySettingsPage.showsAuditTrailResults(), is(true));
    LoginPage page = companySettingsPage.logout();
    assertTrue(page.isDisplayed());

  }

  @After
  public void tearDown() {
    super.tearDown();
  }

}
