package ui.session;

import framework.Screenshot;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.openqa.selenium.By;
import ui.AutomatedTestBase;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

public class WaitForTimeoutInTvn extends AutomatedTestBase{

  @Before
  public void setUp() throws Exception {
    super.setUp("sessionTimeout", "https://development1.ccis.se");
  }

  @Test
  public void trigger5MinuteTimeoutInTvn() throws Exception {
    driver.get(baseUrl + "/login.action");
    Thread.sleep(1000);
    Screenshot.capture(driver, testRun, "timeout_start.png" );
    driver.findElement(By.id("username")).clear();
    driver.findElement(By.id("username")).sendKeys(username);
    driver.findElement(By.id("newPassword1")).clear();
    driver.findElement(By.id("newPassword1")).sendKeys(password);
    driver.findElement(By.id("login-button")).click();
    for (int second = 0;; second++) {
    	if (second >= 60) fail("timeout");
    	try { if ("CCIS Portal v2.7.6".equals(driver.getTitle())) break; } catch (Exception e) {}
    	Thread.sleep(1000);
    }
    Screenshot.capture(driver, testRun, "timeout_loggedin.png");

    driver.findElement(By.cssSelector("div.menuItem > img")).click();
    for (int second = 0;; second++) {
    	if (second >= 60) fail("timeout");
    	try { if ("Recent Shipments".equals(driver.findElement(By.cssSelector("td.boxTopCenter > table > tbody > tr > td")).getText())) break; } catch (Exception e) {}
    	Thread.sleep(1000);
    }

    Screenshot.capture(driver, testRun, "timeout_tvn_open.png");
    driver.findElement(By.linkText("SEARCH")).click();
    for (int second = 0;; second++) {
    	if (second >= 60) fail("timeout");
    	try { if ("Basic Search".equals(driver.findElement(By.id("ui-id-1")).getText())) break; } catch (Exception e) {}
    	Thread.sleep(1000);
    }
    System.out.println("Waiting 6 minutes:");
    for(int i = 0; i <6 ; i++) {
      Thread.sleep(1000 * 60);
      Screenshot.capture(driver, testRun, "timeout_wait_"+i+ "min.png");
    }

    driver.findElement(By.linkText("START")).click();
    Thread.sleep(1000);
    Screenshot.capture(driver, testRun, "timeout_logged_out.png");
    assertEquals("Login", driver.findElement(By.cssSelector("h2")).getText());
  }


  @Test
  public void keepaliveWhen5MinuteTimeoutInTvn() throws Exception {
    driver.get(baseUrl + "/login.action");
    Thread.sleep(1000);
    Screenshot.capture(driver, testRun,  "keepalive_start.png" );
    driver.findElement(By.id("newPassword1")).clear();
    driver.findElement(By.id("newPassword1")).sendKeys(password);
    driver.findElement(By.id("username")).clear();
    driver.findElement(By.id("username")).sendKeys(username);
    driver.findElement(By.id("login-button")).click();
    for (int second = 0;; second++) {
      if (second >= 60) fail("timeout");
      try { if ("CCIS Portal v2.7.6".equals(driver.getTitle())) break;
      }  catch (Exception e) {}
      Thread.sleep(1000);
    }

    Screenshot.capture(driver, testRun, "keepalive_tvn.png" );
    driver.findElement(By.cssSelector("div.menuItem > img")).click();
    for (int second = 0;; second++) {
      if (second >= 60) fail("timeout");
      try { if ("Recent Shipments".equals(driver.findElement(By.cssSelector("td.boxTopCenter > table > tbody > tr > td")).getText())) break; } catch (Exception e) {}
      Thread.sleep(1000);
    }

    System.out.println("Waiting 3 minutes:");
    Thread.sleep(1000*60*3);
    Screenshot.capture(driver, testRun, "keepalive_search.png" );
    driver.findElement(By.linkText("SEARCH")).click();
    System.out.println("Waiting 3 minutes:");
    Thread.sleep(1000*60*3);

    Screenshot.capture(driver, testRun, "keepalive_start.png" );
    driver.findElement(By.linkText("START")).click();
    System.out.println("Waiting 6 minutes:");
    for(int i = 0; i <6 ; i++) {
      Thread.sleep(1000 * 60);
      Screenshot.capture(driver, testRun, "keepalive_wait_"+i+"min.png" );
    }

    driver.findElement(By.linkText("SEARCH")).click();
    Thread.sleep(1000);
    Screenshot.capture(driver, testRun, "keepalive_loggedout.png" );
    assertEquals("Login", driver.findElement(By.cssSelector("h2")).getText());

  }
  @After
  public void tearDown() {
    super.tearDown();
  }

}
