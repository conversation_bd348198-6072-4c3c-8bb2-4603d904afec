package framework.flow;

import framework.page.CompanySettingsPage;
import framework.page.LoginPage;
import framework.page.StartPage;
import ui.AutomatedTestBase;

public class NavigationFlow {


    public static CompanySettingsPage navigateToAuditTrail(AutomatedTestBase test) {
        LoginPage loginPage = LoginPage.open(test);
        StartPage startPage = loginPage.login(test.getUsername(), test.getPassword());
        CompanySettingsPage companySettingsPage = startPage.openCompanySettings();
        companySettingsPage.openAdminTab();
        return companySettingsPage;
    }

}
