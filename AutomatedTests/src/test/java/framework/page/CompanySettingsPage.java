package framework.page;

import framework.Page;
import framework.Screenshot;
import org.openqa.selenium.WebDriver;

import java.nio.file.Path;

import static org.junit.Assert.assertTrue;
import static org.openqa.selenium.By.*;

public class CompanySettingsPage extends Page{

    public CompanySettingsPage(WebDriver driver, Path path, String url) {
        super(driver, path, url);
    }


    public CompanySettingsPage openAdminTab() {
        element(id("ui-id-8")).click();
        assertValue(id("ui-id-9"), "Password Recovery Approval");
        Screenshot.capture(driver, super.path, "adminTab.png");
        return this;
    }

    public CompanySettingsPage showAuditTrailFor(String option) throws InterruptedException {
        element(id("ui-id-10")).click();
        assertValue(id("ui-id-10"),"Audit Trail" );
        clickDropdownItem(option, "action");
        element(xpath("//input[@value='Apply']")).click();

        Thread.currentThread().sleep(8000);
        Screenshot.capture(driver, super.path, "auditTrail.png");
        return this;
    }

    public boolean showsAuditTrailResults() {
        waitForelement(id("audit_trail_result_info"), 10);
        assertTrue(isVisible(cssSelector("div.dataTables_scrollBody")));
        assertTrue(isVisible(linkText("1")));
        return isVisible(id("audit_trail_result_info"));
    }

}
