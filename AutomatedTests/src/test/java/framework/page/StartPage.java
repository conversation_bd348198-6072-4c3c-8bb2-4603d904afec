package framework.page;

import framework.Page;
import framework.Screenshot;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import java.nio.file.Path;

import static org.junit.Assert.assertEquals;

public class StartPage extends Page {


    public StartPage(WebDriver driver, Path path, String url) {
        super(driver, path, url );
        waitForelement(By.cssSelector("#managerLink > div.subMenuItem > img"), 5);
        String text =driver.getTitle();
        Screenshot.capture(driver, path, "startPage.png");
        assertEquals("CCIS Portal v2.7.6", text);

    }

    public CompanySettingsPage openCompanySettings() {
        element(By.cssSelector("#managerLink > div.subMenuItem > img")).click();
       return new CompanySettingsPage(driver, path, url);
    }

    public boolean verifyWebProfilesLink() {
        waitForelement(By.cssSelector("#ttwcProfilesLink > div.subMenuItem > img"), 60);
        Screenshot.capture(driver, path, "webProfilesLink.png");
        return element(By.cssSelector("#ttwcProfilesLink > div.subMenuItem > img")).isDisplayed();
    }
}
