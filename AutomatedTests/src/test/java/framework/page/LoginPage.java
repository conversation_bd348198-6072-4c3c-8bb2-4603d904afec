package framework.page;

import framework.Page;
import framework.Screenshot;
import org.openqa.selenium.WebDriver;
import ui.AutomatedTestBase;

import java.nio.file.Path;

import static org.openqa.selenium.By.id;

public class LoginPage extends Page {

    private LoginPage(WebDriver driver, Path path, String url) {
        super(driver,path, url);
    }


    public StartPage login(String username, String password){
        type(id("username"), username);
        type(id("newPassword1"), password);
        Screenshot.capture(driver, path, "login.png");
        element(id("login-button")).click();
        return new StartPage(driver, path, url);
    }


    public static LoginPage open(AutomatedTestBase test){
        return open(test.getDriver(), test.getTestRun(), test.getBaseUrl());

    }


    public boolean isDisplayed() {
        return isVisible(id("newPassword1"));
    }

    public static LoginPage open(WebDriver driver, Path path, String url) {
        LoginPage loginPage = new LoginPage(driver, path, url);
        loginPage.driver.get(url + "/login.action");
        loginPage.waitForelement(id("newPassword1"), 2);
        return loginPage;
    }
}
