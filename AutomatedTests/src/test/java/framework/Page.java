package framework;


import framework.page.LoginPage;
import org.junit.Ignore;
import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.Select;

import java.nio.file.Path;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;
import static org.openqa.selenium.By.id;

@Ignore
public abstract class Page {
    protected WebDriver driver;
    protected Path path;
    protected String url;


    public Page(WebDriver driver, Path path, String url) {
        this.driver = driver;
        this.path = path;
        this.url = url;
    }

    private  WebElement isElementPresent(By by) throws NoSuchElementException {
        return driver.findElement(by);
    }


    protected  void type(By password, String value) {
        WebElement newPassword1 = element(password);
        newPassword1.clear();
        newPassword1.sendKeys(value);
    }

    protected WebElement element(By element) {
        return waitForelement(element, 2);
    }

    protected WebElement waitForelement(By element, int seconds)  {
        for (int second = 0;; second++) {
            if (second >= seconds) {
                fail("timeout waiting for " +  element.toString());
            }
            try {
                return isElementPresent(element);
            } catch (NoSuchElementException ex) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    //ignored
                }
            }
        }
    }

    protected void assertValue(By id, String value){
        WebElement webElement = waitForelement(id, 10);
        assertThat(webElement.getText(), is(equalTo(value)));
    }


    protected boolean isVisible(By by) {
        return element(by).isDisplayed();
    }

    protected void clickDropdownItem(String option, String dropdown) {
        Select action = new Select(element(By.id(dropdown)));
        for(WebElement e : action.getOptions()){
            if(e.getText().equals(option)){
                e.click();
            }
        }
    }

    public LoginPage logout() {
        driver.findElement(By.cssSelector("div.logout > img")).click();
        waitForelement(id("newPassword1"), 2);
        Screenshot.capture(driver, path, "logout.png");
        return LoginPage.open(driver, path, url);
    }


}
