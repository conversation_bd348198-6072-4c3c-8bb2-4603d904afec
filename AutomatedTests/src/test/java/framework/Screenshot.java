package framework;

import org.junit.Ignore;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.remote.Augmenter;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;


@Ignore
public class Screenshot {

    public static void capture(WebDriver driver, Path testRun, String fileName){
        try {
            byte[] screenshotAs = ((TakesScreenshot) new Augmenter().augment(driver)).getScreenshotAs(OutputType.BYTES);
            new FileOutputStream(new File(testRun.toFile(), ZonedDateTime.now().format(DateTimeFormatter.ofPattern("HH_mm_ss_SSS_")) +fileName)).write(screenshotAs);
        } catch (IOException ex) {
            throw new IllegalStateException(ex);
        }
    }
}
