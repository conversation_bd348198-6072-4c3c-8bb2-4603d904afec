<project name="root" default="test" basedir=".">


    <!-- ==================== File and Directory Names ======================== -->

    <property name="build.home"    value="${basedir}/build"/>
    <property name="test.home"      value="${basedir}/src/test/java"/>
    <property name="test.resources"      value="${basedir}/src/test/resources"/>
    <property name="test.report"    value="${basedir}/build/report"/>
    <property name="test.classes"    value="${basedir}/build/testclasses"/>
    <!--  ==================== Compilation Control Options ==================== -->

    <property name="compile.debug"       value="true"/>
    <property name="compile.deprecation" value="false"/>
    <property name="compile.optimize"    value="false"/>
    <property name="compile.failonerror" value="true"/>

    <!-- ==================== Compilation Classpath =========================== -->

    <path id="test.classpath">
        <dirset dir="${test.classes}">
        </dirset>
        <dirset dir="${test.resources}">
        </dirset>
        <fileset dir="${basedir}/junit">
            <include name="**/*.jar" />
        </fileset>
    </path>


    <!-- ==================== Clean Target ==================================== -->

    <target name="clean" description="Delete old build and dist directories">
        <delete dir="${build.home}"/>
    </target>

    <target name="test">
        <mkdir  dir="${build.home}"/>
        <mkdir  dir="${test.classes}"/>
        <mkdir  dir="${test.report}"/>
        <!-- Compile Java classes as necessary -->
        <javac srcdir="${test.home}"
               destdir="${test.classes}"
               failonerror="true">
            <classpath refid="test.classpath"/>
            <include name="**/*.java" />
        </javac>
        <junit printsummary="yes" haltonfailure="no" logfailedtests="yes" failureproperty="test.failed" >
            <sysproperty key="ui.test.properties" value="${ui.test.properties}"/>
            <classpath refid="test.classpath"/>
            <formatter type="plain"/>
            <batchtest fork="yes" todir="${test.report}">
                <fileset dir="${test.home}">
                    <include name="**/ui/**/*.java"/>
                    <exclude name="**/ui/**/AutomatedTestBase.java"/>
                </fileset>
            </batchtest>
        </junit>
        <fail message="Test(s) failed, check test report" if="test.failed" />
    </target>

    <target name="testReport" >
        <mkdir dir="target" />
        <mkdir dir="target/report" />
        <junitreport todir="target/report">
            <fileset dir="../">
                <include name="build/report/TEST-*.xml"/>
            </fileset>
            <report todir="target/report"
                    format="frames">
            </report>
        </junitreport>
    </target>
</project>