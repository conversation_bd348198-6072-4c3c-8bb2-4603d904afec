
# End to end Testing

  - [Best practices](#best-practices)
  - [Setup](#setup)
  - [NPM scripts](#npm-scripts)


# Best Practices

-   [Use PascalCase for file names.](#file-naming-conventions)
-   [Organize files logically.](#directory-structure)
-   [Use the Page Object Model.](#page-object-model)
-   [Follow Arrange-Act-Assert in tests.](#writing-tests)
-   [**Use `data-testid` attributes as a primary selectors**](#selectors)
-   [**Use accessibility selectors in case data-testid isn't there** (e.g., `aria-label`, `role`).](#selectors)
-   [Configure tests globally.](#test-configuration)
-   [Name test scripts descriptively.](#writing-tests)

## File Naming Conventions

- Use PascalCase for all file names.

- Example: `LoginPage.ts`, `AuditTrailPage.ts`, `AuditTrail.spec.ts`.

## Directory Structure
Organize your tests and page objects in a logical directory structure:

e2e-tests/
├── PageObjects/
│ ├── LoginPage.ts
│ ├── AuditTrailPage.ts
│ └── AddingLoggerPage.ts
├── tests
│ ├── accessibility
│ ├── functional
│ 	├── Login.spec.ts
│ 	└── AuditTrail.spec.ts
│ └── performance
├── utils
└── libs

## Page Object Model

- Create a separate file for each page object.

- Each page object file should export a class that encapsulates interactions with the page.

  

### Example Page Object
```javascript

// tests/PageObjects/LoginPage.js

export class  LoginPage {

	constructor(page) {
		this.page = page;
		this.usernameInput = '[aria-label="Username"]';
		this.passwordInput = '[aria-label="Password"]';
		this.loginButton = '[data-test-id="login-button"]';
	}

	async  goto() {
		await  this.page.goto('https://example.com/login');
	}

	async  login(username, password) {
		await  this.page.fill(this.usernameInput, username);
		await  this.page.fill(this.passwordInput, password);
		await  this.page.click(this.loginButton);
	}
}
module.exports = { LoginPage };
```


### Base Test(libs/BaseTest.ts)

In our Playwright testing framework, we have configured a custom base test setup to extend Playwright's baseTest. This setup ensures consistency and reusability across our test suite by integrating essential pages and utilities.

Few of the custom fixtures are as follows:

#### Custom Test Fixtures

We extend the `baseTest` with the following custom fixtures:

1.  **`loginPage`**: Initializes the `LoginPage` class for handling login-related actions within tests.
2.  **`uploadShipmentPage`**: Initializes the `UploadShipmentPage` class, managing operations related to shipment uploads.
3.  **`addingLoggerPage`**: Initializes the `AddingLoggerPage` class for handling logger additions.
4.  **`login`**: A function that automates the login process, leveraging the `LoginPage` class to authenticate users as needed.
5.  **`makeAxeBuilder`**: Integrates the `AxeBuilder` for accessibility testing, pre-configured with relevant WCAG standards and exclusions for known issues.

## Writing Tests

-   Use descriptive test names.
-   Follow the Arrange-Act-Assert pattern for structuring tests.
-   Utilize the page object methods for interactions.

### Example Test
```javascript
// tests/LoginPage.spec.js
test('should display an error for invalid login', async ({ loginPage }) => {
    // Arrange
    await loginPage.goto();

    // Act
    await loginPage.login('invalidUser', 'invalidPassword');

    // Assert
    await loginPage.expectLoginError();
});
```

## Assertions

-   Use Playwright’s built-in assertions for better readability and reliability.
-   Example: `await expect(locator).toBeVisible();`

## Selectors

-   **Preference 1: Accessibility Selectors**
    -   Use accessible names and roles for selectors to improve both accessibility and test reliability.
    -   Examples: `aria-label`, `aria-labelledby`, `role`, etc.

### Example Selectors Using Data Attributes for Testing
```javascript
const loginButton = await page.getByTestId('login-button');
const usernameInput = await page.getByTestId('username-input');
```

-   **Preference 2: Selectors Using Accessibility Attributes**
    ```javascript
        const loginButton = await page.getByLabel('Login Button');
        const usernameInput = await page.getByLabel('Username Input');
    ```

### Combining Accessibility and Data Attributes
```javascript
<button data-test-id="submit-button" aria-label="Submit">Submit</button>
```

## Test Configuration

-   Use a global configuration file `playwright.config.js` to set up common settings.


## Setup
- Create a .env file in the root of the end to end testing project
- Add the following properties to the .env file
```plaintext 
BASE_URL="clinical-dev.tssgeneral.com/login.action"
USERNAME="YOUR_USER_NAME" 
PASSWORD="PASSWORD" 
NODE_ENV="dev" //optinal: only required for the local react setup
```  
## NPM Scripts
Below is a list of available npm scripts with explanations on their usage:
### `test`
```bash
npm test
```
Runs the full suite of Playwright tests. This command executes all the tests defined in the project using Playwright's default test runner.

### `test single file`
```bash
npm test ./TEST_FILE_PATH
```
Runs the Playwright tests on a single file. 

### `codegen`
```bash
`npm run codegen` 
```
Launches Playwright's code generator for the specified URL (`clinical-dev.tssgeneral.com/`). This tool allows you to interactively record your actions in a browser, generating code that can be used in your tests.

### `codegen:dev`
```bash
`npm run codegen:dev` 
```
Launches Playwright's code generator for the local instance

### `debug`

```bash
`npm run debug` 
```
Runs Playwright tests in debug mode. This command opens the Playwright Inspector, allowing you to step through your tests and see what's happening in the browser in real-time. Useful for diagnosing and fixing test issues.

### `trace`
```bash
`npm run trace` 
```
Runs Playwright tests with tracing enabled. Tracing captures detailed information about the execution of your tests, which you can later review to diagnose issues. The trace files can be opened to see step-by-step execution.

### `lint`

```bash
`npm run lint` 
```
Executes ESLint across the codebase to analyze code for potential errors and enforce code style guidelines. Use this command to ensure your code follows the project's linting rules.

### `prettier:check`
```bash
`npm run prettier:check` 
```
Checks the formatting of the codebase using Prettier. This script will analyze your files and report any that do not conform to the expected format.

### `prettier:fix`

```bash
`npm run prettier:fix` 
```
Automatically formats the codebase according to Prettier's rules. This script modifies files to ensure consistent styling across the project.

### `report`
```bash
`npm run report` 
```

Runs the full suite of Playwright tests and generates an HTML report. After the tests complete, the HTML report is automatically opened in your default browser for review.

### `test:accessibility`
```bash
`npm run test:accessibility` 
```

Runs the accessibility tests located in the `tests/accessibility/` directory, specifically using the Google Chrome browser. This ensures your application meets accessibility standards.

### `test:lighthouse`
```bash
`npm run test:lighthouse` 
```
Executes performance tests using Lighthouse via Playwright. This script runs in a continuous integration (CI) environment, ensuring the performance of your application meets the desired standards.


