import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://tss460dev.tssgeneral.com/login.action');
  await page.locator('#username').click();
  await page.locator('#username').fill('autotester');
  await page.locator('#username').press('Tab');
  await page.locator('#newPassword1').fill('dLlrox&UDQ5!5TPt');
  await page.getByRole('button', { name: 'Login', exact: true }).click();
  await page.getByText('ADMINISTRATION').click();
  await page.getByText('Loggers').click();
  await page.getByRole('link', { name: 'Overview' }).click();
  await page.getByRole('link', { name: 'Add' }).click();
});