const { expect } = require('@playwright/test');
const moment = require('moment');
import dotenv from "dotenv";
import path from "path";

dotenv.config({
	path: path.resolve(__dirname, '../../.env'),
	override: true,
	debug: true
})

exports.UploadingStoragePage = class UploadingStoragePage {

	constructor(page) {
		this.page = page
		this.uploadMenu = page.getByText('UPLOAD', { exact: true });
		this.storageMenu = page.getByText('Storage', { exact: true });
		this.manualMenu = page.getByRole('link', { name: 'Manual', exact: true });
        this.selectLogger = page.getByRole('link', { name: 'B1E1test Other test ACE-CL-007 / 3003 Controlled Room Temperature 2023-03-27 Active' });
        this.selectPeriod = page.locator('._12tkckys');
        this.selectDate = page.getByText('15');
        this.selectTempInRange = page.locator('#temp-in-range');
        this.selectGap = page.locator('#no-gap');
        this.uploadLogger = page.getByText('Browse', { exact: true });
        this.inputFile = page.getByLabel('Browse');
        this.confirmButton = page.getByRole('button', { name: 'Confirm' });
        this.commitYesButton = page.getByRole('button', { name: 'Yes' });
	}

	async navigateToUploadMenu() {
		await this.uploadMenu.click();
		await this.storageMenu.click();
		await this.manualMenu.click();
	}

	async selectLoggerToUploadData() {
		await this.selectLogger.click();
	}

	async setPeriodTo() {
		await this.selectPeriod.click();
        await this.selectDate.click();
	}

	async setTempInRange() {
		await this.setTempInRange.click();
	}

    async setGap() {
		await this.selectGap.click();
	}

    async uplodLoggerFile() {
		await this.uploadLogger.click();
	}

	async selectFile() {
		await this.inputFile.setInputFiles('d21b3e42-5609-43ae-a911-aab1a414c507.pdf');
	}

	async confirmLogger() {
		await this.confirmLoggerButton.click();
	}

    async commitYes() {
		await this.commitYesButton.click();
	}

	async uploadingShipment() {
		await this.navigateToUploadMenu();
		await this.selectLoggerToUploadData();
		await this.setPeriodTo();
		await this.setInputFile();
		await this.setTempInRange();
		await this.setGap();
		await this.uplodLoggerFile();
        await this.selectFile();
		await this.confirmLogger();
		await this.commitYes();
	}
}