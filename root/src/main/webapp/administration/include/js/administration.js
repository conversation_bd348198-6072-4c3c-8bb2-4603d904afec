var Administration = {
	srSelectUser : function(info) {
		if (info.userId == "") {
            OkDialog.showDialog("No user selected!","");
			return false;
		}
		var url = contextRoot + "shipmentRights.action?loadUser&user.id=" + info.userId;
        TSS.loadTab(url,'Panel','Error loading shipment rights');
		return true;
	},
	srAdd : function(btn) {
		var form = btn.form;
		var sel = form["shipmentRight.officeId"];
		var idx = sel.selectedIndex;
		if (idx < 0 || sel.value == null) {
			return false;
		}
		TSS.postFormWithNotification(form, 'addShipmentRight', 'Add shipment rights', 'Shipment rights added','Error adding shipment rights');

		sel[idx] = null;
		if (sel.options.length < 1) {
			btn.disabled = true;
		}

		return false;
	},
	report : function(a,url) {
        var title = (a.href.indexOf("edit") > -1 ? "Edit" : "Add") + " Report";
        var $dialog = $j("#modualReportDialog").dialog({
            title: title,
            autoOpen: false,
            height: 500,
            width: 920,
            modal: true,
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#modualReportForm').load(url);

            },
            beforeClose: function(event, ui) {
                $j('#modualReportForm').empty();
            }
        });

        $dialog.dialog('open');
	},
    reloadReport: function(){
        $j('#tabs').tabs('load', 8);
    },
	search : function(l) {
        $j("#searchFirstResult").val(Number($j("#searchFirstResult").val())+l);
        TSS.postFormWithNotification($j("#searchForm").get(0),"performSearch");
	},
    openUserListDialog : function(myform) {
        var str = myform.serialize();
        var urlToLoad =  contextRoot + "company.action?searchForUser&" + str;
        var $dialog = $j("#DialogUserList").dialog({
            autoOpen: false,
            height: 600,
            width: 1200,
            modal: false,
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#resultUserList').load(urlToLoad);
                // Save the url in case we need to reload the page
                $j('#resultUserList').data('urlToLoad', urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#resultUserList').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
    openOfficeUserListDialog : function(officeId, orderBy, page) {
        orderBy = orderBy ? orderBy : "";
        page = page ? page : 0;
        var urlToLoad =  contextRoot + "company.action?listOfficeUsers&orderBy=" + orderBy + "&office.id=" + officeId + "&page=" + page;

        if ($j("#DialogUserList").is(':data(dialog)')) {
            $j("#DialogUserList").dialog('close');
        }
        if(orderBy){
            $j("#DialogUserList").dialog('close');
        }

        var $dialog = $j("#DialogUserList").dialog({
            autoOpen: false,
            height: 600,
            width: 1200,
            modal: false,
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#resultUserList').load(urlToLoad);
                // Save the url in case we need to reload the page
                $j('#resultUserList').data('urlToLoad', urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#resultUserList').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
    openEditUserDialog : function(userId) {
        var urlToLoad =  contextRoot + "company.action?editUser&user.id=" + userId;
        var $dialog = $j("#DialogUser").dialog({
            autoOpen: false,
            height: 400,
            width: 1000,
            modal: false,
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#userForm').load(urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#userForm').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
    closeEditUserDialog : function() {
        $j("#DialogUser").dialog('close');
        return false;
    }
};

var User = {
	search : function(form) {
		var div = TSS.createCentralizedDiv( {
			id : "users",
			title : "User List",
			url : contextRoot + "company.action?searchForUser&" + Form.serialize(form)
		});
		return false;
	},
	manage : function(id, orderBy) {
		orderBy = orderBy ? orderBy : "";
		var div = TSS.createCentralizedDiv( {
			id : "users",
			title : "User List",
			url : "company.action?listOfficeUsers&orderBy=" + orderBy + "&office.id=" + id
		});
	},
    openCreateUserDialog : function(companyId, officeId) {
        var urlToLoad =  contextRoot + "company.action?newUser&user.companyId=" + companyId + "&user.officeId=" + officeId;
        var $dialog = $j("#DialogUser").dialog({
            autoOpen: false,
            modal: true,
            width: 800,
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#userForm').load(urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#userForm').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
    closeCreateUserDialog : function() {
        $j("#DialogUser").dialog('close');
        return false;
    },
	edit : function(a, title) {
		title = title ? title : "Edit User";
		var div = TSS.createCentralizedDiv( {
			anchor : a,
			title : title
		});
		return false;
	},
	save : function(form) {
		var errMsg = "";
		if (form["user.firstname"].value == "") {
			errMsg += "Users first name must be entered!\n";
		}

		if (form["user.lastname"].value == "") {
			errMsg += "Users last name must be entered!\n";
		}

		if (form["user.username"].value == "") {
			errMsg += "Users user name must be entered!\n";
		}

		if (form["user.email"].value == "") {
			errMsg += "Users E-Mail must be entered!\n";
		}

		if (form["user.tvnId"].value == "") {
			errMsg += "Users TVN ID must be entered!\n";
		}

		if (errMsg != "") {
			alert("Please fix the following errors:\n\n" + errMsg);
			return false;
		} else {
			return TSS.postForm2(form, "saveUser", User.reload);
		}
	},
	reload : function() {
        var url = $j('#resultUserList').data('urlToLoad');
        $j('#resultUserList').load(url);
	}
};

var Office = {
    openEditOfficeDialog : function(officeId) {
        var urlToLoad =  contextRoot + "/company.action?editOffice&office.id=" + officeId;
        var $dialog = $j("#DialogCreateOffice").dialog({
            autoOpen: false,
            modal: true,
            width: 800,
            position: 'center',
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#createOfficeForm').load(urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#createOfficeForm').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
    openCreateOfficeDialog : function(companyId) {
        var urlToLoad =  contextRoot + "/company.action?newOffice&company.id=" + companyId;
        var $dialog = $j("#DialogCreateOffice").dialog({
            autoOpen: false,
            modal: true,
            width: 800,
            position: 'top',
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#createOfficeForm').load(urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#createOfficeForm').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
	list : function(id, orderBy) {
        TSS.openLink(contextRoot + "company.action?loadCompany&company.id=" + id + "&orderBy=" + orderBy,'','offices'+ id);
	},
	save : function(form) {
		if (form["office.name"].value == "") {
			alert("An office name is mandatory!");
		} else {
			var myAjax = new Ajax.Request(form.action + "?saveOffice", {
				method : 'post',
				postBody : Form.serialize(form),
				onComplete : Office.saveDone,
				evalScripts : true
			});
		}
		return false;
	},
	saveDone : function(res) {
		var text = res.responseText;
		if (text.indexOf("editor_header") > -1) {
			$("createOfficeForm").innerHTML = text;
		} else {
			var id = $("officeEditor")["office.companyId"].value;
			$("offices" + id).innerHTML = text;
            $j("#DialogCreateOffice").dialog('close');
		}
	}
};

var Company = {
    openCreateCompanyDialog : function() {
        var urlToLoad =  contextRoot + "company/company_form.jsp";
        var $dialog = $j("#DialogEditCompany").dialog({
            autoOpen: false,
            modal: true,
            position: 'top',
            width: 800,
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#editCompanyForm').load(urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#editCompanyForm').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
    openEditCompanyDialog : function(companyId) {
        var urlToLoad =  contextRoot + "/company.action?editCompany&company.id=" + companyId;
        var $dialog = $j("#DialogEditCompany").dialog({
            autoOpen: false,
            modal: true,
            position: 'top',
            width: 800,
            resizable: true,
            draggable: true,
            open: function(event, ui) {
                $j('#editCompanyForm').load(urlToLoad);
            },
            beforeClose: function(event, ui) {
                $j('#editCompanyForm').empty();
            }
        });

        $dialog.dialog('open');
        return false;
    },
	save : function(form) {
		if (form["company.name"].value == "") {
            var errorTitle = "Save company";
            if($j("#companyId").val()){
                errorTitle = "Update company";
            }
            Notification.showErrorNotification(errorTitle, "A company name is mandatory!");
		} else {
            if($j("#companyId").val()){
                TSS.postFormWithNotification($j("#companyFormEdit").get(0), 'saveCompany', 'Update company', 'Company updated', 'Error updating company',Company.saveDone);
            } else {
                TSS.postFormWithNotification($j("#companyFormEdit").get(0), 'saveCompany', 'Save company', 'Company saved', 'Error saving company',Company.saveDone);
            }
		}
		return false;
	},
	saveDone : function(res) {
		setTimeout(Process.hide, 100);
        $j("#DialogEditCompany").dialog('close');
        // Get selected tab index
        var current_index = $j("#tabs").tabs("option","active");
        $j("#tabs").tabs('load',current_index);
	},
	open : function(id, orderBy) {
        $j("#indicator" + id).html(($j('#cp_company'+ id).is(':visible')) ? '+' : '-');
        $j( "#company" +id).toggle();
        if ($j('#company'+ id).is(':visible')) {
            Office.list(id, orderBy);
        }
	},
    deleteSplashLogo : function(button, id) {
        // only remove the logo from the form
        $j(button).parent().html("<input type=\"file\" name=\"splashLogo\" />");
        // set to remove the logo when the form is submitted.
        $j("#deleteSplashLogo").attr("value", true);
    }
};

var Country = {
	manage : function(id, name, code, count) {
		var url = contextRoot + "country/country_edit.jsp?id=" + id + "&name=" + name + "&code=" + code + "&count=" + (count + 1);
        TSS.openLink(url,'',"country" + id);
	},
	update : function(id, count) {
		var name = $j("#name" + id).val();
		var code = $j("#code" + id).val();
		var url = contextRoot + "country.action?saveCountry&country.id=" + id + "&country.name=" + name + "&country.code=" + code + "&country.count=" + (count + 1)
				+ "&_sourcePage=/country/country_list.jsp";

        // Get selected tab index
        var $tabs = $j('#tabs').tabs();
        var selected = 'ui-tabs-' + ($tabs.tabs('option', 'active') + 1);
        TSS.openLink(url,'',selected);
	}
};

var Tooltip = {
    manage : function(name) {
        var url = contextRoot + "tooltips.action?editTooltip&name=" + name;
        TSS.openLink(url,'',"tooltip_" + name);
    },
    update : function(name) {
        var heading = $j("#"+name + "_heading").val();
        var body = $j("#"+name + "_body").val();
        var url = contextRoot + "/tooltips.action?saveTooltip&name=" + name + "&header=" + heading + "&body=" + body
            + "&_sourcePage=/tooltips/tooltips_list.jsp";

        // Get selected tab index
        var $tabs = $j('#tabs').tabs();
        var selected = 'ui-tabs-' + ($tabs.tabs('option', 'active') + 1);
        TSS.openLink(url,'',selected);
    }
};

var AuditTrail = {
    statuses : function(action) {
        $j.ajax({
            type: 'POST',
            url: contextRoot +"AuditTrail.action?loadStatuses&action=" + action,
            success: function(response){
                $j('#statusRow').empty();
                $j('#statusRow').append(response);
            }
        });
    },
	load : function(page, xls) {
		xls = xls ? true : false;
		page = !page ? 0 : page;

		var action = $j("#action").val();
		var status = $j("#status").val();
		var user = $j("#audittrailuser").val();
		var startDate = $j("#startDate").val();
        var stopDate = $j("#stopDate").val();
        if($j("#stopDate").val().length > 0){
            stopDate += " 23:59:59";
        }

		var url = contextRoot + "AuditTrail.action?fetchAuditTrailJSON&xls=" + xls + "&user.id=" + user + "&startDate=" + startDate + "&stopDate=" + stopDate;
        url += "&action=" + action;
        if (status && status != "0") {
            url += "&status=" + status;
        }
        if (!xls) {
            AuditTrail.fetchAuditTrailJSON(url);
        } else {
            top.document.location = url;
        }
	},
    fetchAuditTrailJSON: function (action) {
        action += "&ts=" + (new Date()).getTime();
        if ($j.fn.dataTable.isDataTable('#audit_trail_result_admin')) {
            var tableY = $j('#audit_trail_result_admin').DataTable();
            tableY.destroy();
            $j('#audit_trail_result_admin tbody').off('click');
        }

        var table = $j('#audit_trail_result_admin').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": action,
            "order": [[ 1, "desc" ]],
            "columns": [
                {
                    "class": 'details-control',
                    "sortable": false,
                    "data": null,
                    "defaultContent": ''
                },
                {"data": "date", "visible": true, "sortable": true, name: "date"},
                {"data": "user", "visible": true, "sortable": true, name: "user"},
                {"data": "company", "visible": true, "sortable": true, name: "company"},
                {"data": "office", "visible": true, "sortable": true, name: "office"},
                {"data": "action", "visible": true, "sortable": true, name: "action"},
                {"data": "status", "visible": true, "sortable": true, name: "status"}
            ],

            "deferRender": true,
            "scrollX": false,
            "scrollY": "600px",
            "scrollCollapse": true,
            "paging": true,
            "searching": true,
            searchDelay: 600,
            "displayLength": 100,
            "dom": '<"left"f><"right"B>rtip',
            "emptyTable": "No data available in table",
            "language": {
                "processing": "<i class='fa fa-4x fa-spinner fa-spin'></i>",
                search: "_INPUT_"
            },
            buttons: [
                {
                    extend: 'collection',
                    text: "Export <i class='fa fa-chevron-down'></i>",
                    buttons: [
                        {
                            text: "<i class='fa fa-file-excel-o'></i> Export to Excel",
                            action: function ( e, dt, node, config ) {
                                AuditTrail.exportToXLS();
                            }
                        },
                        {
                            text: "<i class='fa fa-print'></i> Print page",
                            extend: 'print',
                            autoPrint: true,
                            message : '',
                            title : '',
                            exportOptions: {
                                columns: ':visible',
                            }
                        }
                    ]
                }
            ],
            "preDrawCallback": function( settings ) {
                $j('.dataTables_filter input').addClass('search-query');
                $j('.dataTables_filter input').width("300px");
                $j('.dataTables_filter input').attr('placeholder', 'Filter the search result....');
                var $prepend = $j( "<span id='prepend'>Filter: </span>");
                $j('#prepend').remove();
                $j('.dataTables_filter label').prepend($prepend);
            }
        });
        // Set table to min height (scrollY)
        $j('#audit_trail_result_admin').on( 'init.dt', function () {
            if($j('.dataTables_scrollBody').height() < 600 ){
                $j('.dataTables_scrollBody').height(600);
            }
        } );
        // Add event listener for opening and closing details
        $j('#audit_trail_result_admin tbody').on('click', 'td.details-control', function () {
            var tr = $j(this).closest('tr');
            var row = table.row(tr);
            if (row.child.isShown()) {
                // This row is already open - close it
                row.child.hide();
                tr.removeClass('shown');
            } else {
                // Open this row (the format() function would return the data to be shown)
                row.child(AuditTrail.formatAudittrailDetails(row.data())).show();
                tr.addClass('shown');
                $j("td[colspan='7']").addClass('customDataTable-hover-forced');
            }
        });

    },
    formatAudittrailDetails: function (d) {
        if (d.valuechange[0]) {
            return  '<div>' +
                '<div style="background-color: #f5f5f5">' +
                '<div class="clear" style="padding-bottom: 0px;">' +
                '<div class="left"><b>Description:&nbsp;</b></div><div class="left">' + d.description + '</div>' +
                '</div>' +
                '</div>' +
                '<div class="clear">&nbsp;</div>' +
                '<div style="padding-left: 0px;">' +
                '<div>' +
                '<div class="left header block_larger">Field</div>' +
                '<div class="left header block_huge">Value Before</div>' +
                '<div class="left header block_huge">Value After</div>' +
                '</div>' +
                '<div class="clear"></div>' +
                '<div class="clear">' +
                '<div class="left block_larger">' + d.valuechange[0].FieldName + '</div>' +
                '<div class="left block_huge">' + d.valuechange[0].ValueBefore + '&nbsp;</div>' +
                '<div class="left block_huge">' + d.valuechange[0].ValueAfter + '&nbsp;</div>&nbsp;' +
                '</div>' +
                '</div>';

        } else {
            return  '<div>' +
                '<div style="background-color: #f5f5f5">' +
                '<div class="clear" style="padding-bottom: 0px;">' +
                '<div class="left"><b>Description:&nbsp;</b></div><div class="left">' + d.description + '</div>' +
                '</div>' +
                '</div>' +
                '<div class="clear">&nbsp;</div>' +
                '</div>';
        }
    },
    exportToXLS : function(){

        var table = $j('#audit_trail_result_admin').DataTable();
        var arr = table.order();
        var action = $j("#action").val();
        var status = $j("#status").val();
        var user = $j("#audittrailuser").val();
        var startDate = $j("#startDate").val();
        var stopDate = $j("#stopDate").val();
        if($j("#stopDate").val().length > 0){
            stopDate += " 23:59:59";
        }
        var filterVal = $j(".search-query").val();

        var url = "/administration/AuditTrail.action?fetchAuditTrailJSON" +
            "&user.id=" + user + "" +
            "&startDate=" + startDate + "" +
            "&stopDate=" + stopDate +
            "&action=" + action +
            "&status=" + status +
            "&searchString=" + filterVal+
            "&xls=true";

        top.document.location = url +"&orderBy="+arr[0][0]+"&order="+arr[0][1];

    },
    clear: function(){
        $j("#action").val("-1")
        $j("#statusRow").empty();
        $j("#selected_user").empty();
        $j("#selected_user").text("[NO USER SELECTED!]");
        $j("#audittrailuser").val('');
        DatePicker.reset("startDate","stopDate",1,6);
    }
};
var RegisterMoog = {
    
    deviceName :"MultiSensor",
    
    validate : function(type){
        var ok = false;
        if ($j("#unitSerialNo").val().length < 1) {
            Notification.showErrorNotification(type+' '+RegisterMoog.deviceName+'  device', "Missing value: unitSerialNo");
        } else if ($j("#model").val().length  < 1) {
            Notification.showErrorNotification(type+' '+RegisterMoog.deviceName+'  device', "Missing value: model");
        } else if ($j("#family").val().length  < 1) {
            Notification.showErrorNotification(type+' '+RegisterMoog.deviceName+'  device', "Missing value: family");
        } else if ($j("#description").val().length  < 1) {
            Notification.showErrorNotification(type+' '+RegisterMoog.deviceName+'  device', "Missing value: description");
        } else if ($j("#barcode").val().length  < 1) {
            Notification.showErrorNotification(type+' '+RegisterMoog.deviceName+'  device', "Missing value: barcode");
        } else if ($j('#companyId').prop("selectedIndex")  < 1) {
            Notification.showErrorNotification(type+' '+RegisterMoog.deviceName+'  device', "Missing value: company");
        } else {
            Notification.removeAll();
            ok = true;
        }
        return ok;
    },
    save : function(form){
       if(RegisterMoog.validate("Save")){
            if($j("#active").is(':checked')){
                $j("#available").val('1')
            } else {
                $j("#available").val('0')
            }
            TSS.postFormWithNotification($j("#registerMoog").get(0), 'save', 'Save '+RegisterMoog.deviceName+' device', RegisterMoog.deviceName+' device saved', 'Error saving '+RegisterMoog.deviceName+' device');
        }
        return false;
    },
    update : function(form){
        if(RegisterMoog.validate("Update")) {
            if ($j("#active").is(':checked')) {
                $j("#available").val('1')
            } else {
                $j("#available").val('0')
            }
            TSS.postFormWithNotification($j("#registerMoog").get(0), 'update', 'Update '+RegisterMoog.deviceName+' device', RegisterMoog.deviceName+' device updated', 'Error updating '+RegisterMoog.deviceName+' device');
        }
        return false;
    },
    edit : function(form){
        TSS.postForm($j("#listMoogForm").get(0),'edit','moogForm');
    }
};
var ConfigurationReport = {

  requestFileToDownload: function(url, filename, contentType ) {
    var deferredObject = $j.Deferred();
    var xHttp = new XMLHttpRequest();
    xHttp.onreadystatechange = function() {
      var a;
      if (xHttp.readyState === 4 && xHttp.status === 200) {
        a = document.createElement('a');
        a.href = window.URL.createObjectURL(xHttp.response);
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        deferredObject.resolve();
      }
      deferredObject.resolve();
    };
    xHttp.open("GET", url);
    xHttp.setRequestHeader("Content-Type", contentType);
    xHttp.responseType = 'blob';
    xHttp.send();
    return deferredObject.promise();
  },
  generateExcelReport: function () {
    var addHeader = $j("#includeXlsxHeader").is(':checked');
    var deferredObject = $j.Deferred();
    var url = "/administration/configurationReport.action?generateExcelReport&addXlsxHeaderField="+addHeader;
    ConfigurationReport.requestFileToDownload( url, 'configuration_report.xlsx', "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
      .done( function (  ) {
        deferredObject.resolve();
      })
      .fail( function () {
        deferredObject.reject("generateExcelReport: failed");
      });
    return deferredObject.promise();
  },
  generatePDFReport: function () {
    var deferredObject = $j.Deferred();
    var url = "/administration/configurationReport.action?generatePDFReport";
    ConfigurationReport.requestFileToDownload( url, 'configuration_report.pdf', "application/octet-stream")
      .done( function (  ) {
        deferredObject.resolve();
      })
      .fail( function () {
        deferredObject.reject("generatePDFReport: failed");
      });
    return deferredObject.promise();

  },
  downloadConfigurationReports: function () {
    var deferredObject = $j.Deferred();
    var includeXlsx = $j("#includeXlsx").is(':checked');
    var includePdf = $j("#includePdf").is(':checked');
    if( includeXlsx && includePdf ){
      ConfigurationReport.generateExcelReport()
        .then( ConfigurationReport.generatePDFReport )
        .done(function() {
          deferredObject.resolve();
        })
        .fail(function (errorResponse) {
          deferredObject.reject(errorResponse);
        })
    } else if( includeXlsx ){
      ConfigurationReport.generateExcelReport()
        .done(function() {
          deferredObject.resolve();
        })
        .fail(function (errorResponse) {
          deferredObject.reject(errorResponse);
        })
    } else if( includePdf ){
      ConfigurationReport.generatePDFReport()
        .done(function() {
          deferredObject.resolve();
        })
        .fail(function (errorResponse) {
          deferredObject.reject(errorResponse);
        })
    } else {
      deferredObject.resolve();
    }

    return deferredObject.promise();
  }
};
