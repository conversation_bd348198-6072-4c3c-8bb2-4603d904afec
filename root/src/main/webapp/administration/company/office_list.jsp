<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/administration/company.action" id="company"/>

<table width="100%">
	<tr>
		<th onmouseover="this.style.cursor='pointer';" width="40px" onclick="Office.list(${company.company.id}, 'tvnId');">TVN Id</th>
		<th width="200px" onmouseover="this.style.cursor='pointer';" onclick="Office.list(${company.company.id}, 'name');">Office name</th>
		<th onmouseover="this.style.cursor='pointer';" onclick="Office.list(${company.company.id}, 'country.name');">Country</th>
		<th onmouseover="this.style.cursor='pointer';" onclick="Office.list(${company.company.id}, 'state');">Active</th>
		<th width="130px">&nbsp;</th>
	</tr>
	<c:forEach var="office" items="${company.offices}" varStatus="loop"><%--CCIS-2832--%>
		<tr class="row${loop.index % 2}">
			<td style="cursor:pointer;" onclick="Administration.openOfficeUserListDialog(${office.id});">${office.tvnId}</td>
			<td style="cursor:pointer;" onclick="Administration.openOfficeUserListDialog(${office.id});">${office.name}</td>
			<td style="cursor:pointer;" onclick="Administration.openOfficeUserListDialog(${office.id});">${office.countryName}</td>
			<td style="cursor:pointer;" onclick="Administration.openOfficeUserListDialog(${office.id});">${office.active ? "Y" : "N"}</td>
			<td align="right">
				<input type="button" value="Edit" onclick="Office.openEditOfficeDialog(${office.id});"/>
				<input type="button" value="Add user" onclick="User.openCreateUserDialog(${office.companyId}, ${office.id});"/>
			</td>
		</tr>
		<tr id="office${office.id}" style="display:none;">
			<td colspan="7" class="subcontent">
				<div id="users${office.id}">
				</div>
			</td>
		</tr>
	</c:forEach>
</table>
<script>
	TSS.removeDiv("officeForm");
</script>
