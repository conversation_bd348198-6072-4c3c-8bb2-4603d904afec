<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/administration/company.action" id="company"/>

<stripes:form action="/administration/company.action" target="createOfficeForm" id="officeEditor" onsubmit="return Office.save(this);">
	<stripes:hidden name="office.id"/>
	<stripes:hidden name="office.companyId"/>
	<stripes:hidden name="orderBy"/>

	<table width="100%">
		<tr>
			<td class="inputLabel" align="right">Office name:</td>
			<td><stripes:text name="office.name" style="width:300px;" maxlength="50" /></td>
		</tr>
		<tr>
			<td class="inputLabel" align="right">Status:</td>
			<td>
				<stripes:radio value="true" id="stateActive" name="office.active"/><label for="stateActive">Active</label>&nbsp;
				<stripes:radio value="false" id="stateInactive" name="office.active"/><label for="stateInactive">Disabled</label>&nbsp;
			</td>
		</tr>
		<tr style="${company.office.id != null ? 'display:none;' : ''}">
			<td class="inputLabel" align="right">TVN Id:</td>
			<td><stripes:text name="office.tvnId" style="width:300px;" maxlength="3"/></td>
		</tr>
		<tr>
			<td class="inputLabel" align="right">Country:</td>
			<td>
				<stripes:select name="office.countryId" style="width:300px;">
					<stripes:options-collection collection="${company.countries}" label="name" value="id"/>
				</stripes:select>
			</td>
		</tr>
		<tr>
			<td colspan="2">
				&nbsp;<stripes:submit name="saveOffice" value="Save" class="large_button"/>
			</td>
		</tr>
	</table>
</stripes:form>
<stripes:errors/>