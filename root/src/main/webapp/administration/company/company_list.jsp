<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/administration/company.action" id="company"/>

<div id="search">
	<%@include file="user_search.jsp"%>
</div>

<input type="button" value="Add Company" onclick="Company.openCreateCompanyDialog();" class="large_button"/>
<table width="900">
	<c:forEach var="company" items="${company.companies}" varStatus="loop">
		<tr>
			<td class="label" align="center" style="width: 12px;" id="indicator${company.id}">+</td>
			<td class="label" style="cursor:pointer" onclick="Company.open(${company.id}, 'name');">
				<div class="left" style="width: 350px;">[${company.tvnId}] ${company.name}</div>
				<div align="right">
					${company.country.name}
				</div>
			</td>
			<td align="right" style="width: 150px;">
				<input type="button" value="Edit" onclick="Company.openEditCompanyDialog(${company.id});"/>
				<input type="button" value="Add Office" onclick="Office.openCreateOfficeDialog(${company.id});"/>
			</td>
		</tr>
		<tr id="company${company.id}" style="display:none;">
			<td>&nbsp;</td>
			<td id="offices${company.id}" class="subcontent"></td>
			<td>&nbsp;</td>
		</tr>
	</c:forEach>
</table>
<script>
	TSS.removeDiv("companyForm");
</script>
