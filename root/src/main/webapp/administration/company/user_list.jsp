<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/administration/company.action" id="company"/>
<div id="companyUserList">
<c:choose>
	<c:when test="${fn:length(company.users) == 0}">
		No users found
	</c:when>
	<c:otherwise>
        <div style="width:220px; margin-left:auto; margin-right:auto;">
            <div class="left" style="width:110px;">
                <c:if test="${company.page > 0}">
                    <button style="width: 100px;" onclick="Administration.openOfficeUserListDialog(${company.office.id}, '${company.orderBy}', ${company.page - 1});">&laquo; Previous</button>
                </c:if>
            </div>
            <div class="right" style="width:110px;">
                <c:if test="${fn:length(company.users) == company.pageSize}">
                    <button style="width: 100px;" onclick="Administration.openOfficeUserListDialog(${company.office.id}, '${company.orderBy}', ${company.page + 1});">&raquo; Next</button>
                </c:if>
            </div>
        </div>
        <c:set var="point" value="${company.searchString == null ? ' pointer' :  ''}"/>
        <div class="clear" id="sort-container">
            <div class="block_small header">#</div>
            <div class="block_large header${point}" sort-data="lastname,firstname">Name</div>
            <div class="block header${point}" sort-data="username">Username</div>
            <div class="block header${point}">Company</div>
            <div class="block header${point}">Office</div>
            <div class="block header${point}" sort-data="lastLogin">Last Login</div>
            <div class="block header${point}" sort-data="status%20DESC">Status</div>
            <div class="block_large header">Control</div>
            <div class="block_large header">User Rights</div>
            <div class="clear"></div>
        </div>
		<c:forEach var="user" items="${company.users}" varStatus="loop">
			<div class="row${loop.index % 2}" style="overflow: hidden;">
				<div class="block_small row_padding" style="background-color: inherit;">${company.pageSize * company.page + loop.index + 1}</div>
				<div class="block_large row_padding" style="background-color: inherit;">${user.fullName}</div>
                <div class="block row_padding" style="background-color: inherit;">${user.username}</div>
                <div class="block row_padding" style="background-color: inherit;">${user.company.name}</div>
				<div class="block row_padding" style="background-color: inherit;">${user.office.name}</div>
				<div class="block row_padding" style="background-color: inherit;"><fmt:formatDate value="${user.lastLogin}" pattern="yyyy-MM-dd HH:mm"/></div>
				<div class="block row_padding" style="background-color: inherit;">
					<c:choose>
						<c:when test="${user.approvedBy == null}">
							Pending
						</c:when>
						<c:when test="${user.status == 0}">
							Disabled
						</c:when>
						<c:otherwise>
							Active
						</c:otherwise>
					</c:choose>
				</div>
				<div class="block_large row_padding" style="background-color: inherit;">
					<c:set var="target" value="users"/>
					[
					<c:choose>
                        <c:when test="${user.email == null || user.status == 0 || user.singleSignOnUser}">
							<del>send</del>
						</c:when>
						<c:otherwise>
							<a href="<c:url value="#"/>" onclick="this.blur(); this.disabled=true; this.parentNode.parentNode.style.background='lightgreen'; return TSS.openLink('/administration/company.action?sendUserInfo&user.id=${user.id}');">send</a>
						</c:otherwise>
					</c:choose>
					|
					<a href="#" onclick="return Administration.openEditUserDialog('${user.id}');">edit</a>
					|
					<a href="<c:url value="#"/>" target="resultUserList" onclick="return TSS.openLinkConfirm('/administration/company.action?enableDisableUser&user.id=${user.id}&searchString=${company.searchString}','companyUserList', '${user.status == 0 ? "Enable" : "Disable"} this user?');">${user.status == 0 ? "enable" : "disable"}</a>
					]
				</div>
                <div class="block_large row_padding" style="background-color: inherit;">
                    <c:if test="${tvn:hasAccessTo(user.id, 'tvnadmin')}">
                        Company Manager<br>
                    </c:if>
                    <c:if test="${tvn:hasAccessTo(user.id, 'tvnsuper')}">
                        Office Manager
                    </c:if>
                </div>
				&nbsp;
			</div>
			<div class="clear"></div>
		</c:forEach>
	</c:otherwise>
</c:choose>
</div>
<script>
    $j("#sort-container").click(function(event) {
        var sort = $j(event.target).attr("sort-data");
        if(sort && '${company.office.id}') {
            Administration.openOfficeUserListDialog('${company.office.id}', sort, 0);
        }
    });
</script>