<%@include file="/include/taglibs.jsp"%>
<stripes:useActionBean binding="/administration/company.action" id="company"/>

<stripes:form action="/administration/company.action" target="userForm" onsubmit="return User.save(this);">
	<stripes:hidden name="user.id"/>
	<stripes:hidden name="user.requestedBy"/>
	<stripes:hidden name="user.approvedBy"/>
	<stripes:hidden name="user.companyId"/>
	<stripes:hidden name="user.autoDst"/>
	<stripes:hidden name="user.officeId"/>
	<stripes:hidden name="user.lastLogin" formatPattern="yyyy-MM-dd HH:mm" formatType="datetime"/>
	<stripes:hidden name="user.lastPasswordChange" formatPattern="yyyy-MM-dd HH:mm" formatType="datetime"/>
	<stripes:hidden name="user.created" formatPattern="yyyy-MM-dd HH:mm" formatType="datetime"/>
	<stripes:hidden name="user.screenX"/>
	<stripes:hidden name="user.screenY"/>
	<stripes:hidden name="user.timeZone"/>
	<stripes:hidden name="user.phone"/>
	<stripes:hidden name="user.fax"/>
	<stripes:hidden name="user.lastUpdated" formatPattern="yyyy-MM-dd HH:mm" formatType="datetime"/>
	
	<stripes:errors/>
	<table width="100%">
		<tr>
			<td width="40%" valign="top">
				<table>
					<tr>
						<th class="right">System account:</th>
						<td><stripes:checkbox name="user.systemAccount" value="1"/></td>
					</tr>
					<tr>
						<th class="right">API Account:</th>
						<td><stripes:checkbox name="user.apiAccount" value="true"/></td>
					</tr>
                    <tr>
                        <th class="right">Maintenance access:</th>
                        <td><stripes:checkbox name="user.maintenanceApprovedAccount" value="true"/></td>
                    </tr>
					<c:if test="${company.user.id == null}">
						<tr>
							<th class="right">Send E-Mail:</th>
							<td><stripes:checkbox name="sendMail" value="true"/></td>
						</tr>
					</c:if>
					<tr>
						<th class="right">Active:</th>
						<td><stripes:checkbox name="user.status" value="1"/></td>
					</tr>
					<tr>
						<th class="right">All shipment rights:</th>
						<td><stripes:checkbox name="allShipmentRights" value="true"/></td>
					</tr>
					<tr>
						<th class="right">First name:</th>
						<td><stripes:text name="user.firstname" class="regularInput" maxlength="30"/></td>
					</tr>
					<tr>
						<th class="right">Last name:</th>
						<td><stripes:text name="user.lastname" class="regularInput" maxlength="30"/></td>
					</tr>
					<tr>
						<th class="right">E-Mail:</th>
						<td><stripes:text name="user.email" class="regularInput" maxlength="100"/></td>
					</tr>
                    <tr>
                        <th class="right">User name:</th>
                        <td>
                            <c:choose>
                                <c:when test="${company.user.id == null}">
                                    <stripes:text name="user.username" maxlength="50" class="regularInput"/>
                                </c:when>
                                <c:otherwise>
                                    <stripes:hidden name="user.username"/>
                                    ${company.user.username}
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                    <tr>
                        <td class="inputLabel" align="right">Single sign on:</td>
                        <td>
                            <stripes:radio value="true" id="singleSignOnUserYes" name="user.singleSignOnUser"/><label for="singleSignOnUserYes">Yes</label>&nbsp;
                            <stripes:radio value="false" id="singleSignOnUserNo" name="user.singleSignOnUser"/><label for="singleSignOnUserNo">No</label>&nbsp;
                        </td>
                    </tr>
					<tr>
						<th class="right">TVN Id:</th>
						<td><stripes:text name="user.tvnId" value="00" maxlength="2" class="regularInput"/></td>
					</tr>
                    <tr>
                        <th class="right">Certificate identifier:</th>
                        <td><stripes:text name="user.certificateIdentifier" class="regularInput" maxlength="250"/></td>
                    </tr>
					<tr>
						<td colspan="2">
							<stripes:submit name="saveUser" value="Save"/>
						</td>
					</tr>
				</table>
			</td>
			<td width="60%" valign="top">
				<fieldset>
					<legend>User rights</legend>
					<div class="userlevel_field">
						<tvn:getUserLevelSelector userId="${company.user.id}"/>
					</div>
				</fieldset>
			</td>
		</tr>
	</table>
</stripes:form>
<script>
	<c:if test="${company.saved}">
        Administration.closeEditUserDialog();
        User.closeCreateUserDialog();
	</c:if>
</script>
