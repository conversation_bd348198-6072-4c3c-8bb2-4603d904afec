<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/administration/company.action" id="company"/>

<stripes:form action="/administration/company.action" id="companyFormEdit" target="companyTab" onsubmit="return Company.save(this);">
	<stripes:hidden name="company.id" id="companyId"/>
	<stripes:hidden name="company.uuid"/>
	<input type="hidden" name="company.passwordChange" value="${company.company.passwordChange != null ? company.company.passwordChange : 90}"/>
	<input type="hidden" name="company.sessionTimeout" value="${company.company.sessionTimeout != null ? company.company.sessionTimeout : 20}"/>

	<table>
		<tr>
			<td valign="top" width="30%">
				<table>
                    <tr>
                        <td class="inputLabel">Company name:</td>
                            <td><stripes:text name="company.name" style="width:150px;" maxlength="50"/></td>
					    </tr>
					<tr>
						<td class="inputLabel">Company abbreviation:</td>
						<td><stripes:text name="company.abbreviation" style="width:50px;" maxlength="3"/></td>
					</tr>
					<tr style="${company.company.id != null ? 'display:none;' : ''}">
						<td class="inputLabel">TVN Id:</td>
						<td><stripes:text name="company.tvnId" style="width:50px;" maxlength="3"/></td>
					</tr>
					<tr>
						<td class="inputLabel">Country:</td>
						<td>
							<stripes:select name="company.countryId" style="width:150px;">
								<stripes:options-collection collection="${company.countries}" label="name" value="id"/>
							</stripes:select>
						</td>
					</tr>
                    <tr>
                        <td class="inputLabel">System information heading:</td>
                        <td colspan="3"><stripes:text name="company.systemInformationHeading" style="width:320px;" maxlength="64" /></td>
                    </tr>
                    <tr>
                        <td class="inputLabel">System information message:</td>
                        <td colspan="3"><stripes:text name="company.systemInformationMessage" style="width:320px;" maxlength="512" /></td>
                    </tr>
                    <tr>
                        <td class="inputLabel">Help Url :</td>
                        <td colspan="3"><stripes:text name="company.helpURL" style="width:320px;" maxlength="512" /></td>
                    </tr>
                </table>
			</td>
		</tr>
		<tr>
			<td>
				<stripes:submit name="" value="Save"/>
			</td>
		</tr>
	</table>
	<stripes:errors/>
</stripes:form>
