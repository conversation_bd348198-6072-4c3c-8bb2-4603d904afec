<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/administration/country.action" id="admin"/>

<div id="country_list">
	<div class="left">
		<table>
			<tr>
				<td class="adminLabel">#</td>
				<td class="adminLabel">Name</td>
				<td class="adminLabel">Code</td>
				<td class="adminLabel">Edit</td>
			</tr>
			<c:forEach var="country" items="${admin.countries}" varStatus="loop">
				<tr id="country${country.id}" class="row${loop.index % 2}">
					<td class="adminItem">${loop.index + 1}</td>
					<td class="adminItem">${country.name}</td>
					<td class="adminItem">${country.code}</td>
					<td class="adminItem"><img src="/shared/images/icon_edit.gif" style="cursor:pointer;" onclick="Country.manage(${country.id}, '${fn:replace(country.name, "'", "\\'")}', '${fn:replace(country.code, "'", "\\'")}', ${loop.index});"/></td>
				</tr>
			</c:forEach>
		</table>
	</div>
	<div class="left" style="width: 300px;">
		<fieldset>
			<legend>Add Country</legend>
			<stripes:form action="/administration/country.action" onsubmit="return TSS.postFormAdministration(this, 'saveCountry');">
				<stripes:hidden name="country.id"/>
				<table>
					<tr>
						<td colspan="2"><stripes:errors/></td>
					</tr>
					<tr>
						<td class="inputLabel">Country Name:</td>
						<td><stripes:text name="country.name" maxlength="30"/></td>
					</tr>
					<tr>
						<td class="inputLabel">Country Code:</td>
						<td><stripes:text name="country.code" maxlength="2"/></td>
					</tr>
					<tr>
						<td>&nbsp;</td>
						<td><stripes:submit name="saveCountry" value="Save"/></td>
					</tr>
				</table>
			</stripes:form>
		</fieldset>
	</div>
</div>
