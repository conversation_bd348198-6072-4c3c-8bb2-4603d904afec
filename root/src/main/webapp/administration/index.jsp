<%@include file="/include/taglibs.jsp"%>
<!DOCTYPE html 
     PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
     "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
	<head>
        <link rel="stylesheet" type="text/css" href="/shared/include/style/font-awesome-4.2.0/css/font-awesome.min.css">
		<link rel="stylesheet" type="text/css" href="/shared/include/style/ccis-generic.css?v=<tvn:getVersion/>"/>
		<link rel="stylesheet" type="text/css" href="/administration/include/style/administration.css?v=<tvn:getVersion/>"/>
        <link rel="stylesheet" type="text/css" href="/shared/include/style/smoothness/jquery-ui-1.13.2.custom.min.css"/>
        <link rel="stylesheet" type="text/css" href="/shared/include/style/ccis-notification.css?v=<tvn:getVersion/>"/>
		<script type="text/javascript" src="/include/js/ccis-root.js?v=<tvn:getVersion/>"></script>
        <%-- DataTables --%>
        <link rel="stylesheet" type="text/css" href="/shared/libs/DataTables-1.10.10/css/jquery.dataTables.min.css"/>
        <link rel="stylesheet" type="text/css" href="/shared/libs/Buttons-1.1.0/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" type="text/css" href="/shared/libs/Select-1.1.0/css/select.dataTables.min.css">

        <%--<link rel="stylesheet" type="text/css" href="/libs/bootstrap/css/bootstrap.min.css"/>--%>


		<script type="text/javascript" src="/shared/include/js/date-parser.js"></script>
		<script type="text/javascript" src="/shared/include/js/prototype.js"></script>
        <script type="text/javascript" src="/shared/libs/jQuery-3.7.1/jquery-3.7.1.min.js"></script>
        <script type="text/javascript">
            var $j = jQuery.noConflict();
        </script>
        <script src="/libs/moment/moment.min.js"></script>
        <script type="text/javascript" src="/shared/include/js/jquery-ui-1.13.2.custom.min.js"></script>
		<script type="text/javascript" src="/shared/include/js/tss-utils.js?v=<tvn:getVersion/>"></script>
		<script type="text/javascript" src="/shared/include/js/tooltip.js?v=<tvn:getVersion/>"></script>
		<script type="text/javascript" src="/shared/include/js/generic.js?v=<tvn:getVersion/>"></script>
        <script type="text/javascript" src="/shared/include/js/jquery.form.js"></script>
        <script type="text/javascript" src="/administration/include/js/administration.js?v=<tvn:getVersion/>"></script>
        <script type="text/javascript" src="/shared/include/js/ccis-notification.js?v=<tvn:getVersion/>"></script>
        <%-- DataTables --%>
        <script type="text/javascript" src="/shared/libs/DataTables-1.10.10/js/jquery.dataTables.min.js"></script>
        <script type="text/javascript" src="/shared/libs/Buttons-1.1.0/js/dataTables.buttons.min.js"></script>
        <script type="text/javascript" src="/shared/libs/Buttons-1.1.0/js/buttons.print.min.js"></script>
        <script type="text/javascript" src="/shared/libs/Select-1.1.0/js/dataTables.select.min.js"></script>
        <script type="text/javascript" src="/shared/include/js/generic.js?v=<tvn:getVersion/>"></script>

        <title>TSS Administration v<tvn:getVersion/></title>
		<style>
			body {
				background: #fff;
			}
		</style>
        <script type="text/javascript">
            var contextRoot = "<c:url value="/administration/"/>";
            $j(function() {
                $j("#tabs").tabs({
                    beforeLoad: function (event, ui) {
                        var n = ui.tab;
                        if($(n).data("tabname") == "companyTab"){
                            //Do default, e.g. reload
                        } else if ($j(ui.panel).html()) {
                            event.preventDefault();
                        }
                    }
                });
            });
        </script>
    </head>
    <body>

    <%-- Dialogs used on subpages, these dialogs must be created here, ouside the 'tabbed environment' --%>
    <div id="DialogEditCompany" title="Manage Company">
        <div id="editCompanyForm"></div>
    </div>

    <div id="DialogCreateOffice" title="Manage Office">
        <div id="createOfficeForm"></div>
    </div>

    <div id="DialogUserList" title="User List">
        <div id="resultUserList"></div>
    </div>

    <div id="DialogUser" title="Manage User">
        <div id="userForm"></div>
    </div>
    <div id="userSelectorDialog" >
        <div id="userSelectorForm"></div>
    </div>
    <div id="mapProfileDialog" >
        <div id="mapProfileForm"></div>
    </div>
    <div id="viewTemptracerDialog" >
        <div id="viewTemptracerForm"></div>
    </div>
    <div id="modualReportDialog" >
        <div id="modualReportForm"></div>
    </div>
    <div style="height: 30px; margin-left:auto; margin-right: auto; width: 800px; font-size: 10pt;">
        <div class="left list-item">
            <b>Current User: </b>${currentUser.fullName} (${currentUser.username})
        </div>
        <div class="left list-item">
            <b>Company: </b>${currentUser.company.name}
        </div>
        <div class="left list-item">
            <b>Office: </b>${currentUser.office.name}
        </div>
        <form>
            <input type="button" value="Close" onClick="window.location.href='/'">
        </form>
    </div>
    <div class="clear">
    </div>

    <div style="height: 70px; width: 95%; margin-left: auto; margin-right: auto;">
        <div id="tabs">
            <ul>
                <li id="#companyTab" data-tabname="companyTab"><a href="/administration/company.action?companyManager">Company</a></li>
                <li><a href="/administration/settings.action">Settings</a></li>
                <li><a href="/administration/country.action?countryManager">Countries</a></li>
                <li><a href="/administration/AuditTrail.action?loadAuditTrail">Audit Trail</a></li>
                <li><a href="/administration/tooltips.action">Help texts</a></li>
                <li><a href="/administration/sessions.action">Session Manager</a></li>
                <li><a href="/administration/configurationReport.action">Configurations</a></li>
            </ul>
        </div>
    </div>
    </body>
</html>
