<%@include file="/include/taglibs.jsp" %>

<stripes:useActionBean binding="/administration/sessions.action" id="sessions"/>
<div>
    <table id="table_id" class="table stripe hover" cellspacing="0" width="100%">
        <thead>
        <tr>
            <th>Username</th>
            <th>Last Active</th>
            <th>Allowed Inactivity</th>
            <th>Session ID</th>
            <th>Kick</th>
        </tr>
        </thead>
    </table>
</div>


<script type="text/javascript">
    var table = $j('#table_id').DataTable({
        "ajax": "/administration/sessions.action?getSessionList",
        "order": [[1, "desc"]],
        "columns": [
            {"data": "username"},
            {
                "data": "lastActive",
                "render": function (data) {
                    var a = moment.utc(data);
                    a.local();
                    return a.format("YYYY-MM-DD HH:mm:ss")
                }
            },
            {
                "data": "allowedInactivity",
                "render": function (data) {
                    var minutes = data / 60000;
                    return minutes + " minutes"
                }
            },
            {"data": "sessionID"},
            {
                "data": null,
                "defaultContent": "<button>Kick</button>"
            }
        ],
        "dom": '<"left"f><"right"B>rtip',
        buttons: [
            {
                text: "<i class='fa fa-refresh'></i> Refresh",
                action: function (e, dt, node, config) {
                    table.ajax.reload();
                }
            }
        ],
        "scrollX": true,
        "emptyTable": "No data available",
        select: {
            style: 'single'
        }
    });

    $j('#table_id tbody').on('click', 'button', function () {
        var data = table.row($j(this).parents('tr')).data();
//        alert( data[0] +"'s salary is: "+ data[ 2 ] );
        $j.ajax({
            type: "GET",
            url: "/administration/sessions.action?kickUser&selectedUsername=" + data.username,
            success: function (result, textStatus, xhr) {
                var a = JSON.parse(result);
                alert(a ? "Success" : "Failure " + a);
                table.ajax.reload();
            }
        });
    });

</script>