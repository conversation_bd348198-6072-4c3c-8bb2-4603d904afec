<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/manager/manager.action" id="manager"/>

<table id="trialSiteAccessTable">
    <thead>
    <tr>
        <td class="label" style="padding-right: 15px; width: 106px;">Scope</td>
        <td class="label" style="padding-right: 15px; width: 106px;">Trial</td>
        <td class="label" style="padding-right: 15px; width: 106px;">Site</td>
        <td class="label" style="padding-right: 15px;">Remove</td>
    </tr>
    </thead>
    <tbody id="myTrialSiteAccess">
        <c:forEach var="trialSiteAccess" items="${manager.trialSiteAccessList}" varStatus="loop">
            <input type="hidden" name="trialSiteAccessList[${loop.index}].officeDto.id" value="${trialSiteAccess.officeDto.id}">
            <input type="hidden" name="trialSiteAccessList[${loop.index}].officeDto.officeName" value="${trialSiteAccess.officeDto.officeName}">
            <input type="hidden" name="trialSiteAccessList[${loop.index}].trialDto.id" value="${trialSiteAccess.trialDto.id}">
            <input type="hidden" name="trialSiteAccessList[${loop.index}].trialDto.name" value="${trialSiteAccess.trialDto.name}">
            <input type="hidden" name="trialSiteAccessList[${loop.index}].trialDto.serial" value="${trialSiteAccess.trialDto.serial}">
            <input type="hidden" name="trialSiteAccessList[${loop.index}].siteDto.id" value="${trialSiteAccess.siteDto.id}">
            <input type="hidden" name="trialSiteAccessList[${loop.index}].siteDto.siteNumber" value="${trialSiteAccess.siteDto.siteNumber}">

            <tr>
                <td>${trialSiteAccess.officeDto.id == 0 ? "Global" : trialSiteAccess.officeDto.officeName}</td>
                <td>${trialSiteAccess.trialDto.id == 0 ? "All trials" : trialSiteAccess.trialDto.name}</td>
                <td>${trialSiteAccess.siteDto.id == 0 ? "All sites" : trialSiteAccess.siteDto.siteNumber}</td>

                <td>
                    <input type="button" class="large_button" value="Remove"
                           onclick="Manager.removeTrialSiteAccess(${trialSiteAccess.trialDto.id}, ${trialSiteAccess.siteDto.id}, ${trialSiteAccess.officeDto.id});"/>
                </td>
            </tr>
        </c:forEach>
    </tbody>
</table>