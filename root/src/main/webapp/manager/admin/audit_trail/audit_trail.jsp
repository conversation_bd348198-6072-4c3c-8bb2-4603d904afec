<%@include file="/include/taglibs.jsp" %>

<stripes:useActionBean binding="/manager/AuditTrailManager.action" id="at"/>
<script type="text/javascript">
    $j(function () {
        DatePicker.initFromTo('startDate', 1, 'stopDate', 6);
    });
</script>
<div id="audit_trail">
    <div class="left">
        <table border="0">
            <tr>
                <td class="formLabel">Action:</td>
                <td>
                    <select name="action" id="action" onchange="AuditTrailManager.statuses(this.value);">
                        <option value="0">-- All Actions</option>
                        <option value="COMPANY">COMPANY</option>
                        <option value="OFFICE">OFFICE</option>
                        <option value="PROFILE">PROFILE</option>
                        <option value="USER">USER</option>
                        <option value="PWDRECOVERY">PWDRECOVERY</option>
                    </select>
                </td>
                <td class="formLabel">User:</td>
                <td>
                    <input type="hidden" name="user.id" id="audittrailuser" value="${at.user.id}"/>
                    <a href="#" onclick="return Portal.openDialog('/userSelector.action?loadUserSelector&useAll=false',
                        '#userSelectorDialog',
                        '#userSelectorForm',
                        'Select a User', 500, 150, '#selected_user',{callback:Generic.setAuditTrailUser});return false;"
                       id="selected_user" class="bold">

                        <c:if test="${at.user == null}">
                            [NO USER SELECTED!]
                        </c:if>
                        <c:if test="${at.user != null}">
                            ${at.user.fullName}
                        </c:if>
                    </a>
                </td>
            </tr>
            <tr colspan="2" id="statusRow"></tr>
            <tr colspan="2">
                <td class="formLabel">Dates:</td>
                <td>
                    <input type="text" name="startDate" id="startDate"/>
                    -
                    <input type="text" name="stopDate" id="stopDate"/>
                </td>
            </tr>
        </table>
    </div>
    <div class="right">
        <table border="0">
            <tr>
                <td>
                    <input type="button" value="Clear" onclick="AuditTrailManager.clear();"/>
                </td>
                <td>
                    <input type="button" value="Apply" onclick="AuditTrailManager.load(0, false);"/>
                </td>
            </tr>
        </table>
    </div>
    <div class="clear">
        &nbsp;
        <hr/>
        <table id="audit_trail_result" class="display no-wrap" width="100%">
            <thead>
            <tr>
                <th></th>
                <th class="all">Date</th>
                <th class="all">User</th>
                <th class="all">Company</th>
                <th class="all">Office</th>
                <th class="all">Action</th>
                <th class="all">Status</th>
                <th class="none">Description</th>
            </tr>
            </thead>
            <tbody></tbody>
        </table>
        <div id="AuditTrail"></div>
    </div>
</div>
