<%@include file="/include/taglibs.jsp" %>

<stripes:useActionBean binding="/manager/admin.action" id="admin"/>


<div id="password_recovery_approval">
    <stripes:form action="/manager/admin.action">
        <%--<fieldset>--%>
        <%--<legend>Approve/Reject password recovery requests</legend>--%>
        <stripes:errors/>
        <table style="border-spacing: 0; overflow: hidden;" width="100%" border="0">
            <tr>
                <th style="font-size: 10px; vertical-align: top;" align="left">Name</th>
                <th style="font-size: 10px; vertical-align: top;" align="left">Username</th>
                <th style="font-size: 10px; vertical-align: top;" align="left">Email</th>
                <th style="font-size: 10px; vertical-align: top;" align="left">Office</th>
                <th style="font-size: 10px; vertical-align: top;" align="center">Toggle All<br/><input type="checkbox"
                                                                                                       name="selectAll"
                                                                                                       onclick="Manager.toggleChecked(this.checked)">
                </th>
                <th style="font-size: 10px; vertical-align: top; text-align: center;" align="left">Reject Reason</th>
            </tr>

            <c:forEach items="${admin.usersForApproval}" var="usr" varStatus="loop">
                <c:choose>
                    <c:when test="${admin.uuid == usr.uuid}">
                        <tr class="highlight">
                    </c:when>
                    <c:otherwise>
                        <tr class="row${loop.index % 2}">
                    </c:otherwise>
                </c:choose>
                <td>${usr.name}<stripes:hidden name="usersForApproval[${loop.index}].name" /></td>
                <td>${usr.username}<stripes:hidden name="usersForApproval[${loop.index}].username" /></td>
                <td>${usr.email}<stripes:hidden name="usersForApproval[${loop.index}].email" /></td>
                <td>${usr.office}<stripes:hidden name="usersForApproval[${loop.index}].office" /></td>
                <td align="center">
                    <stripes:checkbox name="usersForApproval[${loop.index}].isSelected" value="${usr.id}" class="checkbox"/>
                </td>
                <td style="text-align: right; width: 180px;">
                    <stripes:textarea name="usersForApproval[${loop.index}].reason"/>
                </td>
                <stripes:hidden name="usersForApproval[${loop.index}].id" />
                </tr>
            </c:forEach>
        </table>

        <div class="right" style="margin-top: 20px;">
            <c:if test="${!currentUser.singleSignOnUser}">
            <div class="right" style="margin-top: 10px;">
                <div class="left" style="margin-bottom: 6px;">Authorization is required to approve/reject</div>
                <div class="clear"></div>
                <div class="left" style="margin-right: 20px; margin-bottom: 20px;">
                    <stripes:label for="username">Username</stripes:label>
                    <div class="clear"></div>
                    <stripes:text id="username" name="username" size="25" maxlength="255"/>
                </div>

                <div class="left">
                    <stripes:label for="password">Password</stripes:label>
                    <div class="clear"></div>
                    <stripes:password id="password" name="password" size="25" maxlength="255"/>
                </div>
            </div>

            <div class="clear"></div>
            </c:if>

            <div class="left">
                <stripes:submit name="reject" value="Reject selected" class="submitButton"
                                onclick="return Manager.submitRecoverPwdUsers(this, '/manager/admin.action?reject','Reject');"/>
            </div>
            <div class="left" style="margin-left: 20px;">
                <stripes:submit name="approve" value="Approve selected" class="submitButton"
                                onclick="return Manager.submitRecoverPwdUsers(this, '/manager/admin.action?approve','Approve');"/>
            </div>
        </div>
        <%--</fieldset>--%>
    </stripes:form>
</div>