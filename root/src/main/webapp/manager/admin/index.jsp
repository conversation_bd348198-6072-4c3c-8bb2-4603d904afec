<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/manager/admin.action" id="admin"/>
<div id="subTabContentLarge">
    <div id="adminTabs">
        <ul>
            <c:if test="${admin.showPwdRecoveryApprovalPage}">
                <li><a href="#adminPwdRecovery">Password Recovery Approval</a></li>
            </c:if>
            <tvn:hasAccess level="tvnadmin">
                <li><a href="#adminAuditTrail">Audit Trail</a></li>
            </tvn:hasAccess>
        </ul>
        <div id="adminPwdRecovery">
            <%@include file="pwd_recovery_approval/password_recovery_approval.jsp"%>
        </div>
        <div id="adminAuditTrail">
            <%@include file="audit_trail/audit_trail.jsp"%>
        </div>
    </div>
</div>
<script type="text/javascript">
    $j(function() {
        $j("#adminTabs").tabs({
            heightStyle: "fill",
            beforeLoad: function (event, ui) {
                if ($(ui.panel).html()) {
                    event.preventDefault();
                }
            }
        });
    });
</script>