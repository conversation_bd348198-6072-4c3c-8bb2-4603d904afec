<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/manager/manager.action" id="manager" />

<stripes:form action="/manager/manager.action" partial="true" >
    <stripes:hidden name="office.uuid" />
    <stripes:hidden name="office.id" />
    <stripes:hidden name="office.tvnId" />
    <stripes:hidden name="office.added" />

    <div id="subTabContent">
        <div id="officeTabs">
            <ul>
                <li><a href="#officeSettings">General</a></li>
            </ul>
            <div id="officeSettings">
                <%@include file="include/office_settings.jspf"%>
            </div>
        </div>
    </div>

</stripes:form>


<script type="text/javascript">
    $j(function() {
        $j("#officeTabs").tabs({
            heightStyle: "fill",
            beforeLoad: function (event, ui) {
                if ($(ui.panel).html()) {
                    event.preventDefault();
                }
            }
        });
    });
</script>
