<%@include file="/include/taglibs.jsp"%>

<stripes:useActionBean binding="/manager/manager.action" id="manager"/>

<div class="left">
<table>
	<tr>
		<td class="inputLabel">Edit Office:&nbsp;</td>
		<td>
			<select name="office.id" id="office.id" style="width: 400px;" onchange="Manager.loadOffice('#office_form', this);">
				<tvn:hasAccess level="tvnAdmin">
					<option value="">-- Create New Office --</option>
				</tvn:hasAccess>
				<c:forEach var="o" items="${manager.offices}"><%--CCIS-2832--%>
					<option value="${o.id}">${o.name}&nbsp; -- ${o.countryName}</option>
				</c:forEach>
			</select>
		</td>
	</tr>
</table>
</div>


<stripes:form action="/manager/manager.action" target="office-tab" onsubmit="return Manager.saveOffice('#saveOfficeForm');" id="saveOfficeForm">
    <div class="right">
        <stripes:submit name="saveOffice" value="Save Office" class="large_button"/>
    </div>
    <div class="clear"></div>
    <div id="office_form">
        <%@include file="office_form.jsp"%>
    </div>
</stripes:form>
