<%--Company settings / Global --%>
<%@include file="/include/taglibs.jsp"%>
<stripes:useActionBean binding="/manager/company.action" id="c"/>
<stripes:form action="/manager/company.action" target="Messages" onsubmit="return Manager.saveGlobalSettings(this);">

    <div id="subTabContent">
        <div id="companyTabs">
            <ul>
                <div class="right">
                    <stripes:submit name="saveGlobalSettings" value="Save Settings" class="large_button"/>
                </div>
            <li><a href="#company_general">General</a></li>
            <c:if test="${c.company.usePasswordRecoveryApproval}">
                <li><a href="#company_pwd">Password recovery</a></li>
            </c:if>
            <li><a href="#comapny_email">Email content</a></li>
        </ul>
        <div id="company_general">
            <%@include file="include/general.jspf"%>
        </div>
        <div id="company_pwd">
            <%@include file="include/password_recovery_settings.jspf"%>
        </div>
        <div id="comapny_email">
            <%@include file="include/email_content.jspf"%>
        </div>
    </div>
</div>
</stripes:form>
<script type="text/javascript">
    $j(function() {
        ToolTip.generate();
        $j("#companyTabs").tabs({
            heightStyle: "fill",
            beforeLoad: function (event, ui) {}
        });
    });
</script>