body {
    position: relative;
    font: 12px/17px Arial,Helvetica,sans-serif;
    font-weight: normal;
    color: #848688;
    background-color: #fef9f9;
    text-align: left;
}

p {
    font-size: 1.2em;
    margin-bottom: 12px;
    margin-top: 0;
}

label { color: #ebebeb; font-weight: normal; font-family: Arial,Helvetica,sans-serif; }

a:link, a:visited, a:hover, a:active {
    color: #ebebeb;
}
.clear {
    clear: both;
}
.stripes-errors {
    color: red;
}

/* General width, height, padding and margins */
.width-362 {
    width: 362px;
}
.width-650 {
    width: 650px;
}

.divider {
    float: left;
    clear: both;
    width: 361px;
    margin-left: -30px;
    margin-top: 15px;
    margin-bottom: 15px;

    border-top: 1px solid #1e4f8a;
    -webkit-box-shadow: inset 0 1px #363636;
    -moz-box-shadow: inset 0 1px #363636;
    -ms-box-shadow: inset 0 1px #363636;
    box-shadow: inset 0 1px #363636;
}

.dividerLast {
    float: left;
    clear: both;
    width: 361px;
    margin-left: -30px;
    margin-top: 15px;
    margin-bottom: 15px;
}

.margin-top-10 {
    margin-top: 10px;
}
.margin-bottom-10 {
    margin-bottom: 10px;
}
.margin-right-10 {
    margin-right: 10px;
}
.margin-left-10 {
    margin-left: 10px;
}

#login-container h3 {
    font-size: 16px;
}

#container {
    margin: 100px auto;
}

#cradle-container {
    width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 30px;
}
#header-menu {
    height: 100px;
    background: url(../../images/clinical_look/tss_logo.png?v=2.6.0) no-repeat top left;
    padding: 10px;
}

#login-container {
    position: relative;
    margin: 0 auto;
    padding-top: 30px;
    padding-right: 30px;
    padding-left: 30px;
    padding-bottom: 30px;
    color: #ebebeb;
    font: 12px Arial, Helvetica, sans-serif;

    background: #449fe5; /* Old browsers */
    /* IE9 SVG, needs conditional override of 'filter' to 'none' */
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPHJhZGlhbEdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgY3g9IjUwJSIgY3k9IjUwJSIgcj0iNzUlIj4KICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM0NDlmZTUiIHN0b3Atb3BhY2l0eT0iMSIvPgogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMWU1Nzk5IiBzdG9wLW9wYWNpdHk9IjEiLz4KICA8L3JhZGlhbEdyYWRpZW50PgogIDxyZWN0IHg9Ii01MCIgeT0iLTUwIiB3aWR0aD0iMTAxIiBoZWlnaHQ9IjEwMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -moz-radial-gradient(center, ellipse cover,  #449fe5 0%, #1e5799 100%); /* FF3.6+ */
    background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,#449fe5), color-stop(100%,#1e5799)); /* Chrome,Safari4+ */
    background: -webkit-radial-gradient(center, ellipse cover,  #449fe5 0%,#1e5799 100%); /* Chrome10+,Safari5.1+ */
    background: -o-radial-gradient(center, ellipse cover,  #449fe5 0%,#1e5799 100%); /* Opera 12+ */
    background: -ms-radial-gradient(center, ellipse cover,  #449fe5 0%,#1e5799 100%); /* IE10+ */
    background: radial-gradient(ellipse at center,  #449fe5 0%,#1e5799 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#449fe5', endColorstr='#1e5799',GradientType=1 ); /* IE6-8 fallback on horizontal gradient */
    -pie-background: linear-gradient(#449fe5, #1e5799);
    border: 1px solid #204abf;
    -webkit-box-shadow: 0 3px 15px 0 #000000;
    -moz-box-shadow: 0 3px 15px 0 #000000;
    box-shadow: 0 3px 15px 0 #000000;
    behavior: url(../../include/style/pie-1.0.0/PIE.htc);
}
#login-form-wrapper {
    margin-top: 20px;
}

#login-register-inputs {
    width: 300px;
}

#login-register-info {
    width: 200px;
}

#login-container h2 {
    height: inherit;
    float: left;
    padding:0;
    margin:0;
    color: #ebebeb;
    font: bold 44px "Calibri", Arial;
}

.login-link {
    height: inherit;
    float: left;
    font-size: 16px;
    font-style: italic;
    cursor: pointer;
    margin-top: 8px;
    color: #ebebeb;
    text-decoration: underline;
}

.logo-container {
    width: 100px;
    height: 40px;
    margin-top: 10px;
    float: right;
}
.logo-container img {
    width: 100%;
    height: auto;
}

.center {
    display: block;
    margin-left: auto;
    margin-right: auto;
}
.pwdRecoveryUrl {
    font: 12px Arial, Helvetica, sans-serif;
    text-decoration: underline;
    font-weight: bold;
}
.splashLogo {
    width: 800px;
    height: 240px;
    padding: 0;
    margin: 0 auto;
    float: none;
}

.splashLogo .helper {
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

.splashLogo img {
    max-width: 240px;
    max-height: 240px;
    vertical-align: middle;
}
#company_logo {
    float: right; clear: both; margin-top: 4px;
}
#company_logo img {
    max-height: 40px;
}

.upload_wrapper {
    padding: 10px;
}
.result_wrapper {
    background-color: white;
    /*border: 1px solid;*/
    border: 2px solid rgba(0, 0, 0, 0.3);
    border-bottom: none;
    height: 200px;
}
#upload_result {
    width: 650px;
    margin-bottom: 50px;
    background-color: white;
    border: 1px;
}
#upload_result a:link, #upload_result a:visited, #upload_result a:hover, #upload_result a:active{
    text-decoration: none;
    color: #848688;
}
#report_url {
    text-decoration: none;
    color: #848688;
}
#report_url:hover {
    display: block;
    background-color: #b2cde9;
}
#uploader,#uploader5 {
    display: none;
}
#uploader5, #uploader_form {
    width: 676px;
    margin: 20px auto;
}
#uploader {
    width: 676px;
    margin: 20px auto;
}
#holder {
    border: 5px dashed #ccc;
    width: 150px;
    min-height: 150px;
    margin: 20px auto;
}
#holder.hover {
    border: 5px dashed #0c0;
}
#holder img {
    display: block;
    margin: 10px auto;
}
#holder p {
    margin: 10px; font-size: 14px;
}
progress {
    width: 100%;
}
progress:after {
    content: '%';
}

#response_msg {
    color: #333333;
    font-weight: bold;
}
#response_header {
    font-size: 1.5em;
    font-weight: bold;
}

#alarm_triggered {
    min-height: 128px;
    font-size: 2.2em;
    font-weight: bold;
    display: none;
    background: url(../../images/clinical_look/alarm.png?v=2.6.0) 0 0 no-repeat;
}

#no_alarm_triggered {
    min-height: 128px;
    font-size: 2.2em;
    font-weight: bold;
    display: none;
    background: url(../../images/clinical_look/ok.png?v=2.6.0) 0 0 no-repeat;
}

#triptag_wrapper {
    color: #333333;
    font-weight: bold;
    display: none;
    margin-bottom: 10px;
    overflow: hidden;
}

.alarm_info {
    width: 500px;
    padding-top: 10px;
    padding-left: 150px;
    overflow: hidden;
    line-height: 100%;
}

.alarm_info_text {
    padding-left: 150px;
    color: #000000;
    font-size: 12px;
}

#login_form label.error {
    margin-left: 1px;
    width: auto;
    display: inline;
    color: rgba(161, 0, 0, 1);
    font-weight: bold;
}
#register-site {
    color: #EBEBEB;
    font: bold 30px "Calibri",Arial;
    margin: 0;
    padding: 0;
}

#user_info {
    font: 12px Arial, Helvetica, sans-serif;
    font-weight: bold;
    margin-top: 4px;
    margin-right: 36px;
    white-space: nowrap;
    clear: both;
    float: right;
}

#register-button {
    margin-top: 2px;
    margin-right: 16px;
    float: right;
    border: none;
}

#login-button {
    margin-top:2px;
    float: right;
}
#sso-button {
    width: 100%;
}
.oauth-wrapper {
    float: left;
    width: 100%;
}
#oauth-login-button {
    float: right;
    margin-top: 15px;
}

.login-label {
    margin: 0 0 2px;
    padding: 4px 10px 0 0;
    text-align: left;
    white-space: nowrap;
}

.login-field {
    width: 300px;
}
.login-field-short {
    width: 140px;
}
.login-field input  {
    width: 290px; /* minus padding and border */
    padding: 10px 4px 6px 4px;
    border: 1px solid #0d2c52;
    background-color:#1e4f8a;
    font-size: 16px;
    color: #ebebeb;
}
.login-field-short input {
    width: 130px; /* minus padding and border */
    padding: 10px 4px 6px 4px;
    border: 1px solid #0d2c52;
    background-color:#1e4f8a;
    font-size: 16px;
    color: #ebebeb;
}
.left {
    float: left;
}

.right {
    float: right;
}

#login-container select {
    width: 100%;
    padding: 10px 4px 6px 4px;
    border: 1px solid #0d2c52;
    background-color:#1e4f8a;
    font-size: 16px;
    color: #ebebeb;
}

.login_remember_me label {
    display: block;
    white-space: nowrap;
    font: 11px Arial, Helvetica, sans-serif;
}
.login_remember_me input {
    vertical-align: middle;
}
.login_remember_me label span {
    vertical-align: middle;
}

.login_manual_upload label {
    display: block;
    white-space: nowrap;
    font: 11px Arial, Helvetica, sans-serif;
}
.login_manual_upload input {
    vertical-align: middle;
}
.login_manual_upload label span {
    vertical-align: middle;
}

#login_pwd_recovery {
    float: right;
    margin-top: 12px;
    cursor: pointer;
    text-decoration: underline;
    white-space: nowrap;
    font-size: 11px;
}

/* Cradle Archive */
#archive_tube {
    padding-top: 20px;
    padding-bottom: 20px;
}
.archive_entry a {
    font: 20px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #334B70;
    text-decoration: none;
    padding-top: 20px;
    float: left;
    clear: both;
}
.archive_entry a#report_url {
    font: 12px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #334B70;
    text-decoration: none;
    padding-top: 0px;
    float: left;
    clear: both;
}
.item_info {
    float: left;
    white-space: nowrap;
    width: 100%;
    clear: both;
}
.archive_mission_info {
    font: 10px Arial, Helvetica, sans-serif;
    font-weight: bold;
    float: left;
    clear: both;
}
.archive_origin {
    font: 10px Arial, Helvetica, sans-serif;
    font-weight: bold;
    float: left;
}
.archive_destination {
    font: 10px Arial, Helvetica, sans-serif;
    font-weight: bold;
    float: left;
}
.break {
    font: 10px Arial, Helvetica, sans-serif;
    font-weight: bold;
    padding-left: 10px;
    padding-right: 10px;
    float: left;
}
.alarm_yes {
    font: 14px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #565659;
    height: 20px;
    padding-left: 30px;
    padding-top: 2px;
    margin-top: 4px;
    margin-bottom: 3px;
    background: url(../../images/clinical_look/alarm_small.png?v=2.6.0) 0 0 no-repeat;
    float: left;
    clear: both;
}
.alarm_yes_bw {
    font: 14px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #cbcbce;
    height: 20px;
    padding-left: 30px;
    padding-top: 2px;
    margin-top: 4px;
    margin-bottom: 3px;
    background: url(../../images/clinical_look/alarm_small_bw.png?v=2.6.0) 0 0 no-repeat;
    float: left;
    clear: both;
}
.alarm_yes_blur {
    font: 14px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #cbcbce;
    height: 20px;
    padding-left: 30px;
    padding-top: 2px;
    margin-top: 4px;
    margin-bottom: 3px;
    background: url(../../images/clinical_look/alarm_small_blur.png) 0 0 no-repeat;
    float: left;
    clear: both;
}
.alarm_no {
    font: 14px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #565659;
    height: 20px;
    padding-left: 30px;
    padding-top: 2px;
    margin-top: 4px;
    margin-bottom: 3px;
    background: url(../../images/clinical_look/ok_small.png?v=2.6.0) 0 0 no-repeat;
    float: left;
    clear: both;
}
.alarm_no_blur {
    font: 14px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #cbcbce;
    height: 20px;
    padding-left: 30px;
    padding-top: 2px;
    margin-top: 4px;
    margin-bottom: 3px;
    background: url(../../images/clinical_look/ok_small_blur.png?v=2.6.0) 0 0 no-repeat;
    float: left;
    clear: both;
}
.alarm_no_bw {
    font: 14px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #cbcbce;
    height: 20px;
    padding-left: 30px;
    padding-top: 2px;
    margin-top: 4px;
    margin-bottom: 3px;
    background: url(../../images/clinical_look/ok_small_bw.png?v=2.6.0) 0 0 no-repeat;
    float: left;
    clear: both;
}
.archive_waiting {
    font: 11px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #56a2e8;
    width: 200px;
    float: left;
    clear: both;
}
.archive_eval_by {
    font: 11px Arial, Helvetica, sans-serif;
    color: #56a2e8;
    font-weight: bold;
    float: left;
    clear: left;
}
.archive_eval_type {
    font: 11px Arial, Helvetica, sans-serif;
    color: #56a2e8;
    font-weight: bold;
    float: left;
}
.archive_eval_date {
    font: 11px Arial, Helvetica, sans-serif;
    color: #56a2e8;
    font-weight: bold;
    float: left;
}
.archive_eval_reason {
    font: 11px Arial, Helvetica, sans-serif;
    color: #56a2e8;
    font-weight: bold;
    font-style: italic;
    float: left;
    clear: left;
    padding-bottom: 6px;
    word-wrap: break-word;
    width: 100%;
}

/* My Settings */
#settings_container {
    margin-left: auto;
    margin-right: auto;
    width: 485px;
    text-align: left;
}
#settings_box {
    width:333px;
    height: 250px;
    padding: 40px 76px 18px 76px;
    color: #848688;
    font: 12px Arial, Helvetica, sans-serif;
}
#save-button {
    margin-top:10px;
    margin-left: 203px;
    float: left;
    border: none;
}
.settings_box_name {
    float: left;
    display:inline;
    width:80px;
    text-align: right;
    padding: 14px 10px 0 0;
    margin:0 0 7px 0;
}
.settings_box_field {
    float: left;
    display:inline;
    width:230px;
    margin:0 0 7px 0;
}
.form_settings  {
    width: 205px;
    padding: 10px 4px 6px 3px;
    border: 1px solid #0d2c52;
    background-color: #1e4f8a;
    font-size: 16px;
    color: #ebebeb;
}
.form_settings_non_edit  {
    width: 205px;
    padding: 10px 4px 6px 3px;
    font-size: 16px;
    color: #848688;
}

#error-container {
    display: none;
    float: left;
    color: white;
    background-color: red;
    font-size: 12px;
    width: 100%;
}
#error-container {
    padding: 6px;
}
#DialogEditSettings {
    height: 0px;
}
.spinner_container {
    position: absolute;
    width: 100px;
    height: 100px;
    z-index: 1;
}

#arrival_date_message {
    padding-bottom: 20px;
}
#skipArrivalDate {
    float: right;
    margin-right: 10px;
}
#submitArrivalDate {
    float: right;
    /*margin-right: 8px;*/
}

.date_time_selector {
    float: left;
    padding-bottom: 20px;
}
#date, #time {
    margin-left: 30px;
}
/* Timepicker */
.ui-timepicker-div .ui-widget-header { margin-bottom: 8px; }
.ui-timepicker-div dl { text-align: left; }
.ui-timepicker-div dl dt { height: 25px; margin-bottom: -25px; }
.ui-timepicker-div dl dd { margin: 0 10px 10px 65px; }
.ui-timepicker-div td { font-size: 90%; }
.ui-tpicker-grid-label { background: none; border: none; margin: 0; padding: 0; }

.searchHeader {
    font: 20px Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #334B70;
    text-decoration: none;
}
.searchInput {
    font: 14px Arial, Helvetica, sans-serif;
    color: #334B70;
    text-decoration: none;
}

#uploaded_missions-dt_wrapper {
    padding-top: 20px;
}

#uploaded_missions-dt_filter input {
    padding: 4px;
    width: 400px;
    margin-left: 2px;
}

.evallink {
    cursor: pointer;
    text-decoration: underline;
    color: blue;
}

.oldBrowserUploadResult {
    float: left;
    clear: both;
}

.alarmTriggered {
    color: red;
}

.noAlarmTriggered {
    color: green;
}

#report_url a:link, #report_url a:visited, #report_url a:hover, #report_url a:active {
    color: blue;
}

#oldBrowserUploadWrapper {
    margin-top: 40px;
    margin-left: 100px;
    margin-right: 100px;
    color: #333333;
}
