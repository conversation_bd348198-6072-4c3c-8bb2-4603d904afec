body {
	background-color: #fff;
}

div {
	background-color: inherit;
}

td {
	font-family: verdana;
	font-size: 11px;
}

.reportHead,td.reportHead td {
	background-color: #ddd;
	font-size: 12px;
	font-weight: bold;
}

table.report {
	border-top: 1px solid #ccc;
}

td.border-left {
	border-left: 1px solid #ccc;
	width: 25px;
}

tr.row0 {
	background-color: #eee;
}

tr.row1 {
	background-color: #fff;
}

th {
	font-family: verdana;
	text-align: left;
	font-weight: bold;
	font-size: 12px;
}

th.right {
	text-align: right;
}

@media print {
	.pagebreak {
		page-break-before: always;
	}
	.pagebreak_after {
		page-break-after: always;
	}
	@page {
		margin: 0cm;
	}
	th {
		font-size: 9px;
	}
	td {
		font-size: 9px;
	}
	.hide_from_printer {
		display: none;
	}
}