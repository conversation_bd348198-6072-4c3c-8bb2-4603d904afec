body {
	margin: 0px;
	padding: 0px;
	font: 10px verdana;
	color: #333;
	background-color: #555555;
}

label {
	color: #333;
}

a,a:visited {
	color: #000;
	text-decoration: none;
}

a:hover {
	color: #333;
	text-decoration: underline;
}

li {
	margin-left: 15px;
}

img {
	border: 0px;
}
img.center {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

legend {
	color: #666;
	font-family: tahoma;
	font-size: 14px;
	font-weight: bold;
}

a,td {
	font-family: verdana;
	font-size: 10px;
	font-weight: normal;
	color: #333;
}

h1 {
	font-size: 12pt;
	height: 18px;
	padding-top: 2px;
}

h2 {
	font-size: 11pt;
	height: 16px;
	padding-top: 2px;
}

h3 {
	font-size: 11px;
	height: 14px;
	padding-top: 2px;
}

.left {
	float: left;
}

.right {
	float: right;
}

.clear {
	clear: both;
}

.txt-color-red {
    color: #EC0000;
}
.txt-color-green {
    color: #03BA03;
}
.txt-color-lightblue {
    color: #00A0E5;
}
.txt-color-yellow {
    color: #f5ea1b;
}

.margin-left-10 {
    margin-left: 10px;
}
.margin-top-0{
    margin-top:0px;
}
.margin-top-5 {
    margin-top: 5px;
}
.margin-top-10 {
    margin-top: 10px;
}
.margin-bottom-4 {
    margin-bottom: 4px;
}
.margin-bottom-5 {
    margin-bottom: 5px;
}
.margin-right-10 {
    margin-right: 10px;
}
.margin-bottom-10 {
    margin-bottom: 10px;
}
select,input,textarea {
	font-family: verdana;
	font-size: 8pt;
}

td.adminLabel,td.label {
	font: 10px verdana;
	font-weight: bold;
	padding-right: 5px;
	padding-left: 5px;
	background-image: url("/shared/images/title_bg.gif?v=2.6.0");
	background-repeat: repeat-x;
	height: 20px;
	border: 1px solid #d0d0d0;
}

td.boxTopLeft {
	width: 12px;
	height: 26px;
	background-image: url("/shared/images/box/top_left.gif?v=2.6.0");
}

td.boxTopCenter,td.boxTopCenter td {
	height: 24px;
	background-image: url("/shared/images/box/top_center.gif?v=2.6.0");
	background-repeat: repeat-x;
	font-family: Tahoma;
	font-size: 13px;
	font-weight: bold;
	color: #333333;
}

td.boxTopRight {
	width: 12px;
	height: 26px;
	background-image: url("/shared/images/box/top_right.gif?v=2.6.0");
}

td.boxMiddleRight {
	width: 12px;
	background-image: url("/shared/images/box/middle_right.gif?v=2.6.0");
	background-repeat: repeat-y;
}

td.boxBody {
	background-color: #FFF;
	font: 10px verdana;
	text-align: justify;
}

td.boxMiddleLeft {
	width: 12px;
	background-image: url("/shared/images/box/middle_left.gif?v=2.6.0?v=2.6.0");
	background-repeat: repeat-y;
}

td.boxBottomLeft {
	width: 12px;
	height: 12px;
	background-image: url("/shared/images/box/bottom_left.gif?v=2.6.0");
}

td.boxBottomCenter {
	height: 12px;
	background-image: url("/shared/images/box/bottom_center.gif?v=2.6.0");
	background-repeat: repeat-x;
}

td.boxBottomRight {
	width: 12px;
	height: 12px;
	background-image: url("/shared/images/box/bottom_right.gif?v=2.6.0");
}

fieldset {
	margin-left: 2px;
	margin-right: 2px;
    border-radius: 5px;
}

fieldset.summary {
	width: 410px;
	padding-left: 5px;
	margin-right: 3px;
}

.processInfo {
	display: block;
	z-index: 1000;
	position: absolute;
	width: 350px;
	float: left;
	height: 60px;
	padding-top: 10px;
	padding-bottom: 10px;
	background-image: url("/shared/images/process_info_background.gif?v=2.6.0");
	background-repeat: no-repeat;
	font: 15px tahoma;
	font-weight: bold;
	letter-spacing: 1px;
	text-align: center;
	overflow: hidden;
	background-color: transparent;
}

label,.inputLabel {
	font: 11px verdana;
	font-weight: bold;
}

div.panel {
	overflow: auto;
	border: 1px solid #D0D0D0;
	padding: 5px;
}

/** NEW DESIGN HERE **/
td.frameTop {
	width: 1020px;
	height: 93px;
	background-color: #555;
	background-image: url("/shared/images/helium/ccis_frame.png?v=2.6.0");
	background-repeat: no-repeat;
}

td.frameLeft {
	width: 20px;
	background-image: url("/shared/images/helium/tvn_frame_bottom_left.png?v=2.6.0")
		;
	background-repeat: repeat-y;
	background-position: left;
}

td.frameRight {
	width: 20px;
	background-image:
		url("/shared/images/helium/tvn_frame_bottom_right.png?v=2.6.0");
	background-repeat: repeat-y;
	padding-right: 2px;
}

td.frameBody {
	width: 979px;
	background: #fff;
}

td.frameBottom {
	width: 1020px;
	height: 24px;
	padding-left: 2px;
	background-image: url("/shared/images/helium/tvn_frame_bottom.png?v=2.6.0");
	background-repeat: no-repeat;
	background-position: bottom;
}

div.tvnHeader {
	width: 988px;
	height: 41px;
	margin-left: 17px;
	margin-top: 47px;
	background-image: url("/shared/images/helium/tvn_header.gif?v=2.6.0");
	background-repeat: no-repeat;
}

div.tvnLogo {
	width: 164px;
	height: 32px;
	padding-top: 8px;
	padding-left: 10px;
	float: left;
	background-color: transparent;
}

div.loginInfo {
	float: left;
	width: 274px;
	height: 29px;
	margin-top: 5px;
	margin-left: 25px;
	overflow: hidden;
	background-image: url("/shared/images/helium/user_info_bg.png?v=2.6.0");
	background-repeat: no-repeat;
	background-color: transparent;
}

div.loginInfo div {
	padding-top: 2px;
	padding-left: 5px;
	color: #999999;
}

div.tvnMenu {
	float: right;
	background-color: transparent;
}

div.tvnMenuItem {
	float: left;
}

div.tvnMenuItemText a,div.tvnMenuItemText a:hover,div.tvnMenuItemText a:visited
	{
	line-height: 41px;
	height: 41px;
	float: left;
	padding-left: 10px;
	padding-right: 10px;
	font-family: arial;
	font-size: 9pt;
	font-weight: bold;
	color: #000066;
	background-image: url("/shared/images/helium/tvn_delimiter.png?v=2.6.0");
	background-repeat: no-repeat;
	text-decoration: none;
}

div.selected {
	background-image: url("/shared/images/helium/tvn_menu_bg_selected.png?v=2.6.0");
	color: white;
}

div.selected div.tvnMenuItemText a {
	color: white;
}

div.ccisHeaderLeft {
	float: left;
	padding-left: 25px;
	padding-top: 10px;
	background-color: transparent;
}

div.ccisHeaderRight {
	float: right;
	padding-top: 18px;
	margin-right: 20px;
	background-color: transparent;
}

#MainBody {
	position: relative;
	width: 975px;
	text-align: left;
	margin: 0 auto;
	background: #fff;
	padding-left: 2px;
	padding-right: 2px;
	padding-top: 5px;
	background-image: url("/shared/images/tss_bg_logo.gif?v=2.6.0");
	background-position: center center;
	background-repeat: no-repeat;
	min-height: 430px;
	overflow: auto;
}

#rootPanel {
    background: inherit;
}

.row0 {
    background: #efefef;
}

.row1 {
    background: #fff;
}

.row1:hover,.row0:hover,.hover {
	background-color: #eaeaea;
}

#Messages {
	font-family: verdana;
	font-size: 9pt;
	padding-left: 30px;
	color: red;
}

form {
	padding: 0px;
	margin: 0px;
}

@media print {
	.hide_from_printer {
		display: none;
	}
}

div.userlevel_field {
	min-height: 300px;
	height: 300px;
	overflow: auto;
}

fieldset.add_user {
	min-height: 165px;
	height: 165px;
}

/** TAB START**/
div.tab div.center a,div.tab div.center a:hover {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 9pt;
	font-weight: bold;
	text-decoration: none;
	color: #333;
}

div.tab div.center {
	float: left;
	background-image: url("/shared/images/tab/center.gif?v=2.6.0");
	background-repeat: repeat-x;
	height: 20px;
	padding-top: 3px;
}

div.tab div.left {
	float: left;
	width: 10px;
	height: 23px;
	background-image: url("/shared/images/tab/left.gif?v=2.6.0");
	background-repeat: no-repeat;
}

div.tab div.right {
	float: left;
	width: 10px;
	height: 23px;
	background-image: url("/shared/images/tab/right.gif?v=2.6.0");
	background-repeat: no-repeat;
}

div.tab_active div.center a,div.tab_active div.center a:hover {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 9pt;
	font-weight: bold;
	text-decoration: none;
	color: #fff;
}

div.tab_active div.center {
	float: left;
	background-image: url("/shared/images/tab/center_active.gif?v=2.6.0");
	background-repeat: repeat-x;
	height: 20px;
	padding-top: 3px;
}

div.tab_active div.left {
	float: left;
	width: 10px;
	height: 23px;
	background-image: url("/shared/images/tab/left_active.gif?v=2.6.0");
	background-repeat: no-repeat;
}

div.tab_active div.right {
	float: left;
	width: 10px;
	height: 23px;
	background-image: url("/shared/images/tab/right_active.gif?v=2.6.0");
	background-repeat: no-repeat;
}

/** TAB END **/
div#signatureDiv {
	visibility: visible;
	position: absolute;
	height: 290px;
	width: 500px;
	border: 2px solid #333;
}

.info_text {
	font-size: 9pt;
}

div.editor {
	position: absolute;
	background: #fcfcfc;
	margin: 5px;
	border: 2px solid #666;
}

div.editor_header {
	padding-top: 2px;
	color: #333;
	height: 20px;
	background-image: url("/shared/images/menu_bg.gif?v=2.6.0");
	font-weight: bold;
	font-size: 14px;
	letter-spacing: 3px;
	border-bottom: 1px solid #999;
}

.large_button {
	font-family: tahoma;
	font-size: 11px;
	font-weight: bolder;
	letter-spacing: 1px;
	padding-left: 5px;
	padding-right: 5px;
	overflow: visible;
}

.select_scope_trial_site {
	width: 120px;
}

.large_button_noborder_nobg {
	font-family: tahoma;
	font-size: 11px;
	font-weight: bolder;
	border: none;
	background: none;
}

.ui-widget .large_button {
    font-family: tahoma;
    font-size: 11px;
    font-weight: bolder;
    letter-spacing: 1px;
    padding-left: 5px;
    padding-right: 5px;
    overflow: visible;
}

div.sub_panel {
	margin-right: 5px;
	padding: 3px;
	border: 1px solid #D0D0D0;
	height: 550px;
	overflow: auto;
}

div.sub_panel_menu {
	padding-top: 5px;
	width: 99%;
	margin-bottom: -1px;
}

.altered {
	cursor: pointer;
}

#history {
	width: 800px;
	height: 300px;
}

#history_window {
	overflow: auto;
	height: 277px;
}

div.history_window div.header {
	text-align: left;
	padding-right: 0px;
	padding-left: 0px;
}

div.block_mini {
	float: left;
	width: 15px;
	overflow: hidden;
}

div.block_miniplus {
    float: left;
    width: 21px;
    overflow: hidden;
}

div.block_small {
	float: left;
	width: 40px;
	overflow: hidden;
}

div.block,div.block2 {
	float: left;
	width: 100px;
	overflow: hidden;
}

div.block2 {
    width: 70px;
}

div.block_medium {
	float: left;
	width: 130px;
	overflow: hidden;
}

div.block_large {
	float: left;
	width: 155px;
	overflow: hidden;
}

div.block_larger {
	float: left;
	width: 220px;
	overflow: hidden;
}

div.block_huge {
	float: left;
	width: 320px;
	overflow: hidden;
}
div.block_mega_huge {
    float: left;
    width: 520px;
    overflow: hidden;
}
div.row_padding {
	padding-top: 4px;
	padding-bottom: 4px;
}

div.height10 {
    height: 10px;
}

div.height20 {
	height: 20px;
}

div.height25 {
	height: 25px;
}

div.block_small input[type="checkbox"] {
	height: 12px;
}

div.header {
	font-weight: bold;
	font-size: 10px;
	text-align: left;
}

div.left30 {
    margin-left: 30px;
}

#user_selector div.window {
	margin: 5px;
}

#user_selector div.header {
	padding-right: 5px;
	padding-top: 3px;
}

#user_selector {
	width: 500px;
	height: 150px;
	overflow: hidden;
}

#user_selector select {
	width: 350px;
}

#user_selector input[type="text"] {
	width: 270px;
}

.height25 {
	padding-top: 2px;
	height: 23px;
}

.height30 {
	padding-top: 2px;
	height: 28px;
}

.height40 {
	padding-top: 2px;
	height: 38px;
}

.width20 {
	width: 20px;
	overflow: hidden;
}

.width50 {
	width: 50px;
	overflow: hidden;
}

.width75 {
	width: 75px;
	overflow: hidden;
}

.width100 {
	width: 100px;
	overflow: hidden;
}

.width120 {
	width: 120px;
	overflow: hidden;
}

.width138 {
    width: 138px;
    overflow: hidden;
}

.width150,.w150 {
	width: 150px;
	overflow: hidden;
}

.width180 {
	width: 180px;
	overflow: hidden;
}

.width200 {
	width: 200px;
	overflow: hidden;
}

.width210 {
    width: 210px;
    overflow: hidden;
}

.width220 {
    width: 220px;
    overflow: hidden;
}

.width250 {
	width: 250px;
	overflow: hidden;
}

.width280 {
    width: 280px;
    overflow: hidden;
}

.width300 {
	width: 300px;
	overflow: hidden;
}

.width324 {
    width: 324px;
    overflow: hidden;
}

.width350 {
	width: 350px;
	overflow: hidden;
}

.width500 {
	width: 500px;
	overflow: hidden;
}

.width600 {
    width: 600px;
    overflow: hidden;
}

div.sortable:hover {
	cursor: pointer;
}

legend.important {
	color: red;
}

#temperatureSelectorBox {
	position: absolute;
	height: 150px;
	overflow: auto;
	color: #eee;
	background: #fff;
	border: 1px solid #ccc;
	color: black;
	cursor: pointer;
}

#temperatureSelectorBox div {
	line-height: 18px;
}

div.temperatureSelectorCover {
	border: 1px solid #ccc;
	background: #fff;
	cursor: pointer;
	text-align: right;
}

#DEBUG_DIV {
	position: absolute;
	width: 300px;
	height: 500px;
	left: 0px;
	top: 50px;
	background: #fff;
	border: 2px solid #000;
	overflow: auto;
}

div.number {
	font-weight: bold;
	color: black;
}

div.triggered {
	color: red;
}

div.notTriggered {
    color: darkgreen;
}

div.pending {
	color: #666;
	font-style: italic;
}

div.configured {
	color: black;
}

div.text-right {
	text-align: right;
}

img.close_window {
	cursor: pointer;
	padding-right: 3px;
}

.red {
	color: red;
}

.green {
    color: green;
}

.top3 {
	padding-top: 3px;
}

.bold {
	font-weight: bold;
}

.center img {
    display: block;
    margin-left:auto;
    margin-right:auto;
}

.toggle_options {
	clear: both;
	/*width: 919px;*/
	border: 1px solid #666;
	/*background-color: #efefef;*/
	z-index: 1000;
    padding: 5px;
}

div.toggle_options_container{
	cursor: pointer;
	background-color: #e6e6e6;
	font-weight: bold;
	font-size: 10pt;
	color: #000000;
	letter-spacing: 2px;
	padding-top: 2px;
	padding-bottom: 2px;
	text-align: center;
	border: 1px solid #666;
	/*border: 1px solid magenta;*/
    border-bottom-width: 0px;
}

.notificationRules {
    float:left;
}
.cradleUser {
    float:left;
    padding-left: 20px;
}
.actionOnAlarm {
    float:left;
    padding-left: 20px;
}
#accordion h3, #tripTagAccordion h3, #rootCauseAccordion h3 {
    height: 22px;
}


/* Overrides of jquery UI */
.ui-accordion .ui-accordion-header a {
    display: block;
    font-size: 1em;
    padding: .3em .5em .5em .7em;
}
.ui-accordion-icons .ui-accordion-header a {
    padding-left: 2.2em;
}

/* Timepicker */
.ui-timepicker-div .ui-widget-header { margin-bottom: 8px; }
.ui-timepicker-div dl { text-align: left; }
.ui-timepicker-div dl dt { height: 25px; margin-bottom: -25px; }
.ui-timepicker-div dl dd { margin: 0 10px 10px 65px; }
.ui-timepicker-div td { font-size: 90%; }
.ui-tpicker-grid-label { background: none; border: none; margin: 0; padding: 0; }

body.error-page {
	background-color: #f2f2f2;
	text-align: center;
}

.image-container img {
	width: auto;
	height: auto;
	margin-top: 100px;
}

.errorPage h1 {
	font-size: 24px;
	color: #333333;
	text-align: center;
}

.errorPage {
	margin: 50px auto;
	padding: 20px;
	max-width: 500px;
	background-color: #ffffff;
	border: 1px solid #cccccc;
	border-radius: 10px;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.errorPage p {
	font-size: 18px;
	color: #666666;
}

.errorPage details {
	margin-top: 20px;
	border-top: 1px solid #cccccc;
	padding-top: 10px;
}

.errorPage details summary {
	font-weight: bold;
	font-size: 16px;
	color: #333333;
}

.errorPage details pre {
	font-size: 14px;
	color: #333333;
	margin-top: 10px;
}

#rootTab {
    height: 800px;
    padding: 0px;
    background: none;
    border-width: 0px;
}

#rootTab .ui-widget {
    font-size: 10px;
}

#rootTab .ui-corner-all {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

#rootTab .ui-tabs-nav {
    padding-left: 0px;
    background: transparent;
    border-width: 0px 0px 1px 0px;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}

#rootTab .ui-tabs-panel {
    border-width: 0px 1px 1px 1px;
    padding: 10px;
}

#rootTab legend {
    font-size: 9pt;
    color: black;
    font-weight: normal;
}

.tabLabel {vertical-align: middle; float: left; margin-right: 2px;}
.loader {vertical-align: middle; float: left; width: 4px;}

#rootTab ul { list-style-type: none; }

.stripesErrors {
    color:red;
    font-weight: bold;
}

.dataTable th {
    text-align: left;
}

.FAIL {
    color: red;
}

.SUCCESS {
    color: green;
}
.customDataTable-hover-forced {
    background-color: #eaeaea;
}
td.details-control {
    cursor: pointer;
    position: relative;
}
td.details-control:before {
    position:absolute;
    color: #808080;
    font-family: FontAwesome;
    top: 5px;
    left: 15px;
    content: "\f055";
    font-size: 18px;
}
tr.shown td.details-control:before {
    position:absolute;
    font-family: FontAwesome;
    top: 4px;
    left: 15px;
    content: "\f056";
}

.pending {
    color: rgb(214, 99, 3);
}

.ajax-loader {
    text-align: center;
    margin-top: 200px;
}
.center270{
    width: 310px;
    margin-left:auto;
    margin-right:auto;
}
#overlay-spinner {
    display: none;
}
#overlay {
    position: fixed;
    z-index: 100;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.3);
}
.hidden {
    display: none;
}
.spinner {
    height: 100%;
    text-align: center;
}
.spinner span {
    position: relative;
    margin: auto;
    top: 40% !important;
}
.spinner-content {
    position: relative;
    margin: auto;
    top: 40% !important;
    height: 76px;
    width: 300px;
    /*background-color: rgba(84, 84, 84, 1);*/
    background-color: #FFFFFF;

    border-radius: 10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    /*border: 2px solid #f5f5f5;*/
    border: 1px solid #1f1f1f;
}
.spinner-text {
    /*color: #f5f5f5;*/
    color: #1f1f1f;
    font-size: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
}
.about-popover {
    cursor: pointer;
}

.map-control-icon {
    clear: both;
    float: left;
    margin-left: 2px;
    margin-top: 2px;
    margin-right: 2px;
    cursor: pointer;
}
.map-control-label {
    line-height: 18px;
    cursor: pointer;
}
.map-control-box {
    margin-bottom: 1px;
    margin-right: 4px;
    font: 10px;
    background: white;
    width: 145px;
    border-bottom-left-radius: 2px;
    border-top-left-radius: 2px;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.14902);
    -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px;
    box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px;
}

#tabs-loader {
    text-align: center;
    margin-top: 200px;
}

.topBlock{
    margin-top:0px;
    display:block;
}
td.wrap {
    border-collapse: collapse;
    word-wrap:break-word;
    -ms-word-wrap:break-word;
    overflow-wrap:break-word;
}
div.wrap {
    border-collapse: collapse;
    word-wrap:break-word;
    -ms-word-wrap:break-word;
    overflow-wrap:break-word;
}

a.contactLink:link, a.contactLink:visited, a.contactLink:hover, a.contactLink:active {
	cursor: pointer;
	text-decoration: underline;
	color: blue;
}