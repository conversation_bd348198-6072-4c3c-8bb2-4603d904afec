/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.administration.tooltips;

import se.tss.ccis.core.persistence.entities.ToolTipEntity;
import se.tss.ccis.web.core.CCISCoreCommand;

import java.util.List;

public class TooltipCommand extends CCISCoreCommand{

    protected List<ToolTipEntity> toolTips;
    protected String name, header, body;

    public List<ToolTipEntity> getToolTips() {
        return toolTips;
    }

    public void setToolTips(List<ToolTipEntity> toolTips) {
        this.toolTips = toolTips;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeader() {
        return this.header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public String getBody() {
        return this.body;
    }

    public void setBody(String body) {
        this.body = body;
    }
}
