{"_type": "Project", "_id": "AAAAAAFF+h6SjaM2Hec=", "name": "Untitled", "ownedElements": [{"_type": "UMLCollaboration", "_id": "AAAAAAFeaymkStCFWGQ=", "_parent": {"$ref": "AAAAAAFF+h6SjaM2Hec="}, "name": "Collaboration1", "ownedElements": [{"_type": "UMLInteraction", "_id": "AAAAAAFeaymkStCGQCI=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Interaction1", "ownedElements": [{"_type": "UMLSequenceDiagram", "_id": "AAAAAAFeaymkStCH04c=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "useAReport", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "UMLFrameView", "_id": "AAAAAAFeaymkStCIqQg=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeaymkStCH04c="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeaymkStCJfwI=", "_parent": {"$ref": "AAAAAAFeaymkStCIqQg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 75.97900390625, "top": 10, "width": 69.64990234375, "height": 13, "autoResize": false, "underline": false, "text": "useAReport", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeaymkStCK3OM=", "_parent": {"$ref": "AAAAAAFeaymkStCIqQg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 10, "top": 10, "width": 60.97900390625, "height": 13, "autoResize": false, "underline": false, "text": "interaction", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 5, "top": 5, "width": 695, "height": 732, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeaymkStCJfwI="}, "frameTypeLabel": {"$ref": "AAAAAAFeaymkStCK3OM="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeayoSFtCXuuQ=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeayoSFtCYSoU=", "_parent": {"$ref": "AAAAAAFeayoSFtCXuuQ="}, "model": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeayoSF9CZU4E=", "_parent": {"$ref": "AAAAAAFeayoSFtCYSoU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 336, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeayoSF9Caf6g=", "_parent": {"$ref": "AAAAAAFeayoSFtCYSoU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 245, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "CCIS", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeayoSF9CbDfA=", "_parent": {"$ref": "AAAAAAFeayoSFtCYSoU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 336, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeayoSF9Cc+4g=", "_parent": {"$ref": "AAAAAAFeayoSFtCYSoU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 336, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 240, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeayoSF9CZU4E="}, "nameLabel": {"$ref": "AAAAAAFeayoSF9Caf6g="}, "namespaceLabel": {"$ref": "AAAAAAFeayoSF9CbDfA="}, "propertyLabel": {"$ref": "AAAAAAFeayoSF9Cc+4g="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeayoSF9CdCmo=", "_parent": {"$ref": "AAAAAAFeayoSFtCXuuQ="}, "model": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 276, "top": 80, "width": 1, "height": 472, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 240, "top": 40, "width": 72.736328125, "height": 512, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeayoSFtCYSoU="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeayoSF9CdCmo="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeaypL9tC3tyQ=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeaypL9tC4pLc=", "_parent": {"$ref": "AAAAAAFeaypL9tC3tyQ="}, "model": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeaypL9tC5XWc=", "_parent": {"$ref": "AAAAAAFeaypL9tC4pLc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 384, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeaypL9tC6IvQ=", "_parent": {"$ref": "AAAAAAFeaypL9tC4pLc="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 541, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "<PERSON>", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeaypL99C7BH8=", "_parent": {"$ref": "AAAAAAFeaypL9tC4pLc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 384, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeaypL99C89t4=", "_parent": {"$ref": "AAAAAAFeaypL9tC4pLc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 384, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 536, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeaypL9tC5XWc="}, "nameLabel": {"$ref": "AAAAAAFeaypL9tC6IvQ="}, "namespaceLabel": {"$ref": "AAAAAAFeaypL99C7BH8="}, "propertyLabel": {"$ref": "AAAAAAFeaypL99C89t4="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeaypL99C9HV4=", "_parent": {"$ref": "AAAAAAFeaypL9tC3tyQ="}, "model": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 572, "top": 80, "width": 1, "height": 625, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 536, "top": 40, "width": 72.736328125, "height": 665, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeaypL9tC4pLc="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeaypL99C9HV4="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeayq19dDZak0=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeayq19dDYXKM="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeayq19dDavAE=", "_parent": {"$ref": "AAAAAAFeayq19dDZak0="}, "model": {"$ref": "AAAAAAFeayq19dDYXKM="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeayq19dDboQQ=", "_parent": {"$ref": "AAAAAAFeayq19dDavAE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -256, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeayq19dDcUu8=", "_parent": {"$ref": "AAAAAAFeayq19dDavAE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 37, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "User", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeayq19tDd4nQ=", "_parent": {"$ref": "AAAAAAFeayq19dDavAE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -256, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeayq19tDeERM=", "_parent": {"$ref": "AAAAAAFeayq19dDavAE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -256, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 32, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeayq19dDboQQ="}, "nameLabel": {"$ref": "AAAAAAFeayq19dDcUu8="}, "namespaceLabel": {"$ref": "AAAAAAFeayq19tDd4nQ="}, "propertyLabel": {"$ref": "AAAAAAFeayq19tDeERM="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeayq19tDf/Fc=", "_parent": {"$ref": "AAAAAAFeayq19dDZak0="}, "model": {"$ref": "AAAAAAFeayq19dDYXKM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 68, "top": 80, "width": 1, "height": 617, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 32, "top": 40, "width": 72.736328125, "height": 657, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeayq19dDavAE="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeayq19tDf/Fc="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeaysChND78Rs=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeaysChND6bw0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeaysChND89zE=", "_parent": {"$ref": "AAAAAAFeaysChND78Rs="}, "model": {"$ref": "AAAAAAFeaysChND6bw0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 121, "top": 88, "width": 95.38623046875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaysChND78Rs="}, "edgePosition": 1, "underline": false, "text": "1 : OpenReports", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaysChdD9g8o=", "_parent": {"$ref": "AAAAAAFeaysChND78Rs="}, "model": {"$ref": "AAAAAAFeaysChND6bw0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 73, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeaysChND78Rs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaysChdD+OnY=", "_parent": {"$ref": "AAAAAAFeaysChND78Rs="}, "model": {"$ref": "AAAAAAFeaysChND6bw0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 108, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaysChND78Rs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeaysChdD/BXU=", "_parent": {"$ref": "AAAAAAFeaysChND78Rs="}, "model": {"$ref": "AAAAAAFeaysChND6bw0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 104, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "tail": {"$ref": "AAAAAAFeayq19tDf/Fc="}, "lineStyle": 0, "points": "68:104;269:104", "nameLabel": {"$ref": "AAAAAAFeaysChND89zE="}, "stereotypeLabel": {"$ref": "AAAAAAFeaysChdD9g8o="}, "propertyLabel": {"$ref": "AAAAAAFeaysChdD+OnY="}, "activation": {"$ref": "AAAAAAFeaysChdD/BXU="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeays9qtERvIA=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeays9qtEQPeQ="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeays9qtES/cE=", "_parent": {"$ref": "AAAAAAFeays9qtERvIA="}, "model": {"$ref": "AAAAAAFeays9qtEQPeQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 366, "top": 116, "width": 109.8271484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeays9qtERvIA="}, "edgePosition": 1, "underline": false, "text": "2 : FetchReportList", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeays9qtET4a8=", "_parent": {"$ref": "AAAAAAFeays9qtERvIA="}, "model": {"$ref": "AAAAAAFeays9qtEQPeQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 420, "top": 101, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeays9qtERvIA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeays9qtEUUw8=", "_parent": {"$ref": "AAAAAAFeays9qtERvIA="}, "model": {"$ref": "AAAAAAFeays9qtEQPeQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 420, "top": 136, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeays9qtERvIA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeays9q9EV4Jw=", "_parent": {"$ref": "AAAAAAFeays9qtERvIA="}, "model": {"$ref": "AAAAAAFeays9qtEQPeQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 565, "top": 132, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeaypL99C9HV4="}, "tail": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "lineStyle": 0, "points": "276:132;565:132", "nameLabel": {"$ref": "AAAAAAFeays9qtES/cE="}, "stereotypeLabel": {"$ref": "AAAAAAFeays9qtET4a8="}, "propertyLabel": {"$ref": "AAAAAAFeays9qtEUUw8="}, "activation": {"$ref": "AAAAAAFeays9q9EV4Jw="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeayulPtEnMzA=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeayulPtEmBEM="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeayulPtEotmA=", "_parent": {"$ref": "AAAAAAFeayulPtEnMzA="}, "model": {"$ref": "AAAAAAFeayulPtEmBEM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 374, "top": 162, "width": 97.5380859375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeayulPtEnMzA="}, "edgePosition": 1, "underline": false, "text": "3 : ListOfReports", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeayulPtEpyKU=", "_parent": {"$ref": "AAAAAAFeayulPtEnMzA="}, "model": {"$ref": "AAAAAAFeayulPtEmBEM="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 422, "top": 177, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeayulPtEnMzA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeayulPtEq/F4=", "_parent": {"$ref": "AAAAAAFeayulPtEnMzA="}, "model": {"$ref": "AAAAAAFeayulPtEmBEM="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 423, "top": 142, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeayulPtEnMzA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeayulPtEr6Y8=", "_parent": {"$ref": "AAAAAAFeayulPtEnMzA="}, "model": {"$ref": "AAAAAAFeayulPtEmBEM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 158, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "tail": {"$ref": "AAAAAAFeaypL99C9HV4="}, "lineStyle": 0, "points": "565:158;282:158", "nameLabel": {"$ref": "AAAAAAFeayulPtEotmA="}, "stereotypeLabel": {"$ref": "AAAAAAFeayulPtEpyKU="}, "propertyLabel": {"$ref": "AAAAAAFeayulPtEq/F4="}, "activation": {"$ref": "AAAAAAFeayulPtEr6Y8="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeayvotNE9tWY=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeayvos9E81nU="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeayvotNE+QXE=", "_parent": {"$ref": "AAAAAAFeayvotNE9tWY="}, "model": {"$ref": "AAAAAAFeayvos9E81nU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 251, "top": 187, "width": 140.88623046875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeayvotNE9tWY="}, "edgePosition": 1, "underline": false, "text": "4 : ParseList", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeayvotNE/VWU=", "_parent": {"$ref": "AAAAAAFeayvotNE9tWY="}, "model": {"$ref": "AAAAAAFeayvos9E81nU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 336, "top": 187, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeayvotNE9tWY="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeayvotNFAcIQ=", "_parent": {"$ref": "AAAAAAFeayvotNE9tWY="}, "model": {"$ref": "AAAAAAFeayvos9E81nU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 302, "top": 188, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeayvotNE9tWY="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeayvotNFBVdM=", "_parent": {"$ref": "AAAAAAFeayvotNE9tWY="}, "model": {"$ref": "AAAAAAFeayvos9E81nU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 204, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "tail": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "lineStyle": 0, "points": "282:184;312:184;312:204;282:204", "nameLabel": {"$ref": "AAAAAAFeayvotNE+QXE="}, "stereotypeLabel": {"$ref": "AAAAAAFeayvotNE/VWU="}, "propertyLabel": {"$ref": "AAAAAAFeayvotNFAcIQ="}, "activation": {"$ref": "AAAAAAFeayvotNFBVdM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeayy2StFUlgQ=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeayy2StFTCw8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeayy2StFV9pk=", "_parent": {"$ref": "AAAAAAFeayy2StFUlgQ="}, "model": {"$ref": "AAAAAAFeayy2StFTCw8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 104, "top": 236, "width": 140.88623046875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeayy2StFUlgQ="}, "edgePosition": 1, "underline": false, "text": "5 : DisplayReportNames", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeayy2StFWCMw=", "_parent": {"$ref": "AAAAAAFeayy2StFUlgQ="}, "model": {"$ref": "AAAAAAFeayy2StFTCw8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 174, "top": 251, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeayy2StFUlgQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeayy2StFXLg4=", "_parent": {"$ref": "AAAAAAFeayy2StFUlgQ="}, "model": {"$ref": "AAAAAAFeayy2StFTCw8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 175, "top": 216, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeayy2StFUlgQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeayy2S9FYV8I=", "_parent": {"$ref": "AAAAAAFeayy2StFUlgQ="}, "model": {"$ref": "AAAAAAFeayy2StFTCw8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 61, "top": 232, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayq19tDf/Fc="}, "tail": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "lineStyle": 0, "points": "276:232;74:232", "nameLabel": {"$ref": "AAAAAAFeayy2StFV9pk="}, "stereotypeLabel": {"$ref": "AAAAAAFeayy2StFWCMw="}, "propertyLabel": {"$ref": "AAAAAAFeayy2StFXLg4="}, "activation": {"$ref": "AAAAAAFeayy2S9FYV8I="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay0uZtFqZQs=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay0uZtFpIFo="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay0uZ9FrXzA=", "_parent": {"$ref": "AAAAAAFeay0uZtFqZQs="}, "model": {"$ref": "AAAAAAFeay0uZtFpIFo="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 116, "top": 289, "width": 104.05078125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay0uZtFqZQs="}, "edgePosition": 1, "underline": false, "text": "6 : Select report A", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay0uZ9FsfVw=", "_parent": {"$ref": "AAAAAAFeay0uZtFqZQs="}, "model": {"$ref": "AAAAAAFeay0uZtFpIFo="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 274, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay0uZtFqZQs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay0uZ9FtHmY=", "_parent": {"$ref": "AAAAAAFeay0uZtFqZQs="}, "model": {"$ref": "AAAAAAFeay0uZtFpIFo="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 309, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay0uZtFqZQs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay0uZ9Fu8Zo=", "_parent": {"$ref": "AAAAAAFeay0uZtFqZQs="}, "model": {"$ref": "AAAAAAFeay0uZtFpIFo="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 305, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "tail": {"$ref": "AAAAAAFeayq19tDf/Fc="}, "lineStyle": 0, "points": "68:305;269:305", "nameLabel": {"$ref": "AAAAAAFeay0uZ9FrXzA="}, "stereotypeLabel": {"$ref": "AAAAAAFeay0uZ9FsfVw="}, "propertyLabel": {"$ref": "AAAAAAFeay0uZ9FtHmY="}, "activation": {"$ref": "AAAAAAFeay0uZ9Fu8Zo="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay1rfdGAKys=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay1rfdF/n68="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay1rfdGBdNE=", "_parent": {"$ref": "AAAAAAFeay1rfdGAKys="}, "model": {"$ref": "AAAAAAFeay1rfdF/n68="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 358, "top": 315, "width": 130.05712890625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay1rfdGAKys="}, "edgePosition": 1, "underline": false, "text": "7 : fetchReportParams", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay1rfdGCRjo=", "_parent": {"$ref": "AAAAAAFeay1rfdGAKys="}, "model": {"$ref": "AAAAAAFeay1rfdF/n68="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 423, "top": 300, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay1rfdGAKys="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay1rfdGD0ow=", "_parent": {"$ref": "AAAAAAFeay1rfdGAKys="}, "model": {"$ref": "AAAAAAFeay1rfdF/n68="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 423, "top": 335, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay1rfdGAKys="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay1rfdGErqA=", "_parent": {"$ref": "AAAAAAFeay1rfdGAKys="}, "model": {"$ref": "AAAAAAFeay1rfdF/n68="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 565, "top": 331, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeaypL99C9HV4="}, "tail": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "lineStyle": 0, "points": "282:331;565:331", "nameLabel": {"$ref": "AAAAAAFeay1rfdGBdNE="}, "stereotypeLabel": {"$ref": "AAAAAAFeay1rfdGCRjo="}, "propertyLabel": {"$ref": "AAAAAAFeay1rfdGD0ow="}, "activation": {"$ref": "AAAAAAFeay1rfdGErqA="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay2sh9GWNE0=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay2sh9GVHG0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay2sh9GXNBo=", "_parent": {"$ref": "AAAAAAFeay2sh9GWNE0="}, "model": {"$ref": "AAAAAAFeay2sh9GVHG0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 368, "top": 363, "width": 117.04443359375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay2sh9GWNE0="}, "edgePosition": 1, "underline": false, "text": "8 : reportParamsList", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay2sh9GYAm8=", "_parent": {"$ref": "AAAAAAFeay2sh9GWNE0="}, "model": {"$ref": "AAAAAAFeay2sh9GVHG0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 426, "top": 378, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay2sh9GWNE0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay2sh9GZw5Y=", "_parent": {"$ref": "AAAAAAFeay2sh9GWNE0="}, "model": {"$ref": "AAAAAAFeay2sh9GVHG0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 427, "top": 343, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay2sh9GWNE0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay2siNGatoM=", "_parent": {"$ref": "AAAAAAFeay2sh9GWNE0="}, "model": {"$ref": "AAAAAAFeay2sh9GVHG0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 359, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "tail": {"$ref": "AAAAAAFeaypL99C9HV4="}, "lineStyle": 0, "points": "572:359;282:359", "nameLabel": {"$ref": "AAAAAAFeay2sh9GXNBo="}, "stereotypeLabel": {"$ref": "AAAAAAFeay2sh9GYAm8="}, "propertyLabel": {"$ref": "AAAAAAFeay2sh9GZw5Y="}, "activation": {"$ref": "AAAAAAFeay2siNGatoM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay4M59Gs8vE=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay4M59GrXD0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay4M6NGt/gg=", "_parent": {"$ref": "AAAAAAFeay4M59Gs8vE="}, "model": {"$ref": "AAAAAAFeay4M59GrXD0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 226, "top": 387, "width": 190.0615234375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay4M59Gs8vE="}, "edgePosition": 1, "underline": false, "text": "9 : Parse list and generate inputs", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay4M6NGuvMQ=", "_parent": {"$ref": "AAAAAAFeay4M59Gs8vE="}, "model": {"$ref": "AAAAAAFeay4M59GrXD0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 336, "top": 387, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay4M59Gs8vE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay4M6NGvKRg=", "_parent": {"$ref": "AAAAAAFeay4M59Gs8vE="}, "model": {"$ref": "AAAAAAFeay4M59GrXD0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 302, "top": 388, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay4M59Gs8vE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay4M6NGwbro=", "_parent": {"$ref": "AAAAAAFeay4M59Gs8vE="}, "model": {"$ref": "AAAAAAFeay4M59GrXD0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 404, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "tail": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "lineStyle": 0, "points": "282:384;312:384;312:404;282:404", "nameLabel": {"$ref": "AAAAAAFeay4M6NGt/gg="}, "stereotypeLabel": {"$ref": "AAAAAAFeay4M6NGuvMQ="}, "propertyLabel": {"$ref": "AAAAAAFeay4M6NGvKRg="}, "activation": {"$ref": "AAAAAAFeay4M6NGwbro="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay5EDtHCyxc=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay5EDtHBBys="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay5EDtHDVrY=", "_parent": {"$ref": "AAAAAAFeay5EDtHCyxc="}, "model": {"$ref": "AAAAAAFeay5EDtHBBys="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 126, "top": 436, "width": 96.1162109375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay5EDtHCyxc="}, "edgePosition": 1, "underline": false, "text": "10 : Show inputs", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay5EDtHEjZY=", "_parent": {"$ref": "AAAAAAFeay5EDtHCyxc="}, "model": {"$ref": "AAAAAAFeay5EDtHBBys="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 174, "top": 451, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay5EDtHCyxc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay5EDtHFWAM=", "_parent": {"$ref": "AAAAAAFeay5EDtHCyxc="}, "model": {"$ref": "AAAAAAFeay5EDtHBBys="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 175, "top": 416, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay5EDtHCyxc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay5EDtHGVZ0=", "_parent": {"$ref": "AAAAAAFeay5EDtHCyxc="}, "model": {"$ref": "AAAAAAFeay5EDtHBBys="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 61, "top": 432, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayq19tDf/Fc="}, "tail": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "lineStyle": 0, "points": "276:432;74:432", "nameLabel": {"$ref": "AAAAAAFeay5EDtHDVrY="}, "stereotypeLabel": {"$ref": "AAAAAAFeay5EDtHEjZY="}, "propertyLabel": {"$ref": "AAAAAAFeay5EDtHFWAM="}, "activation": {"$ref": "AAAAAAFeay5EDtHGVZ0="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay62odHZOt4=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay62odHYHpY="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay62odHa6KU=", "_parent": {"$ref": "AAAAAAFeay62odHZOt4="}, "model": {"$ref": "AAAAAAFeay62odHYHpY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 90, "top": 464, "width": 156.78076171875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay62odHZOt4="}, "edgePosition": 1, "underline": false, "text": "11 : Enter criteria press OK", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay62odHb+e4=", "_parent": {"$ref": "AAAAAAFeay62odHZOt4="}, "model": {"$ref": "AAAAAAFeay62odHYHpY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 449, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay62odHZOt4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay62odHc9gs=", "_parent": {"$ref": "AAAAAAFeay62odHZOt4="}, "model": {"$ref": "AAAAAAFeay62odHYHpY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 484, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay62odHZOt4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay62odHdjNo=", "_parent": {"$ref": "AAAAAAFeay62odHZOt4="}, "model": {"$ref": "AAAAAAFeay62odHYHpY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 480, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "tail": {"$ref": "AAAAAAFeayq19tDf/Fc="}, "lineStyle": 0, "points": "68:480;269:480", "nameLabel": {"$ref": "AAAAAAFeay62odHa6KU="}, "stereotypeLabel": {"$ref": "AAAAAAFeay62odHb+e4="}, "propertyLabel": {"$ref": "AAAAAAFeay62odHc9gs="}, "activation": {"$ref": "AAAAAAFeay62odHdjNo="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay8dStHvi2c=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay8dStHu9B0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay8dS9Hwpxc=", "_parent": {"$ref": "AAAAAAFeay8dStHvi2c="}, "model": {"$ref": "AAAAAAFeay8dStHu9B0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 377, "top": 488, "width": 93.92626953125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay8dStHvi2c="}, "edgePosition": 1, "underline": false, "text": "12 : Post criteria", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay8dS9Hx15U=", "_parent": {"$ref": "AAAAAAFeay8dStHvi2c="}, "model": {"$ref": "AAAAAAFeay8dStHu9B0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 423, "top": 473, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay8dStHvi2c="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay8dS9HyMQs=", "_parent": {"$ref": "AAAAAAFeay8dStHvi2c="}, "model": {"$ref": "AAAAAAFeay8dStHu9B0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 423, "top": 508, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay8dStHvi2c="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay8dS9Hz3kc=", "_parent": {"$ref": "AAAAAAFeay8dStHvi2c="}, "model": {"$ref": "AAAAAAFeay8dStHu9B0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 565, "top": 504, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeaypL99C9HV4="}, "tail": {"$ref": "AAAAAAFeayoSF9CdCmo="}, "lineStyle": 0, "points": "282:504;565:504", "nameLabel": {"$ref": "AAAAAAFeay8dS9Hwpxc="}, "stereotypeLabel": {"$ref": "AAAAAAFeay8dS9Hx15U="}, "propertyLabel": {"$ref": "AAAAAAFeay8dS9HyMQs="}, "activation": {"$ref": "AAAAAAFeay8dS9Hz3kc="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeay99UdIGE9E=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeay99UdIFCOw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeay99UdIHe64=", "_parent": {"$ref": "AAAAAAFeay99UdIGE9E="}, "model": {"$ref": "AAAAAAFeay99UdIFCOw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 552, "top": 555, "width": 118.51708984375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay99UdIGE9E="}, "edgePosition": 1, "underline": false, "text": "13 : GenerateReport", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay99UdIIJHQ=", "_parent": {"$ref": "AAAAAAFeay99UdIGE9E="}, "model": {"$ref": "AAAAAAFeay99UdIFCOw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 626, "top": 555, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeay99UdIGE9E="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeay99UdIJHnA=", "_parent": {"$ref": "AAAAAAFeay99UdIGE9E="}, "model": {"$ref": "AAAAAAFeay99UdIFCOw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 592, "top": 556, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeay99UdIGE9E="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeay99UdIKopo=", "_parent": {"$ref": "AAAAAAFeay99UdIGE9E="}, "model": {"$ref": "AAAAAAFeay99UdIFCOw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 565, "top": 572, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeaypL99C9HV4="}, "tail": {"$ref": "AAAAAAFeaypL99C9HV4="}, "lineStyle": 0, "points": "572:552;602:552;602:572;578:572", "nameLabel": {"$ref": "AAAAAAFeay99UdIHe64="}, "stereotypeLabel": {"$ref": "AAAAAAFeay99UdIIJHQ="}, "propertyLabel": {"$ref": "AAAAAAFeay99UdIJHnA="}, "activation": {"$ref": "AAAAAAFeay99UdIKopo="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeazBgndIdrWE=", "_parent": {"$ref": "AAAAAAFeaymkStCH04c="}, "model": {"$ref": "AAAAAAFeazBgndIcXp4="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeazBgndIesyI=", "_parent": {"$ref": "AAAAAAFeazBgndIdrWE="}, "model": {"$ref": "AAAAAAFeazBgndIcXp4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 636, "width": 106.95166015625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazBgndIdrWE="}, "edgePosition": 1, "underline": false, "text": "14 : RenderReport", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazBgndIffjk=", "_parent": {"$ref": "AAAAAAFeazBgndIdrWE="}, "model": {"$ref": "AAAAAAFeazBgndIcXp4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 322, "top": 651, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeazBgndIdrWE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazBgndIg1Is=", "_parent": {"$ref": "AAAAAAFeazBgndIdrWE="}, "model": {"$ref": "AAAAAAFeazBgndIcXp4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 323, "top": 616, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazBgndIdrWE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeazBgndIhONw=", "_parent": {"$ref": "AAAAAAFeazBgndIdrWE="}, "model": {"$ref": "AAAAAAFeazBgndIcXp4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 61, "top": 632, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeayq19tDf/Fc="}, "tail": {"$ref": "AAAAAAFeaypL99C9HV4="}, "lineStyle": 0, "points": "572:632;74:632", "nameLabel": {"$ref": "AAAAAAFeazBgndIesyI="}, "stereotypeLabel": {"$ref": "AAAAAAFeazBgndIffjk="}, "propertyLabel": {"$ref": "AAAAAAFeazBgndIg1Is="}, "activation": {"$ref": "AAAAAAFeazBgndIhONw="}, "showProperty": true, "showType": true}], "showSequenceNumber": true, "showSignature": true, "showActivation": true}], "visibility": "public", "isReentrant": true, "messages": [{"_type": "UMLMessage", "_id": "AAAAAAFeaysChND6bw0=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "OpenReports", "source": {"$ref": "AAAAAAFeayq19dDYXKM="}, "target": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeays9qtEQPeQ=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "FetchReportList", "source": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "target": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeayulPtEmBEM=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "ListOfReports", "source": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "target": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeayvos9E81nU=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "ParseList", "source": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "target": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeayy2StFTCw8=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "DisplayReportNames", "source": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "target": {"$ref": "AAAAAAFeayq19dDYXKM="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay0uZtFpIFo=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "Select report A", "source": {"$ref": "AAAAAAFeayq19dDYXKM="}, "target": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay1rfdF/n68=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "fetchReportParams", "source": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "target": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay2sh9GVHG0=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "reportParamsList", "source": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "target": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay4M59GrXD0=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "Parse list and generate inputs", "source": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "target": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay5EDtHBBys=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "Show inputs", "source": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "target": {"$ref": "AAAAAAFeayq19dDYXKM="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay62odHYHpY=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "Enter criteria press OK", "source": {"$ref": "AAAAAAFeayq19dDYXKM="}, "target": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay8dStHu9B0=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "Post criteria", "source": {"$ref": "AAAAAAFeayoSFtCW3tU="}, "target": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeay99UdIFCOw=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "GenerateReport", "source": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "target": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeazBgndIcXp4=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "RenderReport", "source": {"$ref": "AAAAAAFeaypL9tC29Cg="}, "target": {"$ref": "AAAAAAFeayq19dDYXKM="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}], "participants": [{"_type": "UMLLifeline", "_id": "AAAAAAFeayoSFtCW3tU=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "CCIS", "visibility": "public", "represent": {"$ref": "AAAAAAFeayoSFtCVzRk="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeaypL9tC29Cg=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "<PERSON>", "visibility": "public", "represent": {"$ref": "AAAAAAFeaypL9dC1ZGE="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeayq19dDYXKM=", "_parent": {"$ref": "AAAAAAFeaymkStCGQCI="}, "name": "User", "visibility": "public", "represent": {"$ref": "AAAAAAFeayq19NDXYsY="}, "isMultiInstance": false}]}, {"_type": "UMLInteraction", "_id": "AAAAAAFeazNaMNMHEzo=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Interaction2", "ownedElements": [{"_type": "UMLSequenceDiagram", "_id": "AAAAAAFeazNaMNMIdls=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "saveSearchParams", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "UMLFrameView", "_id": "AAAAAAFeazNaMNMJGtc=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeazNaMNMIdls="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeazNaMNMK7Xc=", "_parent": {"$ref": "AAAAAAFeazNaMNMJGtc="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 75.97900390625, "top": 10, "width": 114.43896484375, "height": 13, "autoResize": false, "underline": false, "text": "saveSearchParams", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeazNaMNMLApI=", "_parent": {"$ref": "AAAAAAFeazNaMNMJGtc="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 10, "top": 10, "width": 60.97900390625, "height": 13, "autoResize": false, "underline": false, "text": "interaction", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 5, "top": 5, "width": 695, "height": 595, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeazNaMNMK7Xc="}, "frameTypeLabel": {"$ref": "AAAAAAFeazNaMNMLApI="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeazmMEdM3bQU=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeazmMEdM4u+E=", "_parent": {"$ref": "AAAAAAFeazmMEdM3bQU="}, "model": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeazmMEdM5taY=", "_parent": {"$ref": "AAAAAAFeazmMEdM4u+E="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeazmMEdM6Ekg=", "_parent": {"$ref": "AAAAAAFeazmMEdM4u+E="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 53, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "User", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeazmMEdM7F+4=", "_parent": {"$ref": "AAAAAAFeazmMEdM4u+E="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction2)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeazmMEtM8GEc=", "_parent": {"$ref": "AAAAAAFeazmMEdM4u+E="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 48, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeazmMEdM5taY="}, "nameLabel": {"$ref": "AAAAAAFeazmMEdM6Ekg="}, "namespaceLabel": {"$ref": "AAAAAAFeazmMEdM7F+4="}, "propertyLabel": {"$ref": "AAAAAAFeazmMEtM8GEc="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeazmMEtM96pM=", "_parent": {"$ref": "AAAAAAFeazmMEdM3bQU="}, "model": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 84, "top": 80, "width": 1, "height": 472, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 48, "top": 40, "width": 72.736328125, "height": 512, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeazmMEdM4u+E="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeazmMEtM96pM="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeaznFMdNXZfE=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeaznFMdNYqjg=", "_parent": {"$ref": "AAAAAAFeaznFMdNXZfE="}, "model": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeaznFMtNZI6Q=", "_parent": {"$ref": "AAAAAAFeaznFMdNYqjg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeaznFMtNaLJo=", "_parent": {"$ref": "AAAAAAFeaznFMdNYqjg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 269, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "CCIS", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeaznFMtNbpko=", "_parent": {"$ref": "AAAAAAFeaznFMdNYqjg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction2)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeaznFMtNclHk=", "_parent": {"$ref": "AAAAAAFeaznFMdNYqjg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 264, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeaznFMtNZI6Q="}, "nameLabel": {"$ref": "AAAAAAFeaznFMtNaLJo="}, "namespaceLabel": {"$ref": "AAAAAAFeaznFMtNbpko="}, "propertyLabel": {"$ref": "AAAAAAFeaznFMtNclHk="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeaznFMtNd/zY=", "_parent": {"$ref": "AAAAAAFeaznFMdNXZfE="}, "model": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 300, "top": 80, "width": 1, "height": 456, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 264, "top": 40, "width": 72.736328125, "height": 496, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeaznFMdNYqjg="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeaznFMtNd/zY="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeazov5NN3GqU=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeazov5NN2ICM="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeazov5NN45Hs=", "_parent": {"$ref": "AAAAAAFeazov5NN3GqU="}, "model": {"$ref": "AAAAAAFeazov5NN2ICM="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeazov5NN5twg=", "_parent": {"$ref": "AAAAAAFeazov5NN45Hs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeazov5NN60bw=", "_parent": {"$ref": "AAAAAAFeazov5NN45Hs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 509, "top": 47, "width": 94.56982421875, "height": 13, "autoResize": false, "underline": false, "text": "<PERSON>", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeazov5dN7z0Q=", "_parent": {"$ref": "AAAAAAFeazov5NN45Hs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction2)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeazov5dN8nz4=", "_parent": {"$ref": "AAAAAAFeazov5NN45Hs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 504, "top": 40, "width": 104.56982421875, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeazov5NN5twg="}, "nameLabel": {"$ref": "AAAAAAFeazov5NN60bw="}, "namespaceLabel": {"$ref": "AAAAAAFeazov5dN7z0Q="}, "propertyLabel": {"$ref": "AAAAAAFeazov5dN8nz4="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeazov5dN9wkg=", "_parent": {"$ref": "AAAAAAFeazov5NN3GqU="}, "model": {"$ref": "AAAAAAFeazov5NN2ICM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 556, "top": 80, "width": 1, "height": 448, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 504, "top": 40, "width": 104.56982421875, "height": 488, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeazov5NN45Hs="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeazov5dN9wkg="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeazs8wtPYajU=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeazs8wtPX9r4="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeazs8wtPZNeE=", "_parent": {"$ref": "AAAAAAFeazs8wtPYajU="}, "model": {"$ref": "AAAAAAFeazs8wtPX9r4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 109, "top": 112, "width": 171.22802734375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazs8wtPYajU="}, "edgePosition": 1, "underline": false, "text": "1 : Show Report With Params", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazs8wtPa7t4=", "_parent": {"$ref": "AAAAAAFeazs8wtPYajU="}, "model": {"$ref": "AAAAAAFeazs8wtPX9r4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 194, "top": 127, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeazs8wtPYajU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazs8wtPbIyM=", "_parent": {"$ref": "AAAAAAFeazs8wtPYajU="}, "model": {"$ref": "AAAAAAFeazs8wtPX9r4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 195, "top": 92, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazs8wtPYajU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeazs8wtPcR90=", "_parent": {"$ref": "AAAAAAFeazs8wtPYajU="}, "model": {"$ref": "AAAAAAFeazs8wtPX9r4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 77, "top": 108, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeazmMEtM96pM="}, "tail": {"$ref": "AAAAAAFeaznFMtNd/zY="}, "lineStyle": 0, "points": "300:108;90:108", "nameLabel": {"$ref": "AAAAAAFeazs8wtPZNeE="}, "stereotypeLabel": {"$ref": "AAAAAAFeazs8wtPa7t4="}, "propertyLabel": {"$ref": "AAAAAAFeazs8wtPbIyM="}, "activation": {"$ref": "AAAAAAFeazs8wtPcR90="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeazuxWdPvAzs=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeazuxWdPugjQ="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeazuxWtPwuqk=", "_parent": {"$ref": "AAAAAAFeazuxWdPvAzs="}, "model": {"$ref": "AAAAAAFeazuxWdPugjQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 41, "top": 139, "width": 164.01708984375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazuxWdPvAzs="}, "edgePosition": 1, "underline": false, "text": "2 : Set search criteria values", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazuxWtPxme8=", "_parent": {"$ref": "AAAAAAFeazuxWdPvAzs="}, "model": {"$ref": "AAAAAAFeazuxWdPugjQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 138, "top": 139, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeazuxWdPvAzs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazuxWtPy0Jo=", "_parent": {"$ref": "AAAAAAFeazuxWdPvAzs="}, "model": {"$ref": "AAAAAAFeazuxWdPugjQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 104, "top": 140, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazuxWdPvAzs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeazuxWtPz60U=", "_parent": {"$ref": "AAAAAAFeazuxWdPvAzs="}, "model": {"$ref": "AAAAAAFeazuxWdPugjQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 77, "top": 156, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeazmMEtM96pM="}, "tail": {"$ref": "AAAAAAFeazmMEtM96pM="}, "lineStyle": 0, "points": "84:136;114:136;114:156;90:156", "nameLabel": {"$ref": "AAAAAAFeazuxWtPwuqk="}, "stereotypeLabel": {"$ref": "AAAAAAFeazuxWtPxme8="}, "propertyLabel": {"$ref": "AAAAAAFeazuxWtPy0Jo="}, "activation": {"$ref": "AAAAAAFeazuxWtPz60U="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeazyBv9QF+8A=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeazyBv9QEECM="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeazyBv9QGqD8=", "_parent": {"$ref": "AAAAAAFeazyBv9QF+8A="}, "model": {"$ref": "AAAAAAFeazyBv9QEECM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 143, "top": 176, "width": 90.3271484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazyBv9QF+8A="}, "edgePosition": 1, "underline": false, "text": "3 : Save search", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazyBv9QHfxA=", "_parent": {"$ref": "AAAAAAFeazyBv9QF+8A="}, "model": {"$ref": "AAAAAAFeazyBv9QEECM="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 188, "top": 161, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeazyBv9QF+8A="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazyBv9QIw/k=", "_parent": {"$ref": "AAAAAAFeazyBv9QF+8A="}, "model": {"$ref": "AAAAAAFeazyBv9QEECM="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 188, "top": 196, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazyBv9QF+8A="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeazyBv9QJc8Y=", "_parent": {"$ref": "AAAAAAFeazyBv9QF+8A="}, "model": {"$ref": "AAAAAAFeazyBv9QEECM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 293, "top": 192, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeaznFMtNd/zY="}, "tail": {"$ref": "AAAAAAFeazmMEtM96pM="}, "lineStyle": 0, "points": "84:192;293:192", "nameLabel": {"$ref": "AAAAAAFeazyBv9QGqD8="}, "stereotypeLabel": {"$ref": "AAAAAAFeazyBv9QHfxA="}, "propertyLabel": {"$ref": "AAAAAAFeazyBv9QIw/k="}, "activation": {"$ref": "AAAAAAFeazyBv9QJc8Y="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeazy5KtQbcCg=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeazy5KtQaKl8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeazy5K9Qcovg=", "_parent": {"$ref": "AAAAAAFeazy5KtQbcCg="}, "model": {"$ref": "AAAAAAFeazy5KtQaKl8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 252, "top": 219, "width": 186.41796875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazy5KtQbcCg="}, "edgePosition": 1, "underline": false, "text": "4 : Save criterias linked to report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazy5K9Qdpz0=", "_parent": {"$ref": "AAAAAAFeazy5KtQbcCg="}, "model": {"$ref": "AAAAAAFeazy5KtQaKl8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 360, "top": 219, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeazy5KtQbcCg="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeazy5K9Qedo0=", "_parent": {"$ref": "AAAAAAFeazy5KtQbcCg="}, "model": {"$ref": "AAAAAAFeazy5KtQaKl8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 326, "top": 220, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeazy5KtQbcCg="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeazy5K9QfNQ4=", "_parent": {"$ref": "AAAAAAFeazy5KtQbcCg="}, "model": {"$ref": "AAAAAAFeazy5KtQaKl8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 293, "top": 236, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeaznFMtNd/zY="}, "tail": {"$ref": "AAAAAAFeaznFMtNd/zY="}, "lineStyle": 0, "points": "306:216;336:216;336:236;306:236", "nameLabel": {"$ref": "AAAAAAFeazy5K9Qcovg="}, "stereotypeLabel": {"$ref": "AAAAAAFeazy5K9Qdpz0="}, "propertyLabel": {"$ref": "AAAAAAFeazy5K9Qedo0="}, "activation": {"$ref": "AAAAAAFeazy5K9QfNQ4="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeaz0c7NQxIwg=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeaz0c7NQw1WU="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeaz0c7NQyuYg=", "_parent": {"$ref": "AAAAAAFeaz0c7NQxIwg="}, "model": {"$ref": "AAAAAAFeaz0c7NQw1WU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 155, "top": 272, "width": 78.04443359375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz0c7NQxIwg="}, "edgePosition": 1, "underline": false, "text": "5 : Done", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz0c7NQzDkw=", "_parent": {"$ref": "AAAAAAFeaz0c7NQxIwg="}, "model": {"$ref": "AAAAAAFeaz0c7NQw1WU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 194, "top": 287, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeaz0c7NQxIwg="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz0c7dQ0soc=", "_parent": {"$ref": "AAAAAAFeaz0c7NQxIwg="}, "model": {"$ref": "AAAAAAFeaz0c7NQw1WU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 195, "top": 252, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz0c7NQxIwg="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeaz0c7dQ1GiM=", "_parent": {"$ref": "AAAAAAFeaz0c7NQxIwg="}, "model": {"$ref": "AAAAAAFeaz0c7NQw1WU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 77, "top": 268, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeazmMEtM96pM="}, "tail": {"$ref": "AAAAAAFeaznFMtNd/zY="}, "lineStyle": 0, "points": "300:268;90:268", "nameLabel": {"$ref": "AAAAAAFeaz0c7NQyuYg="}, "stereotypeLabel": {"$ref": "AAAAAAFeaz0c7NQzDkw="}, "propertyLabel": {"$ref": "AAAAAAFeaz0c7dQ0soc="}, "activation": {"$ref": "AAAAAAFeaz0c7dQ1GiM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeaz1AFtRHfIQ=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeaz1AFdRGrNs="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeaz1AFtRI+jY=", "_parent": {"$ref": "AAAAAAFeaz1AFtRHfIQ="}, "model": {"$ref": "AAAAAAFeaz1AFdRGrNs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 149, "top": 288, "width": 78.04443359375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz1AFtRHfIQ="}, "edgePosition": 1, "underline": false, "text": "6 : Press OK", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz1AFtRJ6y0=", "_parent": {"$ref": "AAAAAAFeaz1AFtRHfIQ="}, "model": {"$ref": "AAAAAAFeaz1AFdRGrNs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 188, "top": 273, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeaz1AFtRHfIQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz1AFtRK3NA=", "_parent": {"$ref": "AAAAAAFeaz1AFtRHfIQ="}, "model": {"$ref": "AAAAAAFeaz1AFdRGrNs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 188, "top": 308, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz1AFtRHfIQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeaz1AFtRLnGI=", "_parent": {"$ref": "AAAAAAFeaz1AFtRHfIQ="}, "model": {"$ref": "AAAAAAFeaz1AFdRGrNs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 293, "top": 304, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeaznFMtNd/zY="}, "tail": {"$ref": "AAAAAAFeazmMEtM96pM="}, "lineStyle": 0, "points": "84:304;293:304", "nameLabel": {"$ref": "AAAAAAFeaz1AFtRI+jY="}, "stereotypeLabel": {"$ref": "AAAAAAFeaz1AFtRJ6y0="}, "propertyLabel": {"$ref": "AAAAAAFeaz1AFtRK3NA="}, "activation": {"$ref": "AAAAAAFeaz1AFtRLnGI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeaz1oedRdHCU=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeaz1oeNRcEF0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeaz1oedRe8ZU=", "_parent": {"$ref": "AAAAAAFeaz1oedRdHCU="}, "model": {"$ref": "AAAAAAFeaz1oeNRcEF0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 373, "top": 312, "width": 109.83984375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz1oedRdHCU="}, "edgePosition": 1, "underline": false, "text": "7 : Generate report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz1oedRf5E0=", "_parent": {"$ref": "AAAAAAFeaz1oedRdHCU="}, "model": {"$ref": "AAAAAAFeaz1oeNRcEF0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 427, "top": 297, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeaz1oedRdHCU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz1oedRgH4g=", "_parent": {"$ref": "AAAAAAFeaz1oedRdHCU="}, "model": {"$ref": "AAAAAAFeaz1oeNRcEF0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 427, "top": 332, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz1oedRdHCU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeaz1oedRh6jA=", "_parent": {"$ref": "AAAAAAFeaz1oedRdHCU="}, "model": {"$ref": "AAAAAAFeaz1oeNRcEF0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 549, "top": 328, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeazov5dN9wkg="}, "tail": {"$ref": "AAAAAAFeaznFMtNd/zY="}, "lineStyle": 0, "points": "306:328;549:328", "nameLabel": {"$ref": "AAAAAAFeaz1oedRe8ZU="}, "stereotypeLabel": {"$ref": "AAAAAAFeaz1oedRf5E0="}, "propertyLabel": {"$ref": "AAAAAAFeaz1oedRgH4g="}, "activation": {"$ref": "AAAAAAFeaz1oedRh6jA="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeaz2cVNRzPfU=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeaz2cVNRyEBY="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeaz2cVNR0cVE=", "_parent": {"$ref": "AAAAAAFeaz2cVNRzPfU="}, "model": {"$ref": "AAAAAAFeaz2cVNRyEBY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 547, "top": 355, "width": 109.83984375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz2cVNRzPfU="}, "edgePosition": 1, "underline": false, "text": "8 : Generate report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz2cVNR1d/c=", "_parent": {"$ref": "AAAAAAFeaz2cVNRzPfU="}, "model": {"$ref": "AAAAAAFeaz2cVNRyEBY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 616, "top": 355, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeaz2cVNRzPfU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz2cVNR2FS8=", "_parent": {"$ref": "AAAAAAFeaz2cVNRzPfU="}, "model": {"$ref": "AAAAAAFeaz2cVNRyEBY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 582, "top": 356, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz2cVNRzPfU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeaz2cVNR3MUI=", "_parent": {"$ref": "AAAAAAFeaz2cVNRzPfU="}, "model": {"$ref": "AAAAAAFeaz2cVNRyEBY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 549, "top": 372, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeazov5dN9wkg="}, "tail": {"$ref": "AAAAAAFeazov5dN9wkg="}, "lineStyle": 0, "points": "562:352;592:352;592:372;562:372", "nameLabel": {"$ref": "AAAAAAFeaz2cVNR0cVE="}, "stereotypeLabel": {"$ref": "AAAAAAFeaz2cVNR1d/c="}, "propertyLabel": {"$ref": "AAAAAAFeaz2cVNR2FS8="}, "activation": {"$ref": "AAAAAAFeaz2cVNR3MUI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeaz3DzNSJm90=", "_parent": {"$ref": "AAAAAAFeazNaMNMIdls="}, "model": {"$ref": "AAAAAAFeaz3DzNSILx8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeaz3DzNSKZuA=", "_parent": {"$ref": "AAAAAAFeaz3DzNSJm90="}, "model": {"$ref": "AAAAAAFeaz3DzNSILx8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 278, "top": 405, "width": 88.15625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz3DzNSJm90="}, "edgePosition": 1, "underline": false, "text": "9 : Show report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz3DzNSLRlU=", "_parent": {"$ref": "AAAAAAFeaz3DzNSJm90="}, "model": {"$ref": "AAAAAAFeaz3DzNSILx8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 322, "top": 420, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeaz3DzNSJm90="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeaz3DzNSMVeQ=", "_parent": {"$ref": "AAAAAAFeaz3DzNSJm90="}, "model": {"$ref": "AAAAAAFeaz3DzNSILx8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 323, "top": 385, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeaz3DzNSJm90="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeaz3DzNSN2wA=", "_parent": {"$ref": "AAAAAAFeaz3DzNSJm90="}, "model": {"$ref": "AAAAAAFeaz3DzNSILx8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 77, "top": 401, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeazmMEtM96pM="}, "tail": {"$ref": "AAAAAAFeazov5dN9wkg="}, "lineStyle": 0, "points": "556:401;90:401", "nameLabel": {"$ref": "AAAAAAFeaz3DzNSKZuA="}, "stereotypeLabel": {"$ref": "AAAAAAFeaz3DzNSLRlU="}, "propertyLabel": {"$ref": "AAAAAAFeaz3DzNSMVeQ="}, "activation": {"$ref": "AAAAAAFeaz3DzNSN2wA="}, "showProperty": true, "showType": true}], "showSequenceNumber": true, "showSignature": true, "showActivation": true}], "visibility": "public", "isReentrant": true, "messages": [{"_type": "UMLMessage", "_id": "AAAAAAFeazs8wtPX9r4=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Show Report With Params", "source": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "target": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeazuxWdPugjQ=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Set search criteria values", "source": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "target": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeazyBv9QEECM=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Save search", "source": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "target": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "visibility": "public", "messageSort": "asynchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeazy5KtQaKl8=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Save criterias linked to report", "source": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "target": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeaz0c7NQw1WU=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Done", "source": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "target": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeaz1AFdRGrNs=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Press OK", "source": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "target": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "visibility": "public", "messageSort": "asynchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeaz1oeNRcEF0=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Generate report", "source": {"$ref": "AAAAAAFeaznFMdNWtpE="}, "target": {"$ref": "AAAAAAFeazov5NN2ICM="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeaz2cVNRyEBY=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Generate report", "source": {"$ref": "AAAAAAFeazov5NN2ICM="}, "target": {"$ref": "AAAAAAFeazov5NN2ICM="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeaz3DzNSILx8=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "Show report", "source": {"$ref": "AAAAAAFeazov5NN2ICM="}, "target": {"$ref": "AAAAAAFeazmMEdM2I9A="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}], "participants": [{"_type": "UMLLifeline", "_id": "AAAAAAFeazmMEdM2I9A=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "User", "visibility": "public", "represent": {"$ref": "AAAAAAFeazmMEdM1mvY="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeaznFMdNWtpE=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "CCIS", "visibility": "public", "represent": {"$ref": "AAAAAAFeaznFMdNVBy4="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeazov5NN2ICM=", "_parent": {"$ref": "AAAAAAFeazNaMNMHEzo="}, "name": "<PERSON>", "visibility": "public", "represent": {"$ref": "AAAAAAFeazov5NN1yjc="}, "isMultiInstance": false}]}], "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAFeayoSFtCVzRk=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role1", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeaypL9dC1ZGE=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role2", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeayq19NDXYsY=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role3", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeazmMEdM1mvY=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role4", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeaznFMdNVBy4=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role5", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeazov5NN1yjc=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role6", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFea0al+NSgx70=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role7", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFea0baLtTAuGw=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role8", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFea0cHzNTgc8k=", "_parent": {"$ref": "AAAAAAFeaymkStCFWGQ="}, "name": "Role9", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}], "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}, {"_type": "UMLCollaboration", "_id": "AAAAAAFeet93V3LF3+I=", "_parent": {"$ref": "AAAAAAFF+h6SjaM2Hec="}, "name": "Collaboration2", "ownedElements": [{"_type": "UMLInteraction", "_id": "AAAAAAFeet93V3LGg6g=", "_parent": {"$ref": "AAAAAAFeet93V3LF3+I="}, "name": "Interaction1", "ownedElements": [{"_type": "UMLSequenceDiagram", "_id": "AAAAAAFeet93V3LHuRQ=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "scheduleExecute", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "UMLFrameView", "_id": "AAAAAAFeet93V3LI0Uk=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeet93V3LJv0M=", "_parent": {"$ref": "AAAAAAFeet93V3LI0Uk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 70.97900390625, "top": 5, "width": 100.0107421875, "height": 13, "autoResize": false, "underline": false, "text": "scheduleExecute", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeet93V3LKR70=", "_parent": {"$ref": "AAAAAAFeet93V3LI0Uk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 5, "top": 5, "width": 60.97900390625, "height": 13, "autoResize": false, "underline": false, "text": "interaction", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 721, "height": 409, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeet93V3LJv0M="}, "frameTypeLabel": {"$ref": "AAAAAAFeet93V3LKR70="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeeuFZkXLXV5M=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFeeuFZkXLWxLo="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeeuFZkXLYKo0=", "_parent": {"$ref": "AAAAAAFeeuFZkXLXV5M="}, "model": {"$ref": "AAAAAAFeeuFZkXLWxLo="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeeuFZkXLZUtg=", "_parent": {"$ref": "AAAAAAFeeuFZkXLYKo0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuFZkXLaV7U=", "_parent": {"$ref": "AAAAAAFeeuFZkXLYKo0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 93, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "User", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuFZkXLbha4=", "_parent": {"$ref": "AAAAAAFeeuFZkXLYKo0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuFZkXLcAWI=", "_parent": {"$ref": "AAAAAAFeeuFZkXLYKo0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 88, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeeuFZkXLZUtg="}, "nameLabel": {"$ref": "AAAAAAFeeuFZkXLaV7U="}, "namespaceLabel": {"$ref": "AAAAAAFeeuFZkXLbha4="}, "propertyLabel": {"$ref": "AAAAAAFeeuFZkXLcAWI="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeeuFZkXLd2Wg=", "_parent": {"$ref": "AAAAAAFeeuFZkXLXV5M="}, "model": {"$ref": "AAAAAAFeeuFZkXLWxLo="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 124, "top": 80, "width": 1, "height": 281, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 88, "top": 40, "width": 72.736328125, "height": 321, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeeuFZkXLYKo0="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeeuFZkXLd2Wg="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeeuLNT3L4rHQ=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFeeuLNT3L3qoU="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeeuLNT3L5s38=", "_parent": {"$ref": "AAAAAAFeeuLNT3L4rHQ="}, "model": {"$ref": "AAAAAAFeeuLNT3L3qoU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeeuLNT3L6bDQ=", "_parent": {"$ref": "AAAAAAFeeuLNT3L5s38="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 80, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuLNT3L7ncA=", "_parent": {"$ref": "AAAAAAFeeuLNT3L5s38="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 309, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "CCIS", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuLNT3L8I+s=", "_parent": {"$ref": "AAAAAAFeeuLNT3L5s38="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 80, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuLNT3L99Qc=", "_parent": {"$ref": "AAAAAAFeeuLNT3L5s38="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 80, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 304, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeeuLNT3L6bDQ="}, "nameLabel": {"$ref": "AAAAAAFeeuLNT3L7ncA="}, "namespaceLabel": {"$ref": "AAAAAAFeeuLNT3L8I+s="}, "propertyLabel": {"$ref": "AAAAAAFeeuLNT3L99Qc="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeeuLNT3L+0qo=", "_parent": {"$ref": "AAAAAAFeeuLNT3L4rHQ="}, "model": {"$ref": "AAAAAAFeeuLNT3L3qoU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 340, "top": 80, "width": 1, "height": 149, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 304, "top": 40, "width": 72.736328125, "height": 189, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeeuLNT3L5s38="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeeuLNT3L+0qo="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeeuLsGHMYEV0=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFeeuLsGHMXj7I="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeeuLsGHMZh04=", "_parent": {"$ref": "AAAAAAFeeuLsGHMYEV0="}, "model": {"$ref": "AAAAAAFeeuLsGHMXj7I="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeeuLsGHMa62U=", "_parent": {"$ref": "AAAAAAFeeuLsGHMZh04="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 208, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuLsGHMbWf4=", "_parent": {"$ref": "AAAAAAFeeuLsGHMZh04="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 549, "top": 47, "width": 92.39892578125, "height": 13, "autoResize": false, "underline": false, "text": "JasperServer", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuLsGHMczOA=", "_parent": {"$ref": "AAAAAAFeeuLsGHMZh04="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 208, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeeuLsGHMd2fc=", "_parent": {"$ref": "AAAAAAFeeuLsGHMZh04="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 208, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 40, "width": 102.39892578125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeeuLsGHMa62U="}, "nameLabel": {"$ref": "AAAAAAFeeuLsGHMbWf4="}, "namespaceLabel": {"$ref": "AAAAAAFeeuLsGHMczOA="}, "propertyLabel": {"$ref": "AAAAAAFeeuLsGHMd2fc="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeeuLsGHMeULY=", "_parent": {"$ref": "AAAAAAFeeuLsGHMYEV0="}, "model": {"$ref": "AAAAAAFeeuLsGHMXj7I="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 595, "top": 80, "width": 1, "height": 273, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 40, "width": 102.39892578125, "height": 313, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeeuLsGHMZh04="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeeuLsGHMeULY="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFehdl9B0PyBRI=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFehdl9B0PxetI="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehdl9B0PzDBE=", "_parent": {"$ref": "AAAAAAFehdl9B0PyBRI="}, "model": {"$ref": "AAAAAAFehdl9B0PxetI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 173, "top": 81, "width": 111.287109375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehdl9B0PyBRI="}, "edgePosition": 1, "underline": false, "text": "1 : Choose a report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehdl9CEP0Sts=", "_parent": {"$ref": "AAAAAAFehdl9B0PyBRI="}, "model": {"$ref": "AAAAAAFehdl9B0PxetI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 228, "top": 66, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFehdl9B0PyBRI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehdl9CEP1HVg=", "_parent": {"$ref": "AAAAAAFehdl9B0PyBRI="}, "model": {"$ref": "AAAAAAFehdl9B0PxetI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 228, "top": 101, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehdl9B0PyBRI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFehdl9CEP2mzc=", "_parent": {"$ref": "AAAAAAFehdl9B0PyBRI="}, "model": {"$ref": "AAAAAAFehdl9B0PxetI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 333, "top": 97, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeuLNT3L+0qo="}, "tail": {"$ref": "AAAAAAFeeuFZkXLd2Wg="}, "lineStyle": 0, "points": "124:97;333:97", "nameLabel": {"$ref": "AAAAAAFehdl9B0PzDBE="}, "stereotypeLabel": {"$ref": "AAAAAAFehdl9CEP0Sts="}, "propertyLabel": {"$ref": "AAAAAAFehdl9CEP1HVg="}, "activation": {"$ref": "AAAAAAFehdl9CEP2mzc="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFehdm2zUQIeTA=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFehdm2zEQHPZ8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehdm2zUQJTF4=", "_parent": {"$ref": "AAAAAAFehdm2zUQIeTA="}, "model": {"$ref": "AAAAAAFehdm2zEQHPZ8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 163, "top": 124, "width": 135.1162109375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehdm2zUQIeTA="}, "edgePosition": 1, "underline": false, "text": "2 : Show report params", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehdm2zUQKI2g=", "_parent": {"$ref": "AAAAAAFehdm2zUQIeTA="}, "model": {"$ref": "AAAAAAFehdm2zEQHPZ8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 230, "top": 139, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFehdm2zUQIeTA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehdm2zUQLV7M=", "_parent": {"$ref": "AAAAAAFehdm2zUQIeTA="}, "model": {"$ref": "AAAAAAFehdm2zEQHPZ8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 231, "top": 104, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehdm2zUQIeTA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFehdm2zUQM6bI=", "_parent": {"$ref": "AAAAAAFehdm2zUQIeTA="}, "model": {"$ref": "AAAAAAFehdm2zEQHPZ8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 117, "top": 120, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeuFZkXLd2Wg="}, "tail": {"$ref": "AAAAAAFeeuLNT3L+0qo="}, "lineStyle": 0, "points": "333:120;130:120", "nameLabel": {"$ref": "AAAAAAFehdm2zUQJTF4="}, "stereotypeLabel": {"$ref": "AAAAAAFehdm2zUQKI2g="}, "propertyLabel": {"$ref": "AAAAAAFehdm2zUQLV7M="}, "activation": {"$ref": "AAAAAAFehdm2zUQM6bI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFehdnnzEQenNI=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFehdnnzEQdIQs="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehdnnzUQfssg=", "_parent": {"$ref": "AAAAAAFehdnnzEQenNI="}, "model": {"$ref": "AAAAAAFehdnnzEQdIQs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 140, "top": 146, "width": 176.2998046875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehdnnzEQenNI="}, "edgePosition": 1, "underline": false, "text": "3 : Confirm scheduling params", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehdnnzUQgYYE=", "_parent": {"$ref": "AAAAAAFehdnnzEQenNI="}, "model": {"$ref": "AAAAAAFehdnnzEQdIQs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 228, "top": 131, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFehdnnzEQenNI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehdnnzUQh6ro=", "_parent": {"$ref": "AAAAAAFehdnnzEQenNI="}, "model": {"$ref": "AAAAAAFehdnnzEQdIQs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 228, "top": 166, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehdnnzEQenNI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFehdnnzUQioak=", "_parent": {"$ref": "AAAAAAFehdnnzEQenNI="}, "model": {"$ref": "AAAAAAFehdnnzEQdIQs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 333, "top": 162, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeuLNT3L+0qo="}, "tail": {"$ref": "AAAAAAFeeuFZkXLd2Wg="}, "lineStyle": 0, "points": "124:162;333:162", "nameLabel": {"$ref": "AAAAAAFehdnnzUQfssg="}, "stereotypeLabel": {"$ref": "AAAAAAFehdnnzUQgYYE="}, "propertyLabel": {"$ref": "AAAAAAFehdnnzUQh6ro="}, "activation": {"$ref": "AAAAAAFehdnnzUQioak="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFehd5rDETPWH8=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFehd5rC0TOBfw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehd5rDETQwso=", "_parent": {"$ref": "AAAAAAFehd5rDETPWH8="}, "model": {"$ref": "AAAAAAFehd5rC0TOBfw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 374, "top": 170, "width": 187.71923828125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehd5rDETPWH8="}, "edgePosition": 1, "underline": false, "text": "4 : Init report \"job\" for scheduling", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehd5rDETR7TY=", "_parent": {"$ref": "AAAAAAFehd5rDETPWH8="}, "model": {"$ref": "AAAAAAFehd5rC0TOBfw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 467, "top": 155, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFehd5rDETPWH8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFehd5rDETSEcU=", "_parent": {"$ref": "AAAAAAFehd5rDETPWH8="}, "model": {"$ref": "AAAAAAFehd5rC0TOBfw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 467, "top": 190, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFehd5rDETPWH8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFehd5rDETToMk=", "_parent": {"$ref": "AAAAAAFehd5rDETPWH8="}, "model": {"$ref": "AAAAAAFehd5rC0TOBfw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 588, "top": 186, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeuLsGHMeULY="}, "tail": {"$ref": "AAAAAAFeeuLNT3L+0qo="}, "lineStyle": 0, "points": "346:186;588:186", "nameLabel": {"$ref": "AAAAAAFehd5rDETQwso="}, "stereotypeLabel": {"$ref": "AAAAAAFehd5rDETR7TY="}, "propertyLabel": {"$ref": "AAAAAAFehd5rDETSEcU="}, "activation": {"$ref": "AAAAAAFehd5rDETToMk="}, "showProperty": true, "showType": true}, {"_type": "UMLCombinedFragmentView", "_id": "AAAAAAFeheB4U0UaNnQ=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFeheB4U0UYhrk="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeheB4VEUb9e8=", "_parent": {"$ref": "AAAAAAFeheB4U0UaNnQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 119.9599609375, "top": 245, "width": 109.392578125, "height": 13, "autoResize": false, "underline": false, "text": "for each job trigger", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeheB4VEUch70=", "_parent": {"$ref": "AAAAAAFeheB4U0UaNnQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 93, "top": 245, "width": 21.9599609375, "height": 13, "autoResize": false, "underline": false, "text": "seq", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLInteractionOperandCompartmentView", "_id": "AAAAAAFeheB4VEUdHi8=", "_parent": {"$ref": "AAAAAAFeheB4U0UaNnQ="}, "model": {"$ref": "AAAAAAFeheB4U0UYhrk="}, "subViews": [{"_type": "UMLInteractionOperandView", "_id": "AAAAAAFeheB4f0Up1Js=", "_parent": {"$ref": "AAAAAAFeheB4VEUdHi8="}, "model": {"$ref": "AAAAAAFeheB4U0UZj2Q="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeheB4f0Uqzj0=", "_parent": {"$ref": "AAAAAAFeheB4f0Up1Js="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 108, "top": 278, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 88, "top": 263, "width": 561, "height": 43, "autoResize": false, "guardLabel": {"$ref": "AAAAAAFeheB4f0Uqzj0="}}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 88, "top": 263, "width": 561, "height": 43, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 88, "top": 240, "width": 561, "height": 112, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeheB4VEUb9e8="}, "frameTypeLabel": {"$ref": "AAAAAAFeheB4VEUch70="}, "operandCompartment": {"$ref": "AAAAAAFeheB4VEUdHi8="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeheG8ZUVJnKc=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFeheG8ZUVIi2s="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeheG8ZUVK0yY=", "_parent": {"$ref": "AAAAAAFeheG8ZUVJnKc="}, "model": {"$ref": "AAAAAAFeheG8ZUVIi2s="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 423, "top": 266, "width": 166.18798828125, "height": 13, "autoResize": false, "alpha": -1.5792004910486586, "distance": 119.00420160649792, "hostEdge": {"$ref": "AAAAAAFeheG8ZUVJnKc="}, "edgePosition": 1, "underline": false, "text": "5 : Generate & Extract report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeheG8ZUVLtxM=", "_parent": {"$ref": "AAAAAAFeheG8ZUVJnKc="}, "model": {"$ref": "AAAAAAFeheG8ZUVIi2s="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 649, "top": 267, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeheG8ZUVJnKc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeheG8ZUVM3hI=", "_parent": {"$ref": "AAAAAAFeheG8ZUVJnKc="}, "model": {"$ref": "AAAAAAFeheG8ZUVIi2s="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 615, "top": 268, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeheG8ZUVJnKc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeheG8ZUVNVd0=", "_parent": {"$ref": "AAAAAAFeheG8ZUVJnKc="}, "model": {"$ref": "AAAAAAFeheG8ZUVIi2s="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 588, "top": 284, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeuLsGHMeULY="}, "tail": {"$ref": "AAAAAAFeeuLsGHMeULY="}, "lineStyle": 0, "points": "595:264;625:264;625:284;601:284", "nameLabel": {"$ref": "AAAAAAFeheG8ZUVK0yY="}, "stereotypeLabel": {"$ref": "AAAAAAFeheG8ZUVLtxM="}, "propertyLabel": {"$ref": "AAAAAAFeheG8ZUVM3hI="}, "activation": {"$ref": "AAAAAAFeheG8ZUVNVd0="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeheE4a0Uyq8I=", "_parent": {"$ref": "AAAAAAFeet93V3LHuRQ="}, "model": {"$ref": "AAAAAAFeheE4a0UxKus="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeheE4a0Uz7nk=", "_parent": {"$ref": "AAAAAAFeheE4a0Uyq8I="}, "model": {"$ref": "AAAAAAFeheE4a0UxKus="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 258, "top": 294, "width": 195.8251953125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeheE4a0Uyq8I="}, "edgePosition": 1, "underline": false, "text": "6 : Mail scheduled report to owner", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeheE4bEU0Grw=", "_parent": {"$ref": "AAAAAAFeheE4a0Uyq8I="}, "model": {"$ref": "AAAAAAFeheE4a0UxKus="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 355, "top": 309, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeheE4a0Uyq8I="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeheE4bEU1h7o=", "_parent": {"$ref": "AAAAAAFeheE4a0Uyq8I="}, "model": {"$ref": "AAAAAAFeheE4a0UxKus="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 356, "top": 274, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeheE4a0Uyq8I="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeheE4bEU2Cu4=", "_parent": {"$ref": "AAAAAAFeheE4a0Uyq8I="}, "model": {"$ref": "AAAAAAFeheE4a0UxKus="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 124, "top": 290, "width": 14, "height": 25, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeuFZkXLd2Wg="}, "tail": {"$ref": "AAAAAAFeeuLsGHMeULY="}, "lineStyle": 0, "points": "588:290;124:290", "nameLabel": {"$ref": "AAAAAAFeheE4a0Uz7nk="}, "stereotypeLabel": {"$ref": "AAAAAAFeheE4bEU0Grw="}, "propertyLabel": {"$ref": "AAAAAAFeheE4bEU1h7o="}, "activation": {"$ref": "AAAAAAFeheE4bEU2Cu4="}, "showProperty": true, "showType": true}], "showSequenceNumber": true, "showSignature": true, "showActivation": true}], "visibility": "public", "isReentrant": true, "messages": [{"_type": "UMLMessage", "_id": "AAAAAAFehdl9B0PxetI=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "Choose a report", "source": {"$ref": "AAAAAAFeeuFZkXLWxLo="}, "target": {"$ref": "AAAAAAFeeuLNT3L3qoU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFehdm2zEQHPZ8=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "Show report params", "source": {"$ref": "AAAAAAFeeuLNT3L3qoU="}, "target": {"$ref": "AAAAAAFeeuFZkXLWxLo="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFehdnnzEQdIQs=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "Confirm scheduling params", "source": {"$ref": "AAAAAAFeeuFZkXLWxLo="}, "target": {"$ref": "AAAAAAFeeuLNT3L3qoU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFehd5rC0TOBfw=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "Init report \"job\" for scheduling", "source": {"$ref": "AAAAAAFeeuLNT3L3qoU="}, "target": {"$ref": "AAAAAAFeeuLsGHMXj7I="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeheG8ZUVIi2s=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "Generate & Extract report", "source": {"$ref": "AAAAAAFeeuLsGHMXj7I="}, "target": {"$ref": "AAAAAAFeeuLsGHMXj7I="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeheE4a0UxKus=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "Mail scheduled report to owner", "source": {"$ref": "AAAAAAFeeuLsGHMXj7I="}, "target": {"$ref": "AAAAAAFeeuFZkXLWxLo="}, "visibility": "public", "messageSort": "reply", "isConcurrentIteration": false}], "participants": [{"_type": "UMLLifeline", "_id": "AAAAAAFeeuFZkXLWxLo=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "User", "visibility": "public", "represent": {"$ref": "AAAAAAFeeuFZkXLVxZ4="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeeuLNT3L3qoU=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "CCIS", "visibility": "public", "represent": {"$ref": "AAAAAAFeeuLNT3L2oZw="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeeuLsGHMXj7I=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "JasperServer", "visibility": "public", "represent": {"$ref": "AAAAAAFeeuLsGHMW6NU="}, "isMultiInstance": false}], "fragments": [{"_type": "UMLCombinedFragment", "_id": "AAAAAAFeheB4U0UYhrk=", "_parent": {"$ref": "AAAAAAFeet93V3LGg6g="}, "name": "for each job trigger", "visibility": "public", "isReentrant": true, "interactionOperator": "seq", "operands": [{"_type": "UMLInteractionOperand", "_id": "AAAAAAFeheB4U0UZj2Q=", "_parent": {"$ref": "AAAAAAFeheB4U0UYhrk="}, "name": "Operand1", "visibility": "public", "isReentrant": true}]}]}, {"_type": "FCFlowchart", "_id": "AAAAAAFeewR9rXUsmlc=", "_parent": {"$ref": "AAAAAAFeet93V3LF3+I="}, "name": "Flowchart", "ownedElements": [{"_type": "FCFlowchartDiagram", "_id": "AAAAAAFeewR9rXUtrtY=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "reportFlow", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "FCTerminatorView", "_id": "AAAAAAFeewoMaXU5dag=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeewoMaXU3CCE="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeewoManU6UN8=", "_parent": {"$ref": "AAAAAAFeewoMaXU5dag="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 498, "top": 66, "width": 70.078125, "height": 13, "autoResize": false, "underline": false, "text": "START", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 488, "top": 56, "width": 90.078125, "height": 33, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeewoManU6UN8="}, "wordWrap": true}, {"_type": "FCTerminatorView", "_id": "AAAAAAFeewpkF3VFB5s=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeewpkF3VD3Xg="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeewpkF3VG+2A=", "_parent": {"$ref": "AAAAAAFeewpkF3VFB5s="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 522, "top": 1026, "width": 70.078125, "height": 13, "autoResize": false, "underline": false, "text": "END", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 512, "top": 1016, "width": 90.078125, "height": 33, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeewpkF3VG+2A="}, "wordWrap": true}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFeewyILnV7AhI=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeewyILnV5nEY="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeewyILnV8Bxc=", "_parent": {"$ref": "AAAAAAFeewyILnV7AhI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 479, "top": 130, "width": 112.06982421875, "height": 26, "autoResize": false, "underline": false, "text": "Show all available reports", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 464, "top": 120, "width": 137.06982421875, "height": 46, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeewyILnV8Bxc="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeewzDjXWQaNs=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeewzDjXWOAVw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeewzDjXWRe4c=", "_parent": {"$ref": "AAAAAAFeewzDjXWQaNs="}, "model": {"$ref": "AAAAAAFeewzDjXWOAVw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 546, "top": 97, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeewzDjXWQaNs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeewyILnV7AhI="}, "tail": {"$ref": "AAAAAAFeewoMaXU5dag="}, "lineStyle": 2, "points": "532:88;532:120", "nameLabel": {"$ref": "AAAAAAFeewzDjXWRe4c="}}, {"_type": "FCProcessView", "_id": "AAAAAAFeewzxvnWZ9XE=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeewzxvXWXj6k="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeewzxvnWaekw=", "_parent": {"$ref": "AAAAAAFeewzxvnWZ9XE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 762, "top": 274, "width": 85, "height": 39, "autoResize": false, "underline": false, "text": "User Selects a report for viewing", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 752, "top": 264, "width": 105, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeewzxvnWaekw="}, "wordWrap": true}, {"_type": "FCOrView", "_id": "AAAAAAFeew25xnWnvdE=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeew25xnWlUw8="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeew25xnWoHCY=", "_parent": {"$ref": "AAAAAAFeew25xnWnvdE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 530, "top": 218, "width": 10, "height": 0, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 520, "top": 208, "width": 30, "height": 30, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeew25xnWoHCY="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeew4F9nWxxeo=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeew4F9nWvpD4="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeew4F9nWyC6w=", "_parent": {"$ref": "AAAAAAFeew4F9nWxxeo="}, "model": {"$ref": "AAAAAAFeew4F9nWvpD4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 547, "top": 179, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeew4F9nWxxeo="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeew25xnWnvdE="}, "tail": {"$ref": "AAAAAAFeewyILnV7AhI="}, "lineStyle": 2, "points": "533:165;533:208", "nameLabel": {"$ref": "AAAAAAFeew4F9nWyC6w="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeew46L3W9rJ4=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeew46L3W7ftc="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeew46L3W+fDE=", "_parent": {"$ref": "AAAAAAFeew46L3W9rJ4="}, "model": {"$ref": "AAAAAAFeew46L3W7ftc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 822, "top": 217, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeew46L3W9rJ4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeewzxvnWZ9XE="}, "tail": {"$ref": "AAAAAAFeew25xnWnvdE="}, "lineStyle": 2, "points": "549:224;808:224;808:264", "nameLabel": {"$ref": "AAAAAAFeew46L3W+fDE="}}, {"_type": "FCProcessView", "_id": "AAAAAAFeew7OJXXPw3U=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeew7OJXXN1ts="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeew7OJXXQjoI=", "_parent": {"$ref": "AAAAAAFeew7OJXXPw3U="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 266, "top": 274, "width": 101, "height": 39, "autoResize": false, "underline": false, "text": "User Selects a report for scheduling", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 256, "top": 264, "width": 121, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeew7OJXXQjoI="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeew8zNXXdnTc=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeew8zNXXbsqg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeew8zNXXe6RU=", "_parent": {"$ref": "AAAAAAFeew8zNXXdnTc="}, "model": {"$ref": "AAAAAAFeew8zNXXbsqg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 334, "top": 217, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeew8zNXXdnTc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeew7OJXXPw3U="}, "tail": {"$ref": "AAAAAAFeew25xnWnvdE="}, "lineStyle": 2, "points": "520:224;320:224;320:264", "nameLabel": {"$ref": "AAAAAAFeew8zNXXe6RU="}}, {"_type": "FCProcessView", "_id": "AAAAAAFeexAxBXXsY44=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexAxBXXqH2o="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexAxBnXtOHE=", "_parent": {"$ref": "AAAAAAFeexAxBXXsY44="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 698, "top": 530, "width": 54.18994140625, "height": 65, "autoResize": false, "underline": false, "text": "User uses report filter preset", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 688, "top": 520, "width": 74.18994140625, "height": 85, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexAxBnXtOHE="}, "wordWrap": true}, {"_type": "FCOrView", "_id": "AAAAAAFeexE0HXYLbTw=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexE0HXYJGqI="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexE0HXYME8Y=", "_parent": {"$ref": "AAAAAAFeexE0HXYLbTw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 802, "top": 498, "width": 10, "height": 0, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 792, "top": 488, "width": 30, "height": 30, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexE0HXYME8Y="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeexGQjXYogiA=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexGQjXYmLgA="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexGQjXYpX5g=", "_parent": {"$ref": "AAAAAAFeexGQjXYogiA="}, "model": {"$ref": "AAAAAAFeexGQjXYmLgA="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 742, "top": 489, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeexGQjXYogiA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexAxBXXsY44="}, "tail": {"$ref": "AAAAAAFeexE0HXYLbTw="}, "lineStyle": 2, "points": "792:496;728:496;728:520", "nameLabel": {"$ref": "AAAAAAFeexGQjXYpX5g="}}, {"_type": "FCPreparationView", "_id": "AAAAAAFeexLWVXZFqZM=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexLWVXZDA0E="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexLWVXZGGaU=", "_parent": {"$ref": "AAAAAAFeexLWVXZFqZM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 785, "top": 354, "width": 44.43896484375, "height": 78, "autoResize": false, "underline": false, "text": "Fetch user and report specific parameter presets", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 760, "top": 344, "width": 94.43896484375, "height": 111, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexLWVXZGGaU="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeexOJbHZScXw=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexOJbHZQRu8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexOJbHZTdGM=", "_parent": {"$ref": "AAAAAAFeexOJbHZScXw="}, "model": {"$ref": "AAAAAAFeexOJbHZQRu8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 819, "top": 326, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeexOJbHZScXw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexLWVXZFqZM="}, "tail": {"$ref": "AAAAAAFeewzxvnWZ9XE="}, "lineStyle": 2, "points": "805:322;805:344", "nameLabel": {"$ref": "AAAAAAFeexOJbHZTdGM="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeexOzG3Zb3sQ=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexOzG3ZZa0M="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexOzG3Zc3XU=", "_parent": {"$ref": "AAAAAAFeexOzG3Zb3sQ="}, "model": {"$ref": "AAAAAAFeexOzG3ZZa0M="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 820, "top": 464, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeexOzG3Zb3sQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexE0HXYLbTw="}, "tail": {"$ref": "AAAAAAFeexLWVXZFqZM="}, "lineStyle": 2, "points": "806:454;806:488", "nameLabel": {"$ref": "AAAAAAFeexOzG3Zc3XU="}}, {"_type": "FCDecisionView", "_id": "AAAAAAFeexVxtHZ7xuI=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexVxs3Z5s6A="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexVxtHZ8LZQ=", "_parent": {"$ref": "AAAAAAFeexVxtHZ7xuI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 860.542236328125, "top": 643.5, "width": 57.08447265625, "height": 39, "autoResize": false, "underline": false, "text": "Save new filter choices?", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 832, "top": 624, "width": 114.1689453125, "height": 78, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexVxtHZ8LZQ="}, "wordWrap": true}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFeexao63aiHkQ=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexao63agqmo="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexao63ajXpI=", "_parent": {"$ref": "AAAAAAFeexao63aiHkQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1031, "top": 658, "width": 112.06982421875, "height": 13, "autoResize": false, "underline": false, "text": "Save Filters to DB", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1016, "top": 648, "width": 137.06982421875, "height": 33, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexao63ajXpI="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeexdE4nauslE=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexdE4nasDmI="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexdE4navPkA=", "_parent": {"$ref": "AAAAAAFeexdE4nauslE="}, "model": {"$ref": "AAAAAAFeexdE4nasDmI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 959, "top": 647, "width": 26.0126953125, "height": 13, "autoResize": false, "alpha": -3.985747260392962, "distance": 12.041594578792296, "hostEdge": {"$ref": "AAAAAAFeexdE4nauslE="}, "edgePosition": 1, "underline": false, "text": "YES", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexao63aiHkQ="}, "tail": {"$ref": "AAAAAAFeexVxtHZ7xuI="}, "lineStyle": 2, "points": "945:663;1016:663", "nameLabel": {"$ref": "AAAAAAFeexdE4navPkA="}}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFeexe7G3a6nlI=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexe7G3a4nFs="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexe7G3a73nk=", "_parent": {"$ref": "AAAAAAFeexe7G3a6nlI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 751, "top": 730, "width": 112.06982421875, "height": 39, "autoResize": false, "underline": false, "text": "Generate Report given the filters provided", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 736, "top": 720, "width": 137.06982421875, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexe7G3a73nk="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeexgdc3bEEQs=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexgdc3bCjDY="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexgdc3bFkS0=", "_parent": {"$ref": "AAAAAAFeexgdc3bEEQs="}, "model": {"$ref": "AAAAAAFeexgdc3bCjDY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 896, "top": 712, "width": 19.5, "height": 13, "autoResize": false, "alpha": 4.133314441586954, "distance": 31.064449134018133, "hostEdge": {"$ref": "AAAAAAFeexgdc3bEEQs="}, "edgePosition": 1, "underline": false, "text": "NO", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexe7G3a6nlI="}, "tail": {"$ref": "AAAAAAFeexVxtHZ7xuI="}, "lineStyle": 2, "points": "888:701;888:744;872:744", "nameLabel": {"$ref": "AAAAAAFeexgdc3bFkS0="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeexihe3bTae4=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexihenbR/a0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexihe3bUNIk=", "_parent": {"$ref": "AAAAAAFeexihe3bTae4="}, "model": {"$ref": "AAAAAAFeexihenbR/a0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1079, "top": 753, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeexihe3bTae4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexe7G3a6nlI="}, "tail": {"$ref": "AAAAAAFeexao63aiHkQ="}, "lineStyle": 2, "points": "1080:680;1080:744;872:744", "nameLabel": {"$ref": "AAAAAAFeexihe3bUNIk="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeexjYonbeGBA=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexjYonbcaTY="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexjYonbfkAI=", "_parent": {"$ref": "AAAAAAFeexjYonbeGBA="}, "model": {"$ref": "AAAAAAFeexjYonbcaTY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 720, "top": 731, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeexjYonbeGBA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexe7G3a6nlI="}, "tail": {"$ref": "AAAAAAFeexAxBXXsY44="}, "lineStyle": 2, "points": "720:604;720:752;736:752", "nameLabel": {"$ref": "AAAAAAFeexjYonbfkAI="}}, {"_type": "FCDecisionView", "_id": "AAAAAAFeexlrA3brR4c=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexlrA3bppiE="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexlrA3bsldc=", "_parent": {"$ref": "AAAAAAFeexlrA3brR4c="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 278.25, "top": 376.5, "width": 76.5, "height": 65, "autoResize": false, "underline": false, "text": "Does user & report specific parameters exist?", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 240, "top": 344, "width": 153, "height": 130, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexlrA3bsldc="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeexoYKnb9GCo=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexoYKnb782w="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexoYK3b+3Uw=", "_parent": {"$ref": "AAAAAAFeexoYKnb9GCo="}, "model": {"$ref": "AAAAAAFeexoYKnb782w="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 330, "top": 326, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeexoYKnb9GCo="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexlrA3brR4c="}, "tail": {"$ref": "AAAAAAFeew7OJXXPw3U="}, "lineStyle": 2, "points": "316:322;316:344", "nameLabel": {"$ref": "AAAAAAFeexoYK3b+3Uw="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeexo6e3cG3Y0=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexo6e3cE9aA="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeexo6e3cHOfM=", "_parent": {"$ref": "AAAAAAFeexo6e3cG3Y0="}, "model": {"$ref": "AAAAAAFeexo6e3cE9aA="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 269, "width": 19.5, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeexo6e3cG3Y0="}, "edgePosition": 1, "underline": false, "text": "NO", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeewyILnV7AhI="}, "tail": {"$ref": "AAAAAAFeexlrA3brR4c="}, "lineStyle": 2, "points": "240:408;192:408;192:144;464:144", "nameLabel": {"$ref": "AAAAAAFeexo6e3cHOfM="}}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFeexsU43chJkQ=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeexsU4ncfOLo="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeexsU43ciacw=", "_parent": {"$ref": "AAAAAAFeexsU43chJkQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 263, "top": 602, "width": 112.06982421875, "height": 26, "autoResize": false, "underline": false, "text": "Show scheduling parameters", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 248, "top": 592, "width": 137.06982421875, "height": 46, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeexsU43ciacw="}, "wordWrap": true}, {"_type": "FCDataView", "_id": "AAAAAAFeex0Wk3c3MLw=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeex0Wk3c1WwU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeex0WlHc4pVY=", "_parent": {"$ref": "AAAAAAFeex0Wk3c3MLw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 280, "top": 674, "width": 73, "height": 52, "autoResize": false, "underline": false, "text": "User defines scheduling parameters", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 248, "top": 664, "width": 137, "height": 72, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeex0WlHc4pVY="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeex2PqXdF274=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeex2PqHdDARg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeex2PqXdGBzw=", "_parent": {"$ref": "AAAAAAFeex2PqXdF274="}, "model": {"$ref": "AAAAAAFeex2PqHdDARg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 329, "top": 643, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeex2PqXdF274="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeex0Wk3c3MLw="}, "tail": {"$ref": "AAAAAAFeexsU43chJkQ="}, "lineStyle": 2, "points": "315:637;315:664", "nameLabel": {"$ref": "AAAAAAFeex2PqXdGBzw="}}, {"_type": "FCDataView", "_id": "AAAAAAFeex38sXdT9Z8=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeex38sXdR2z8="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeex38sXdUZAc=", "_parent": {"$ref": "AAAAAAFeex38sXdT9Z8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 855, "top": 538, "width": 67, "height": 52, "autoResize": false, "underline": false, "text": "User Defines report filters", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 824, "top": 528, "width": 129, "height": 72, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeex38sXdUZAc="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeex5fUndtEE8=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeex5fUndrA9Q="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeex5fUnduMSQ=", "_parent": {"$ref": "AAAAAAFeex5fUndtEE8="}, "model": {"$ref": "AAAAAAFeex5fUndrA9Q="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 902, "top": 489, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeex5fUndtEE8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeex38sXdT9Z8="}, "tail": {"$ref": "AAAAAAFeexE0HXYLbTw="}, "lineStyle": 2, "points": "821:496;888:496;888:528", "nameLabel": {"$ref": "AAAAAAFeex5fUnduMSQ="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeex6t+nd5n/M=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeex6t+nd31qI="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeex6t+nd6HVg=", "_parent": {"$ref": "AAAAAAFeex6t+nd5n/M="}, "model": {"$ref": "AAAAAAFeex6t+nd31qI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 902, "top": 604, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeex6t+nd5n/M="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexVxtHZ7xuI="}, "tail": {"$ref": "AAAAAAFeex38sXdT9Z8="}, "lineStyle": 2, "points": "888:599;888:624", "nameLabel": {"$ref": "AAAAAAFeex6t+nd6HVg="}}, {"_type": "FCProcessView", "_id": "AAAAAAFeeyBSQHeGIk4=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyBSQHeEVe4="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeeyBSQXeH1oY=", "_parent": {"$ref": "AAAAAAFeeyBSQHeGIk4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 274, "top": 514, "width": 78.18994140625, "height": 39, "autoResize": false, "underline": false, "text": "User uses report filter preset", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 264, "top": 504, "width": 98.18994140625, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeeyBSQXeH1oY="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeeyD68HefHoU=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyD673edTcM="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeeyD68HegnHE=", "_parent": {"$ref": "AAAAAAFeeyD68HefHoU="}, "model": {"$ref": "AAAAAAFeeyD673edTcM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 319, "top": 481, "width": 26.0126953125, "height": 13, "autoResize": false, "alpha": 1.6262941762713996, "distance": 18.027756377319946, "hostEdge": {"$ref": "AAAAAAFeeyD68HefHoU="}, "edgePosition": 1, "underline": false, "text": "YES", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeyBSQHeGIk4="}, "tail": {"$ref": "AAAAAAFeexlrA3brR4c="}, "lineStyle": 2, "points": "314:473;314:504", "nameLabel": {"$ref": "AAAAAAFeeyD68HegnHE="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeeyEcQ3eqNNE=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyEcQ3eoBmo="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeeyEcQ3erP1k=", "_parent": {"$ref": "AAAAAAFeeyEcQ3eqNNE="}, "model": {"$ref": "AAAAAAFeeyEcQ3eoBmo="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 327, "top": 570, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeeyEcQ3eqNNE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexsU43chJkQ="}, "tail": {"$ref": "AAAAAAFeeyBSQHeGIk4="}, "lineStyle": 2, "points": "313:562;313:592", "nameLabel": {"$ref": "AAAAAAFeeyEcQ3erP1k="}}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFeeyJbmHezBcU=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyJbmHex3IU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeeyJbmHe08mg=", "_parent": {"$ref": "AAAAAAFeeyJbmHezBcU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 263, "top": 786, "width": 112.06982421875, "height": 52, "autoResize": false, "underline": false, "text": "Gather data for scheduling and generate well-formed XML", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 248, "top": 776, "width": 137.06982421875, "height": 72, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeeyJbmHe08mg="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeeyLTzXe/2Mo=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyLTzXe91z4="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeeyLTzXfAaDM=", "_parent": {"$ref": "AAAAAAFeeyLTzXe/2Mo="}, "model": {"$ref": "AAAAAAFeeyLTzXe91z4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 330, "top": 748, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeeyLTzXe/2Mo="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeyJbmHezBcU="}, "tail": {"$ref": "AAAAAAFeex0Wk3c3MLw="}, "lineStyle": 2, "points": "316:735;316:776", "nameLabel": {"$ref": "AAAAAAFeeyLTzXfAaDM="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeeyNXt3fT9gw=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyNXt3fRrB8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeeyNXt3fUlOc=", "_parent": {"$ref": "AAAAAAFeeyNXt3fT9gw="}, "model": {"$ref": "AAAAAAFeeyNXt3fRrB8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 668, "top": 961, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeeyNXt3fT9gw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeewpkF3VFB5s="}, "tail": {"$ref": "AAAAAAFeexe7G3a6nlI="}, "lineStyle": 2, "points": "787:778;787:952;552:952;552:1016", "nameLabel": {"$ref": "AAAAAAFeeyNXt3fUlOc="}}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFeeypbwnfw3zg=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeypbwnfuf50="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeeypbwnfxX7s=", "_parent": {"$ref": "AAAAAAFeeypbwnfw3zg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 263, "top": 882, "width": 112.06982421875, "height": 39, "autoResize": false, "underline": false, "text": "Call JasperReports REST service to init. scheduling", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 248, "top": 872, "width": 137.06982421875, "height": 72, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeeypbwnfxX7s="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFeeyto6XgAMiw=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyto6Xf+oWM="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeeyto6XgBZv4=", "_parent": {"$ref": "AAAAAAFeeyto6XgAMiw="}, "model": {"$ref": "AAAAAAFeeyto6Xf+oWM="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 330, "top": 852, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeeyto6XgAMiw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeeypbwnfw3zg="}, "tail": {"$ref": "AAAAAAFeeyJbmHezBcU="}, "lineStyle": 2, "points": "316:847;316:872", "nameLabel": {"$ref": "AAAAAAFeeyto6XgBZv4="}}, {"_type": "FCFlowView", "_id": "AAAAAAFeeyt2pngJ+Kc=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFeeyt2pngHsJw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeeyt2pngKpHI=", "_parent": {"$ref": "AAAAAAFeeyt2pngJ+Kc="}, "model": {"$ref": "AAAAAAFeeyt2pngHsJw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 436, "top": 955, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFeeyt2pngJ+Kc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeewpkF3VFB5s="}, "tail": {"$ref": "AAAAAAFeeypbwnfw3zg="}, "lineStyle": 2, "points": "320:943;320:976;552:976;552:1016", "nameLabel": {"$ref": "AAAAAAFeeyt2pngKpHI="}}, {"_type": "FCProcessView", "_id": "AAAAAAFehe0maUV0/bI=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehe0maUVyzEs="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFehe0maUV1kyQ=", "_parent": {"$ref": "AAAAAAFehe0maUV0/bI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 474, "top": 274, "width": 133, "height": 39, "autoResize": false, "underline": false, "text": "User selects to view all (his/her) scheduled reports", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 464, "top": 264, "width": 153, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFehe0maUV1kyQ="}, "wordWrap": true}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFehe4vUEWDgWQ=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehe4vUEWBtko="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFehe4vUEWE+d8=", "_parent": {"$ref": "AAAAAAFehe4vUEWDgWQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 495, "top": 354, "width": 112.06982421875, "height": 39, "autoResize": false, "underline": false, "text": "Show all user specific scheduled reports", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 480, "top": 344, "width": 137.06982421875, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFehe4vUEWE+d8="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFehe57IUWNeNE=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehe57IEWLNF8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehe57IUWO1Kk=", "_parent": {"$ref": "AAAAAAFehe57IUWNeNE="}, "model": {"$ref": "AAAAAAFehe57IEWLNF8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 553, "top": 243, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehe57IUWNeNE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFehe0maUV0/bI="}, "tail": {"$ref": "AAAAAAFeew25xnWnvdE="}, "lineStyle": 2, "points": "539:237;539:264", "nameLabel": {"$ref": "AAAAAAFehe57IUWO1Kk="}}, {"_type": "FCFlowView", "_id": "AAAAAAFehe6bMEWY+/k=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehe6bMEWWeCQ="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehe6bMEWZvYY=", "_parent": {"$ref": "AAAAAAFehe6bMEWY+/k="}, "model": {"$ref": "AAAAAAFehe6bMEWWeCQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 559, "top": 326, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehe6bMEWY+/k="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFehe4vUEWDgWQ="}, "tail": {"$ref": "AAAAAAFehe0maUV0/bI="}, "lineStyle": 2, "points": "545:322;545:344", "nameLabel": {"$ref": "AAAAAAFehe6bMEWZvYY="}}, {"_type": "FCProcessView", "_id": "AAAAAAFehe939EWjFG0=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehe939EWhOkA="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFehe939EWkN14=", "_parent": {"$ref": "AAAAAAFehe939EWjFG0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 498, "top": 442, "width": 99.26806640625, "height": 39, "autoResize": false, "underline": false, "text": "User selects a specific scheduled job", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 488, "top": 432, "width": 119.26806640625, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFehe939EWkN14="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFehe/nT0WvnqE=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehe/nT0WtByg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehe/nT0Ww/dA=", "_parent": {"$ref": "AAAAAAFehe/nT0WvnqE="}, "model": {"$ref": "AAAAAAFehe/nT0WtByg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 563, "top": 410, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehe/nT0WvnqE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFehe939EWjFG0="}, "tail": {"$ref": "AAAAAAFehe4vUEWDgWQ="}, "lineStyle": 2, "points": "549:402;549:432", "nameLabel": {"$ref": "AAAAAAFehe/nT0Ww/dA="}}, {"_type": "FCProcessView", "_id": "AAAAAAFehfCqJEXPYPU=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfCqJEXNjOQ="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFehfCqJEXQ4Wg=", "_parent": {"$ref": "AAAAAAFehfCqJEXPYPU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 426, "top": 642, "width": 88.8798828125, "height": 26, "autoResize": false, "underline": false, "text": "User edits a job definition", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 416, "top": 632, "width": 108.8798828125, "height": 46, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFehfCqJEXQ4Wg="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFehfIqh0Xtcm4=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfIqh0Xr4Ww="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehfIqh0XuClA=", "_parent": {"$ref": "AAAAAAFehfIqh0Xtcm4="}, "model": {"$ref": "AAAAAAFehfIqh0Xr4Ww="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 385, "top": 633, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehfIqh0Xtcm4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeexsU43chJkQ="}, "tail": {"$ref": "AAAAAAFehfCqJEXPYPU="}, "lineStyle": 2, "points": "416:664;400:664;400:616;384:616", "nameLabel": {"$ref": "AAAAAAFehfIqh0XuClA="}}, {"_type": "FCPredefinedProcessView", "_id": "AAAAAAFehfKwJEX4vYY=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfKwJEX2RNI="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFehfKwJEX5H7c=", "_parent": {"$ref": "AAAAAAFehfKwJEX4vYY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 495, "top": 522, "width": 112.06982421875, "height": 26, "autoResize": false, "underline": false, "text": "Show detailed job description", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 480, "top": 512, "width": 137.06982421875, "height": 46, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFehfKwJEX5H7c="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFehfMHLkYR2nU=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfMHLkYP2tk="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehfMHLkYSTCI=", "_parent": {"$ref": "AAAAAAFehfMHLkYR2nU="}, "model": {"$ref": "AAAAAAFehfMHLkYP2tk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 561, "top": 494, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehfMHLkYR2nU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFehfKwJEX4vYY="}, "tail": {"$ref": "AAAAAAFehe939EWjFG0="}, "lineStyle": 2, "points": "547:490;547:512", "nameLabel": {"$ref": "AAAAAAFehfMHLkYSTCI="}}, {"_type": "FCOrView", "_id": "AAAAAAFehfMw7UYbcVU=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfMw7UYZpFA="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFehfMw7UYcqKI=", "_parent": {"$ref": "AAAAAAFehfMw7UYbcVU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 546, "top": 586, "width": 10, "height": 0, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 536, "top": 576, "width": 30, "height": 30, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFehfMw7UYcqKI="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFehfNM/kYkUk0=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfNM/kYi0qw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehfNM/0YlssM=", "_parent": {"$ref": "AAAAAAFehfNM/kYkUk0="}, "model": {"$ref": "AAAAAAFehfNM/kYi0qw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 563, "top": 559, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehfNM/kYkUk0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFehfMw7UYbcVU="}, "tail": {"$ref": "AAAAAAFehfKwJEX4vYY="}, "lineStyle": 2, "points": "549:557;549:576", "nameLabel": {"$ref": "AAAAAAFehfNM/0YlssM="}}, {"_type": "FCFlowView", "_id": "AAAAAAFehfNcd0Yto2s=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfNcd0YrHf4="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehfNcd0Yum9U=", "_parent": {"$ref": "AAAAAAFehfNcd0Yto2s="}, "model": {"$ref": "AAAAAAFehfNcd0YrHf4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 486, "top": 585, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehfNcd0Yto2s="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFehfCqJEXPYPU="}, "tail": {"$ref": "AAAAAAFehfMw7UYbcVU="}, "lineStyle": 2, "points": "536:592;472:592;472:632", "nameLabel": {"$ref": "AAAAAAFehfNcd0Yum9U="}}, {"_type": "FCProcessView", "_id": "AAAAAAFehfTCJkZYLeg=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfTCJkZWJyc="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFehfTCJkZZ9Sg=", "_parent": {"$ref": "AAAAAAFehfTCJkZYLeg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 570, "top": 674, "width": 94.9072265625, "height": 39, "autoResize": false, "underline": false, "text": "User Pauses / Resumes a Report job", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": true}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 560, "top": 664, "width": 114.9072265625, "height": 59, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFehfTCJkZZ9Sg="}, "wordWrap": true}, {"_type": "FCFlowView", "_id": "AAAAAAFehfUx3kZmK3M=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfUx3kZkXcw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehfUx3kZnaS4=", "_parent": {"$ref": "AAAAAAFehfUx3kZmK3M="}, "model": {"$ref": "AAAAAAFehfUx3kZkXcw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 630, "top": 585, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehfUx3kZmK3M="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFehfTCJkZYLeg="}, "tail": {"$ref": "AAAAAAFehfMw7UYbcVU="}, "lineStyle": 2, "points": "565:592;616:592;616:664", "nameLabel": {"$ref": "AAAAAAFehfUx3kZnaS4="}}, {"_type": "FCFlowView", "_id": "AAAAAAFehfYDbkZxvJA=", "_parent": {"$ref": "AAAAAAFeewR9rXUtrtY="}, "model": {"$ref": "AAAAAAFehfYDbkZv9Fg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFehfYDb0ZyqYE=", "_parent": {"$ref": "AAAAAAFehfYDbkZxvJA="}, "model": {"$ref": "AAAAAAFehfYDbkZv9Fg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 579, "top": 849, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 15, "hostEdge": {"$ref": "AAAAAAFehfYDbkZxvJA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeewpkF3VFB5s="}, "tail": {"$ref": "AAAAAAFehfTCJkZYLeg="}, "lineStyle": 2, "points": "608:722;608:840;552:840;552:1016", "nameLabel": {"$ref": "AAAAAAFehfYDb0ZyqYE="}}]}, {"_type": "FCTerminator", "_id": "AAAAAAFeewoMaXU3CCE=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "START", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeewzDjXWOAVw=", "_parent": {"$ref": "AAAAAAFeewoMaXU3CCE="}, "source": {"$ref": "AAAAAAFeewoMaXU3CCE="}, "target": {"$ref": "AAAAAAFeewyILnV5nEY="}}]}, {"_type": "FCTerminator", "_id": "AAAAAAFeewpkF3VD3Xg=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "END"}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFeewyILnV5nEY=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Show all available reports", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeew4F9nWvpD4=", "_parent": {"$ref": "AAAAAAFeewyILnV5nEY="}, "source": {"$ref": "AAAAAAFeewyILnV5nEY="}, "target": {"$ref": "AAAAAAFeew25xnWlUw8="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFeewzxvXWXj6k=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User Selects a report for viewing", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexOJbHZQRu8=", "_parent": {"$ref": "AAAAAAFeewzxvXWXj6k="}, "source": {"$ref": "AAAAAAFeewzxvXWXj6k="}, "target": {"$ref": "AAAAAAFeexLWVXZDA0E="}}]}, {"_type": "FCOr", "_id": "AAAAAAFeew25xnWlUw8=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeew46L3W7ftc=", "_parent": {"$ref": "AAAAAAFeew25xnWlUw8="}, "source": {"$ref": "AAAAAAFeew25xnWlUw8="}, "target": {"$ref": "AAAAAAFeewzxvXWXj6k="}}, {"_type": "FCFlow", "_id": "AAAAAAFeew8zNXXbsqg=", "_parent": {"$ref": "AAAAAAFeew25xnWlUw8="}, "source": {"$ref": "AAAAAAFeew25xnWlUw8="}, "target": {"$ref": "AAAAAAFeew7OJXXN1ts="}}, {"_type": "FCFlow", "_id": "AAAAAAFehe57IEWLNF8=", "_parent": {"$ref": "AAAAAAFeew25xnWlUw8="}, "source": {"$ref": "AAAAAAFeew25xnWlUw8="}, "target": {"$ref": "AAAAAAFehe0maUVyzEs="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFeew7OJXXN1ts=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User Selects a report for scheduling", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexoYKnb782w=", "_parent": {"$ref": "AAAAAAFeew7OJXXN1ts="}, "source": {"$ref": "AAAAAAFeew7OJXXN1ts="}, "target": {"$ref": "AAAAAAFeexlrA3bppiE="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFeexAxBXXqH2o=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User uses report filter preset", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexjYonbcaTY=", "_parent": {"$ref": "AAAAAAFeexAxBXXqH2o="}, "source": {"$ref": "AAAAAAFeexAxBXXqH2o="}, "target": {"$ref": "AAAAAAFeexe7G3a4nFs="}}]}, {"_type": "FCOr", "_id": "AAAAAAFeexE0HXYJGqI=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexGQjXYmLgA=", "_parent": {"$ref": "AAAAAAFeexE0HXYJGqI="}, "source": {"$ref": "AAAAAAFeexE0HXYJGqI="}, "target": {"$ref": "AAAAAAFeexAxBXXqH2o="}}, {"_type": "FCFlow", "_id": "AAAAAAFeex5fUndrA9Q=", "_parent": {"$ref": "AAAAAAFeexE0HXYJGqI="}, "source": {"$ref": "AAAAAAFeexE0HXYJGqI="}, "target": {"$ref": "AAAAAAFeex38sXdR2z8="}}]}, {"_type": "FCPreparation", "_id": "AAAAAAFeexLWVXZDA0E=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Fetch user and report specific parameter presets", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexOzG3ZZa0M=", "_parent": {"$ref": "AAAAAAFeexLWVXZDA0E="}, "source": {"$ref": "AAAAAAFeexLWVXZDA0E="}, "target": {"$ref": "AAAAAAFeexE0HXYJGqI="}}]}, {"_type": "FCDecision", "_id": "AAAAAAFeexVxs3Z5s6A=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Save new filter choices?", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexdE4nasDmI=", "_parent": {"$ref": "AAAAAAFeexVxs3Z5s6A="}, "name": "YES", "source": {"$ref": "AAAAAAFeexVxs3Z5s6A="}, "target": {"$ref": "AAAAAAFeexao63agqmo="}}, {"_type": "FCFlow", "_id": "AAAAAAFeexgdc3bCjDY=", "_parent": {"$ref": "AAAAAAFeexVxs3Z5s6A="}, "name": "NO", "source": {"$ref": "AAAAAAFeexVxs3Z5s6A="}, "target": {"$ref": "AAAAAAFeexe7G3a4nFs="}}]}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFeexao63agqmo=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Save Filters to DB", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexihenbR/a0=", "_parent": {"$ref": "AAAAAAFeexao63agqmo="}, "source": {"$ref": "AAAAAAFeexao63agqmo="}, "target": {"$ref": "AAAAAAFeexe7G3a4nFs="}}]}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFeexe7G3a4nFs=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Generate Report given the filters provided", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeeyNXt3fRrB8=", "_parent": {"$ref": "AAAAAAFeexe7G3a4nFs="}, "source": {"$ref": "AAAAAAFeexe7G3a4nFs="}, "target": {"$ref": "AAAAAAFeewpkF3VD3Xg="}}]}, {"_type": "FCDecision", "_id": "AAAAAAFeexlrA3bppiE=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Does user & report specific parameters exist?", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeexo6e3cE9aA=", "_parent": {"$ref": "AAAAAAFeexlrA3bppiE="}, "name": "NO", "source": {"$ref": "AAAAAAFeexlrA3bppiE="}, "target": {"$ref": "AAAAAAFeewyILnV5nEY="}}, {"_type": "FCFlow", "_id": "AAAAAAFeeyD673edTcM=", "_parent": {"$ref": "AAAAAAFeexlrA3bppiE="}, "name": "YES", "source": {"$ref": "AAAAAAFeexlrA3bppiE="}, "target": {"$ref": "AAAAAAFeeyBSQHeEVe4="}}]}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFeexsU4ncfOLo=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Show scheduling parameters", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeex2PqHdDARg=", "_parent": {"$ref": "AAAAAAFeexsU4ncfOLo="}, "source": {"$ref": "AAAAAAFeexsU4ncfOLo="}, "target": {"$ref": "AAAAAAFeex0Wk3c1WwU="}}]}, {"_type": "FCData", "_id": "AAAAAAFeex0Wk3c1WwU=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User defines scheduling parameters", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeeyLTzXe91z4=", "_parent": {"$ref": "AAAAAAFeex0Wk3c1WwU="}, "source": {"$ref": "AAAAAAFeex0Wk3c1WwU="}, "target": {"$ref": "AAAAAAFeeyJbmHex3IU="}}]}, {"_type": "FCData", "_id": "AAAAAAFeex38sXdR2z8=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User Defines report filters", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeex6t+nd31qI=", "_parent": {"$ref": "AAAAAAFeex38sXdR2z8="}, "source": {"$ref": "AAAAAAFeex38sXdR2z8="}, "target": {"$ref": "AAAAAAFeexVxs3Z5s6A="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFeeyBSQHeEVe4=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User uses report filter preset", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeeyEcQ3eoBmo=", "_parent": {"$ref": "AAAAAAFeeyBSQHeEVe4="}, "source": {"$ref": "AAAAAAFeeyBSQHeEVe4="}, "target": {"$ref": "AAAAAAFeexsU4ncfOLo="}}]}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFeeyJbmHex3IU=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Gather data for scheduling and generate well-formed XML", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeeyMqW3fGkaI=", "_parent": {"$ref": "AAAAAAFeeyJbmHex3IU="}, "source": {"$ref": "AAAAAAFeeyJbmHex3IU="}, "target": {"$ref": "AAAAAAFeewpkF3VD3Xg="}}, {"_type": "FCFlow", "_id": "AAAAAAFeeyto6Xf+oWM=", "_parent": {"$ref": "AAAAAAFeeyJbmHex3IU="}, "source": {"$ref": "AAAAAAFeeyJbmHex3IU="}, "target": {"$ref": "AAAAAAFeeypbwnfuf50="}}]}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFeeypbwnfuf50=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Call JasperReports REST service to init. scheduling", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFeeyt2pngHsJw=", "_parent": {"$ref": "AAAAAAFeeypbwnfuf50="}, "source": {"$ref": "AAAAAAFeeypbwnfuf50="}, "target": {"$ref": "AAAAAAFeewpkF3VD3Xg="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFehe0maUVyzEs=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User selects to view all (his/her) scheduled reports", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFehe6bMEWWeCQ=", "_parent": {"$ref": "AAAAAAFehe0maUVyzEs="}, "source": {"$ref": "AAAAAAFehe0maUVyzEs="}, "target": {"$ref": "AAAAAAFehe4vUEWBtko="}}]}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFehe4vUEWBtko=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Show all user specific scheduled reports", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFehe/nT0WtByg=", "_parent": {"$ref": "AAAAAAFehe4vUEWBtko="}, "source": {"$ref": "AAAAAAFehe4vUEWBtko="}, "target": {"$ref": "AAAAAAFehe939EWhOkA="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFehe939EWhOkA=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User selects a specific scheduled job", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFehfMHLkYP2tk=", "_parent": {"$ref": "AAAAAAFehe939EWhOkA="}, "source": {"$ref": "AAAAAAFehe939EWhOkA="}, "target": {"$ref": "AAAAAAFehfKwJEX2RNI="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFehfCqJEXNjOQ=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User edits a job definition", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFehfIqh0Xr4Ww=", "_parent": {"$ref": "AAAAAAFehfCqJEXNjOQ="}, "source": {"$ref": "AAAAAAFehfCqJEXNjOQ="}, "target": {"$ref": "AAAAAAFeexsU4ncfOLo="}}]}, {"_type": "FCPredefinedProcess", "_id": "AAAAAAFehfKwJEX2RNI=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "Show detailed job description", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFehfNM/kYi0qw=", "_parent": {"$ref": "AAAAAAFehfKwJEX2RNI="}, "source": {"$ref": "AAAAAAFehfKwJEX2RNI="}, "target": {"$ref": "AAAAAAFehfMw7UYZpFA="}}]}, {"_type": "FCOr", "_id": "AAAAAAFehfMw7UYZpFA=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFehfNcd0YrHf4=", "_parent": {"$ref": "AAAAAAFehfMw7UYZpFA="}, "source": {"$ref": "AAAAAAFehfMw7UYZpFA="}, "target": {"$ref": "AAAAAAFehfCqJEXNjOQ="}}, {"_type": "FCFlow", "_id": "AAAAAAFehfUx3kZkXcw=", "_parent": {"$ref": "AAAAAAFehfMw7UYZpFA="}, "source": {"$ref": "AAAAAAFehfMw7UYZpFA="}, "target": {"$ref": "AAAAAAFehfTCJkZWJyc="}}]}, {"_type": "FCProcess", "_id": "AAAAAAFehfTCJkZWJyc=", "_parent": {"$ref": "AAAAAAFeewR9rXUsmlc="}, "name": "User Pauses / Resumes a Report job", "ownedElements": [{"_type": "FCFlow", "_id": "AAAAAAFehfYDbkZv9Fg=", "_parent": {"$ref": "AAAAAAFehfTCJkZWJyc="}, "source": {"$ref": "AAAAAAFehfTCJkZWJyc="}, "target": {"$ref": "AAAAAAFeewpkF3VD3Xg="}}]}]}], "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAFeeuFZkXLVxZ4=", "_parent": {"$ref": "AAAAAAFeet93V3LF3+I="}, "name": "Role1", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeeuLNT3L2oZw=", "_parent": {"$ref": "AAAAAAFeet93V3LF3+I="}, "name": "Role2", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeeuLsGHMW6NU=", "_parent": {"$ref": "AAAAAAFeet93V3LF3+I="}, "name": "Role3", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeeufVM3O1qRA=", "_parent": {"$ref": "AAAAAAFeet93V3LF3+I="}, "name": "Role4", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}], "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}, {"_type": "UMLCollaboration", "_id": "AAAAAAFhg8RyP/nBorE=", "_parent": {"$ref": "AAAAAAFF+h6SjaM2Hec="}, "name": "Collaboration3", "ownedElements": [{"_type": "UMLInteraction", "_id": "AAAAAAFhg8RyP/nC32M=", "_parent": {"$ref": "AAAAAAFhg8RyP/nBorE="}, "name": "Interaction1", "ownedElements": [{"_type": "UMLSequenceDiagram", "_id": "AAAAAAFhg8RyP/nDRt8=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "scheduleReport", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "UMLFrameView", "_id": "AAAAAAFhg8RyP/nE8cg=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFhg8RyP/nFKIo=", "_parent": {"$ref": "AAAAAAFhg8RyP/nE8cg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 142.97900390625, "top": 13, "width": 92.05712890625, "height": 13, "autoResize": false, "underline": false, "text": "scheduleReport", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8RyP/nG5bQ=", "_parent": {"$ref": "AAAAAAFhg8RyP/nE8cg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 77, "top": 13, "width": 60.97900390625, "height": 13, "autoResize": false, "underline": false, "text": "interaction", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 72, "top": 8, "width": 969, "height": 945, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFhg8RyP/nFKIo="}, "frameTypeLabel": {"$ref": "AAAAAAFhg8RyP/nG5bQ="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFhg8UMl/nWjA4=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFhg8UMl/nXAlk=", "_parent": {"$ref": "AAAAAAFhg8UMl/nWjA4="}, "model": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFhg8UMl/nYucY=", "_parent": {"$ref": "AAAAAAFhg8UMl/nXAlk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8UMl/nZTq0=", "_parent": {"$ref": "AAAAAAFhg8UMl/nXAlk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 173, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "User", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8UMl/na/lk=", "_parent": {"$ref": "AAAAAAFhg8UMl/nXAlk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8UMl/nbJKg=", "_parent": {"$ref": "AAAAAAFhg8UMl/nXAlk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFhg8UMl/nYucY="}, "nameLabel": {"$ref": "AAAAAAFhg8UMl/nZTq0="}, "namespaceLabel": {"$ref": "AAAAAAFhg8UMl/na/lk="}, "propertyLabel": {"$ref": "AAAAAAFhg8UMl/nbJKg="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFhg8UMl/ncwpM=", "_parent": {"$ref": "AAAAAAFhg8UMl/nWjA4="}, "model": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 204, "top": 80, "width": 1, "height": 691, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 168, "top": 40, "width": 72.736328125, "height": 731, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFhg8UMl/nXAlk="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFhg8UMl/ncwpM="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFhg8VMkfn4YLg=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFhg8VMkfn5rdY=", "_parent": {"$ref": "AAAAAAFhg8VMkfn4YLg="}, "model": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFhg8VMkfn6Uw0=", "_parent": {"$ref": "AAAAAAFhg8VMkfn5rdY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -48, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8VMkfn7Q2U=", "_parent": {"$ref": "AAAAAAFhg8VMkfn5rdY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 453, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "CCIS", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8VMkfn8o20=", "_parent": {"$ref": "AAAAAAFhg8VMkfn5rdY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -48, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8VMkfn9Ohc=", "_parent": {"$ref": "AAAAAAFhg8VMkfn5rdY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -48, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 448, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFhg8VMkfn6Uw0="}, "nameLabel": {"$ref": "AAAAAAFhg8VMkfn7Q2U="}, "namespaceLabel": {"$ref": "AAAAAAFhg8VMkfn8o20="}, "propertyLabel": {"$ref": "AAAAAAFhg8VMkfn9Ohc="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFhg8VMkfn+kUg=", "_parent": {"$ref": "AAAAAAFhg8VMkfn4YLg="}, "model": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 484, "top": 80, "width": 1, "height": 697, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 448, "top": 40, "width": 72.736328125, "height": 737, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFhg8VMkfn5rdY="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFhg8VMkfn+kUg="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFhg8WRe/oaTyU=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFhg8WRfPobgTw=", "_parent": {"$ref": "AAAAAAFhg8WRe/oaTyU="}, "model": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFhg8WRfPocsc0=", "_parent": {"$ref": "AAAAAAFhg8WRfPobgTw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 256, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8WRfPodXbU=", "_parent": {"$ref": "AAAAAAFhg8WRfPobgTw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 733, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "<PERSON>", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8WRfPoe5ig=", "_parent": {"$ref": "AAAAAAFhg8WRfPobgTw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 256, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFhg8WRfPofZ0g=", "_parent": {"$ref": "AAAAAAFhg8WRfPobgTw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 256, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 728, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFhg8WRfPocsc0="}, "nameLabel": {"$ref": "AAAAAAFhg8WRfPodXbU="}, "namespaceLabel": {"$ref": "AAAAAAFhg8WRfPoe5ig="}, "propertyLabel": {"$ref": "AAAAAAFhg8WRfPofZ0g="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFhg8WRfPogWnQ=", "_parent": {"$ref": "AAAAAAFhg8WRe/oaTyU="}, "model": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 764, "top": 80, "width": 1, "height": 713, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 728, "top": 40, "width": 72.736328125, "height": 753, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFhg8WRfPobgTw="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFhg8WRfPogWnQ="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg8X9YPo9Hew=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8X9X/o8m98="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg8X9YPo+9eI=", "_parent": {"$ref": "AAAAAAFhg8X9YPo9Hew="}, "model": {"$ref": "AAAAAAFhg8X9X/o8m98="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 294, "top": 88, "width": 93.93896484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8X9YPo9Hew="}, "edgePosition": 1, "underline": false, "text": "1 : Open reports", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8X9YPo/4Pk=", "_parent": {"$ref": "AAAAAAFhg8X9YPo9Hew="}, "model": {"$ref": "AAAAAAFhg8X9X/o8m98="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 340, "top": 73, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg8X9YPo9Hew="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8X9YfpAkec=", "_parent": {"$ref": "AAAAAAFhg8X9YPo9Hew="}, "model": {"$ref": "AAAAAAFhg8X9X/o8m98="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 340, "top": 108, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8X9YPo9Hew="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg8X9YfpBmEE=", "_parent": {"$ref": "AAAAAAFhg8X9YPo9Hew="}, "model": {"$ref": "AAAAAAFhg8X9X/o8m98="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 104, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "tail": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "lineStyle": 0, "points": "204:104;477:104", "nameLabel": {"$ref": "AAAAAAFhg8X9YPo+9eI="}, "stereotypeLabel": {"$ref": "AAAAAAFhg8X9YPo/4Pk="}, "propertyLabel": {"$ref": "AAAAAAFhg8X9YfpAkec="}, "activation": {"$ref": "AAAAAAFhg8X9YfpBmEE="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg8ZDKvpTPys=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8ZDKvpSGMI="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg8ZDK/pU4zI=", "_parent": {"$ref": "AAAAAAFhg8ZDKvpTPys="}, "model": {"$ref": "AAAAAAFhg8ZDKvpSGMI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 571, "top": 129, "width": 98.287109375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8ZDKvpTPys="}, "edgePosition": 1, "underline": false, "text": "2 : get schedules", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8ZDK/pVBY4=", "_parent": {"$ref": "AAAAAAFhg8ZDKvpTPys="}, "model": {"$ref": "AAAAAAFhg8ZDKvpSGMI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 620, "top": 114, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg8ZDKvpTPys="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8ZDK/pWvCc=", "_parent": {"$ref": "AAAAAAFhg8ZDKvpTPys="}, "model": {"$ref": "AAAAAAFhg8ZDKvpSGMI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 620, "top": 149, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8ZDKvpTPys="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg8ZDK/pXHVM=", "_parent": {"$ref": "AAAAAAFhg8ZDKvpTPys="}, "model": {"$ref": "AAAAAAFhg8ZDKvpSGMI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 757, "top": 145, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "tail": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "lineStyle": 0, "points": "484:145;757:145", "nameLabel": {"$ref": "AAAAAAFhg8ZDK/pU4zI="}, "stereotypeLabel": {"$ref": "AAAAAAFhg8ZDK/pVBY4="}, "propertyLabel": {"$ref": "AAAAAAFhg8ZDK/pWvCc="}, "activation": {"$ref": "AAAAAAFhg8ZDK/pXHVM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg8aQ6fppRW8=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8aQ6PpoXAw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg8aQ6fpqMbk=", "_parent": {"$ref": "AAAAAAFhg8aQ6fppRW8="}, "model": {"$ref": "AAAAAAFhg8aQ6PpoXAw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 542, "top": 196, "width": 169.0888671875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8aQ6fppRW8="}, "edgePosition": 1, "underline": false, "text": "3 : scheduled reports for user", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8aQ6fprOXU=", "_parent": {"$ref": "AAAAAAFhg8aQ6fppRW8="}, "model": {"$ref": "AAAAAAFhg8aQ6PpoXAw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 626, "top": 211, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg8aQ6fppRW8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8aQ6fpsRjI=", "_parent": {"$ref": "AAAAAAFhg8aQ6fppRW8="}, "model": {"$ref": "AAAAAAFhg8aQ6PpoXAw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 627, "top": 176, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8aQ6fppRW8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg8aQ6fpthvc=", "_parent": {"$ref": "AAAAAAFhg8aQ6fppRW8="}, "model": {"$ref": "AAAAAAFhg8aQ6PpoXAw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 192, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "tail": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "lineStyle": 0, "points": "764:192;490:192", "nameLabel": {"$ref": "AAAAAAFhg8aQ6fpqMbk="}, "stereotypeLabel": {"$ref": "AAAAAAFhg8aQ6fprOXU="}, "propertyLabel": {"$ref": "AAAAAAFhg8aQ6fpsRjI="}, "activation": {"$ref": "AAAAAAFhg8aQ6fpthvc="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg8bjk/p/ckk=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8bjk/p+b9Q="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg8bjlPqAq9c=", "_parent": {"$ref": "AAAAAAFhg8bjk/p/ckk="}, "model": {"$ref": "AAAAAAFhg8bjk/p+b9Q="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 425, "top": 227, "width": 197.29150390625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8bjk/p/ckk="}, "edgePosition": 1, "underline": false, "text": "4 : extrach shedule and report info", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8bjlPqB3VQ=", "_parent": {"$ref": "AAAAAAFhg8bjk/p/ckk="}, "model": {"$ref": "AAAAAAFhg8bjk/p+b9Q="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 538, "top": 227, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg8bjk/p/ckk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8bjlPqCWL0=", "_parent": {"$ref": "AAAAAAFhg8bjk/p/ckk="}, "model": {"$ref": "AAAAAAFhg8bjk/p+b9Q="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 504, "top": 228, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8bjk/p/ckk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg8bjlPqDSFM=", "_parent": {"$ref": "AAAAAAFhg8bjk/p/ckk="}, "model": {"$ref": "AAAAAAFhg8bjk/p+b9Q="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 244, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "tail": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "lineStyle": 0, "points": "484:224;514:224;514:244;490:244", "nameLabel": {"$ref": "AAAAAAFhg8bjlPqAq9c="}, "stereotypeLabel": {"$ref": "AAAAAAFhg8bjlPqB3VQ="}, "propertyLabel": {"$ref": "AAAAAAFhg8bjlPqCWL0="}, "activation": {"$ref": "AAAAAAFhg8bjlPqDSFM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg8daHPqV1uY=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8daHPqUYLQ="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg8daHPqWUhw=", "_parent": {"$ref": "AAAAAAFhg8daHPqV1uY="}, "model": {"$ref": "AAAAAAFhg8daHPqUYLQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 247, "top": 284, "width": 198.7197265625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8daHPqV1uY="}, "edgePosition": 1, "underline": false, "text": "5 : show scheduled reports to user", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8daHPqXHIc=", "_parent": {"$ref": "AAAAAAFhg8daHPqV1uY="}, "model": {"$ref": "AAAAAAFhg8daHPqUYLQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 346, "top": 299, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg8daHPqV1uY="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8daHPqYVww=", "_parent": {"$ref": "AAAAAAFhg8daHPqV1uY="}, "model": {"$ref": "AAAAAAFhg8daHPqUYLQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 347, "top": 264, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8daHPqV1uY="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg8daHPqZV4A=", "_parent": {"$ref": "AAAAAAFhg8daHPqV1uY="}, "model": {"$ref": "AAAAAAFhg8daHPqUYLQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 197, "top": 280, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "tail": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "lineStyle": 0, "points": "484:280;210:280", "nameLabel": {"$ref": "AAAAAAFhg8daHPqWUhw="}, "stereotypeLabel": {"$ref": "AAAAAAFhg8daHPqXHIc="}, "propertyLabel": {"$ref": "AAAAAAFhg8daHPqYVww="}, "activation": {"$ref": "AAAAAAFhg8daHPqZV4A="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg8+Wtfqrlas=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8+WtfqqAt0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg8+WtfqsI5c=", "_parent": {"$ref": "AAAAAAFhg8+Wtfqrlas="}, "model": {"$ref": "AAAAAAFhg8+WtfqqAt0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 580, "top": 297, "width": 80.208984375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8+Wtfqrlas="}, "edgePosition": 1, "underline": false, "text": "6 : get reports", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8+WtfqthvI=", "_parent": {"$ref": "AAAAAAFhg8+Wtfqrlas="}, "model": {"$ref": "AAAAAAFhg8+WtfqqAt0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 620, "top": 282, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg8+Wtfqrlas="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8+WtfquSZ4=", "_parent": {"$ref": "AAAAAAFhg8+Wtfqrlas="}, "model": {"$ref": "AAAAAAFhg8+WtfqqAt0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 620, "top": 317, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8+Wtfqrlas="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg8+Wtfqv3H4=", "_parent": {"$ref": "AAAAAAFhg8+Wtfqrlas="}, "model": {"$ref": "AAAAAAFhg8+WtfqqAt0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 757, "top": 313, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "tail": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "lineStyle": 0, "points": "484:313;757:313", "nameLabel": {"$ref": "AAAAAAFhg8+WtfqsI5c="}, "stereotypeLabel": {"$ref": "AAAAAAFhg8+WtfqthvI="}, "propertyLabel": {"$ref": "AAAAAAFhg8+WtfquSZ4="}, "activation": {"$ref": "AAAAAAFhg8+Wtfqv3H4="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg8/M1vrBlcE=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg8/M1vrA1MY="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg8/M1vrCgHQ=", "_parent": {"$ref": "AAAAAAFhg8/M1vrBlcE="}, "model": {"$ref": "AAAAAAFhg8/M1vrA1MY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 573, "top": 356, "width": 106.208984375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8/M1vrBlcE="}, "edgePosition": 1, "underline": false, "text": "7 : reports for user", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8/M1vrDAbU=", "_parent": {"$ref": "AAAAAAFhg8/M1vrBlcE="}, "model": {"$ref": "AAAAAAFhg8/M1vrA1MY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 626, "top": 371, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg8/M1vrBlcE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg8/M1vrE/GY=", "_parent": {"$ref": "AAAAAAFhg8/M1vrBlcE="}, "model": {"$ref": "AAAAAAFhg8/M1vrA1MY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 627, "top": 336, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg8/M1vrBlcE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg8/M1vrF7R8=", "_parent": {"$ref": "AAAAAAFhg8/M1vrBlcE="}, "model": {"$ref": "AAAAAAFhg8/M1vrA1MY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 352, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "tail": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "lineStyle": 0, "points": "764:352;490:352", "nameLabel": {"$ref": "AAAAAAFhg8/M1vrCgHQ="}, "stereotypeLabel": {"$ref": "AAAAAAFhg8/M1vrDAbU="}, "propertyLabel": {"$ref": "AAAAAAFhg8/M1vrE/GY="}, "activation": {"$ref": "AAAAAAFhg8/M1vrF7R8="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9GBCPrXuro=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9GBCPrW3nQ="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9GBCPrYlIA=", "_parent": {"$ref": "AAAAAAFhg9GBCPrXuro="}, "model": {"$ref": "AAAAAAFhg9GBCPrW3nQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 270, "top": 400, "width": 152.4580078125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9GBCPrXuro="}, "edgePosition": 1, "underline": false, "text": "8 : show all reports to user", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9GBCPrZ+JA=", "_parent": {"$ref": "AAAAAAFhg9GBCPrXuro="}, "model": {"$ref": "AAAAAAFhg9GBCPrW3nQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 346, "top": 415, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9GBCPrXuro="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9GBCPraR/c=", "_parent": {"$ref": "AAAAAAFhg9GBCPrXuro="}, "model": {"$ref": "AAAAAAFhg9GBCPrW3nQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 347, "top": 380, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9GBCPrXuro="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9GBCPrbCuU=", "_parent": {"$ref": "AAAAAAFhg9GBCPrXuro="}, "model": {"$ref": "AAAAAAFhg9GBCPrW3nQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 197, "top": 396, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "tail": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "lineStyle": 0, "points": "484:396;210:396", "nameLabel": {"$ref": "AAAAAAFhg9GBCPrYlIA="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9GBCPrZ+JA="}, "propertyLabel": {"$ref": "AAAAAAFhg9GBCPraR/c="}, "activation": {"$ref": "AAAAAAFhg9GBCPrbCuU="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9GugvrtxyU=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9GugvrssH0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9Gugvru6To=", "_parent": {"$ref": "AAAAAAFhg9GugvrtxyU="}, "model": {"$ref": "AAAAAAFhg9GugvrssH0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 130, "top": 435, "width": 226.9033203125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9GugvrtxyU="}, "edgePosition": 1, "underline": false, "text": "9 : user wants to schedule a new report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9GugvrvrIA=", "_parent": {"$ref": "AAAAAAFhg9GugvrtxyU="}, "model": {"$ref": "AAAAAAFhg9GugvrssH0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 258, "top": 435, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9GugvrtxyU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9GugvrwHYc=", "_parent": {"$ref": "AAAAAAFhg9GugvrtxyU="}, "model": {"$ref": "AAAAAAFhg9GugvrssH0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 224, "top": 436, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9GugvrtxyU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9Gugvrx0UA=", "_parent": {"$ref": "AAAAAAFhg9GugvrtxyU="}, "model": {"$ref": "AAAAAAFhg9GugvrssH0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 197, "top": 452, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "tail": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "lineStyle": 0, "points": "204:432;234:432;234:452;210:452", "nameLabel": {"$ref": "AAAAAAFhg9Gugvru6To="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9GugvrvrIA="}, "propertyLabel": {"$ref": "AAAAAAFhg9GugvrwHYc="}, "activation": {"$ref": "AAAAAAFhg9Gugvrx0UA="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9IEwvsDzw4=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9IEwvsCJvw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9IEwvsEYSw=", "_parent": {"$ref": "AAAAAAFhg9IEwvsDzw4="}, "model": {"$ref": "AAAAAAFhg9IEwvsCJvw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 288, "top": 474, "width": 104.06982421875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9IEwvsDzw4="}, "edgePosition": 1, "underline": false, "text": "10 : open report A", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9IEwvsFP2c=", "_parent": {"$ref": "AAAAAAFhg9IEwvsDzw4="}, "model": {"$ref": "AAAAAAFhg9IEwvsCJvw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 340, "top": 459, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9IEwvsDzw4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9IEwvsGsh8=", "_parent": {"$ref": "AAAAAAFhg9IEwvsDzw4="}, "model": {"$ref": "AAAAAAFhg9IEwvsCJvw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 340, "top": 494, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9IEwvsDzw4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9IEwvsHKGw=", "_parent": {"$ref": "AAAAAAFhg9IEwvsDzw4="}, "model": {"$ref": "AAAAAAFhg9IEwvsCJvw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 490, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "tail": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "lineStyle": 0, "points": "204:490;477:490", "nameLabel": {"$ref": "AAAAAAFhg9IEwvsEYSw="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9IEwvsFP2c="}, "propertyLabel": {"$ref": "AAAAAAFhg9IEwvsGsh8="}, "activation": {"$ref": "AAAAAAFhg9IEwvsHKGw="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9aMFfueVmw=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9aMFfuduNk="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9aMFfuf5nY=", "_parent": {"$ref": "AAAAAAFhg9aMFfueVmw="}, "model": {"$ref": "AAAAAAFhg9aMFfuduNk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 244, "top": 532, "width": 204.47705078125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9aMFfueVmw="}, "edgePosition": 1, "underline": false, "text": "11 : show report A parameters view", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9aMFfugJM4=", "_parent": {"$ref": "AAAAAAFhg9aMFfueVmw="}, "model": {"$ref": "AAAAAAFhg9aMFfuduNk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 346, "top": 547, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9aMFfueVmw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9aMFfuhVFA=", "_parent": {"$ref": "AAAAAAFhg9aMFfueVmw="}, "model": {"$ref": "AAAAAAFhg9aMFfuduNk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 347, "top": 512, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9aMFfueVmw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9aMFfuiLJM=", "_parent": {"$ref": "AAAAAAFhg9aMFfueVmw="}, "model": {"$ref": "AAAAAAFhg9aMFfuduNk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 197, "top": 528, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "tail": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "lineStyle": 0, "points": "484:528;210:528", "nameLabel": {"$ref": "AAAAAAFhg9aMFfuf5nY="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9aMFfugJM4="}, "propertyLabel": {"$ref": "AAAAAAFhg9aMFfuhVFA="}, "activation": {"$ref": "AAAAAAFhg9aMFfuiLJM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9QsyvtGQ24=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9QsyvtF1zE="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9QsyvtHSq4=", "_parent": {"$ref": "AAAAAAFhg9QsyvtGQ24="}, "model": {"$ref": "AAAAAAFhg9QsyvtF1zE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 242, "top": 560, "width": 197.2724609375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9QsyvtGQ24="}, "edgePosition": 1, "underline": false, "text": "12 : set parameters and run report", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9QszvtIoLs=", "_parent": {"$ref": "AAAAAAFhg9QsyvtGQ24="}, "model": {"$ref": "AAAAAAFhg9QsyvtF1zE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 340, "top": 545, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9QsyvtGQ24="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9QszvtJ2K0=", "_parent": {"$ref": "AAAAAAFhg9QsyvtGQ24="}, "model": {"$ref": "AAAAAAFhg9QsyvtF1zE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 340, "top": 580, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9QsyvtGQ24="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9QszvtKQV0=", "_parent": {"$ref": "AAAAAAFhg9QsyvtGQ24="}, "model": {"$ref": "AAAAAAFhg9QsyvtF1zE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 576, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "tail": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "lineStyle": 0, "points": "204:576;477:576", "nameLabel": {"$ref": "AAAAAAFhg9QsyvtHSq4="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9QszvtIoLs="}, "propertyLabel": {"$ref": "AAAAAAFhg9QszvtJ2K0="}, "activation": {"$ref": "AAAAAAFhg9QszvtKQV0="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9VH6vtdHfs=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9VH6vtcIzg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9VH6vteAu0=", "_parent": {"$ref": "AAAAAAFhg9VH6vtdHfs="}, "model": {"$ref": "AAAAAAFhg9VH6vtcIzg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 532, "top": 600, "width": 177.7470703125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9VH6vtdHfs="}, "edgePosition": 1, "underline": false, "text": "13 : run report with parameters", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9VH6vtf3yk=", "_parent": {"$ref": "AAAAAAFhg9VH6vtdHfs="}, "model": {"$ref": "AAAAAAFhg9VH6vtcIzg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 620, "top": 585, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9VH6vtdHfs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9VH6vtg7lk=", "_parent": {"$ref": "AAAAAAFhg9VH6vtdHfs="}, "model": {"$ref": "AAAAAAFhg9VH6vtcIzg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 620, "top": 620, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9VH6vtdHfs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9VH6vthPPE=", "_parent": {"$ref": "AAAAAAFhg9VH6vtdHfs="}, "model": {"$ref": "AAAAAAFhg9VH6vtcIzg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 757, "top": 616, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "tail": {"$ref": "AAAAAAFhg8VMkfn+kUg="}, "lineStyle": 0, "points": "484:616;757:616", "nameLabel": {"$ref": "AAAAAAFhg9VH6vteAu0="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9VH6vtf3yk="}, "propertyLabel": {"$ref": "AAAAAAFhg9VH6vtg7lk="}, "activation": {"$ref": "AAAAAAFhg9VH6vthPPE="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9c0Afu04lk=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9cz/fuzMak="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9c0Afu1gJE=", "_parent": {"$ref": "AAAAAAFhg9c0Afu04lk="}, "model": {"$ref": "AAAAAAFhg9cz/fuzMak="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 364, "top": 688, "width": 232.68603515625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9c0Afu04lk="}, "edgePosition": 1, "underline": false, "text": "15 : schedule report by selecting interval", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9c0Afu2LQ0=", "_parent": {"$ref": "AAAAAAFhg9c0Afu04lk="}, "model": {"$ref": "AAAAAAFhg9cz/fuzMak="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 480, "top": 673, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9c0Afu04lk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9c0Afu30MY=", "_parent": {"$ref": "AAAAAAFhg9c0Afu04lk="}, "model": {"$ref": "AAAAAAFhg9cz/fuzMak="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 480, "top": 708, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9c0Afu04lk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9c0Afu4EZU=", "_parent": {"$ref": "AAAAAAFhg9c0Afu04lk="}, "model": {"$ref": "AAAAAAFhg9cz/fuzMak="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 757, "top": 704, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "tail": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "lineStyle": 0, "points": "204:704;757:704", "nameLabel": {"$ref": "AAAAAAFhg9c0Afu1gJE="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9c0Afu2LQ0="}, "propertyLabel": {"$ref": "AAAAAAFhg9c0Afu30MY="}, "activation": {"$ref": "AAAAAAFhg9c0Afu4EZU="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9fAafvMDI8=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9fAafvLSwI="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9fAafvNgBk=", "_parent": {"$ref": "AAAAAAFhg9fAafvMDI8="}, "model": {"$ref": "AAAAAAFhg9fAafvLSwI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 732, "top": 731, "width": 154.64794921875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9fAafvMDI8="}, "edgePosition": 1, "underline": false, "text": "16 : create report schedule", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9fAafvOe/k=", "_parent": {"$ref": "AAAAAAFhg9fAafvMDI8="}, "model": {"$ref": "AAAAAAFhg9fAafvLSwI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 824, "top": 731, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9fAafvMDI8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9fAafvPLBA=", "_parent": {"$ref": "AAAAAAFhg9fAafvMDI8="}, "model": {"$ref": "AAAAAAFhg9fAafvLSwI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 790, "top": 732, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9fAafvMDI8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9fAafvQRKE=", "_parent": {"$ref": "AAAAAAFhg9fAafvMDI8="}, "model": {"$ref": "AAAAAAFhg9fAafvLSwI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 757, "top": 748, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "tail": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "lineStyle": 0, "points": "770:728;800:728;800:748;770:748", "nameLabel": {"$ref": "AAAAAAFhg9fAafvNgBk="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9fAafvOe/k="}, "propertyLabel": {"$ref": "AAAAAAFhg9fAafvPLBA="}, "activation": {"$ref": "AAAAAAFhg9fAafvQRKE="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFhg9WOrPtzsvQ=", "_parent": {"$ref": "AAAAAAFhg8RyP/nDRt8="}, "model": {"$ref": "AAAAAAFhg9WOrPtyyXg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFhg9WOrPt0sqA=", "_parent": {"$ref": "AAAAAAFhg9WOrPtzsvQ="}, "model": {"$ref": "AAAAAAFhg9WOrPtyyXg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 434, "top": 660, "width": 105.498046875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9WOrPtzsvQ="}, "edgePosition": 1, "underline": false, "text": "14 : show report A", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9WOrPt1QIU=", "_parent": {"$ref": "AAAAAAFhg9WOrPtzsvQ="}, "model": {"$ref": "AAAAAAFhg9WOrPtyyXg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 486, "top": 675, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFhg9WOrPtzsvQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFhg9WOrPt2hzM=", "_parent": {"$ref": "AAAAAAFhg9WOrPtzsvQ="}, "model": {"$ref": "AAAAAAFhg9WOrPtyyXg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 487, "top": 640, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFhg9WOrPtzsvQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFhg9WOrPt3cVA=", "_parent": {"$ref": "AAAAAAFhg9WOrPtzsvQ="}, "model": {"$ref": "AAAAAAFhg9WOrPtyyXg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 197, "top": 656, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFhg8UMl/ncwpM="}, "tail": {"$ref": "AAAAAAFhg8WRfPogWnQ="}, "lineStyle": 0, "points": "764:656;210:656", "nameLabel": {"$ref": "AAAAAAFhg9WOrPt0sqA="}, "stereotypeLabel": {"$ref": "AAAAAAFhg9WOrPt1QIU="}, "propertyLabel": {"$ref": "AAAAAAFhg9WOrPt2hzM="}, "activation": {"$ref": "AAAAAAFhg9WOrPt3cVA="}, "showProperty": true, "showType": true}], "showSequenceNumber": true, "showSignature": true, "showActivation": true}], "visibility": "public", "isReentrant": true, "messages": [{"_type": "UMLMessage", "_id": "AAAAAAFhg8X9X/o8m98=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "Open reports", "source": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "target": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg8ZDKvpSGMI=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "get schedules", "source": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "target": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg8aQ6PpoXAw=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "scheduled reports for user", "source": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "target": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg8bjk/p+b9Q=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "extrach shedule and report info", "source": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "target": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg8daHPqUYLQ=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "show scheduled reports to user", "source": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "target": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg8+WtfqqAt0=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "get reports", "source": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "target": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg8/M1vrA1MY=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "reports for user", "source": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "target": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9GBCPrW3nQ=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "show all reports to user", "source": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "target": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9GugvrssH0=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "user wants to schedule a new report", "source": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "target": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9IEwvsCJvw=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "open report A", "source": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "target": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9aMFfuduNk=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "show report A parameters view", "source": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "target": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9QsyvtF1zE=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "set parameters and run report", "source": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "target": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9VH6vtcIzg=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "run report with parameters", "source": {"$ref": "AAAAAAFhg8VMkfn3AGw="}, "target": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9WOrPtyyXg=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "show report A", "source": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "target": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9cz/fuzMak=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "schedule report by selecting interval", "source": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "target": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9fAafvLSwI=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "create report schedule", "source": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "target": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFhg9ONZfsughE=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "show report A", "source": {"$ref": "AAAAAAFhg8WRe/oZ6eU="}, "target": {"$ref": "AAAAAAFhg8UMl/nVNfU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}], "participants": [{"_type": "UMLLifeline", "_id": "AAAAAAFhg8UMl/nVNfU=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "User", "visibility": "public", "represent": {"$ref": "AAAAAAFhg8UMlvnUjKw="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFhg8VMkfn3AGw=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "CCIS", "visibility": "public", "represent": {"$ref": "AAAAAAFhg8VMkfn28yc="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFhg8WRe/oZ6eU=", "_parent": {"$ref": "AAAAAAFhg8RyP/nC32M="}, "name": "<PERSON>", "visibility": "public", "represent": {"$ref": "AAAAAAFhg8WRe/oYnyk="}, "isMultiInstance": false}]}], "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAFhg8UMlvnUjKw=", "_parent": {"$ref": "AAAAAAFhg8RyP/nBorE="}, "name": "Role1", "visibility": "public", "isStatic": false, "isLeaf": false, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFhg8VMkfn28yc=", "_parent": {"$ref": "AAAAAAFhg8RyP/nBorE="}, "name": "Role2", "visibility": "public", "isStatic": false, "isLeaf": false, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFhg8WRe/oYnyk=", "_parent": {"$ref": "AAAAAAFhg8RyP/nBorE="}, "name": "Role3", "visibility": "public", "isStatic": false, "isLeaf": false, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}], "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}]}