{"_type": "Project", "_id": "AAAAAAFF+h6SjaM2Hec=", "name": "LoggerReceiver", "ownedElements": [{"_type": "UMLModel", "_id": "AAAAAAFF+qBWK6M3Z8Y=", "_parent": {"$ref": "AAAAAAFF+h6SjaM2Hec="}, "name": "Model", "ownedElements": [{"_type": "UMLClassDiagram", "_id": "AAAAAAFF+qBtyKM79qY=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "Main", "visible": true, "defaultDiagram": true}, {"_type": "UMLCollaboration", "_id": "AAAAAAFeljwGCaFacf8=", "_parent": {"$ref": "AAAAAAFF+qBWK6M3Z8Y="}, "name": "Collaboration1", "ownedElements": [{"_type": "UMLInteraction", "_id": "AAAAAAFeljwGCaFb+0M=", "_parent": {"$ref": "AAAAAAFeljwGCaFacf8="}, "name": "Interaction1", "ownedElements": [{"_type": "UMLSequenceDiagram", "_id": "AAAAAAFeljwGCaFc6Ss=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "berlingerLogger", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "UMLFrameView", "_id": "AAAAAAFeljwGCaFdjFk=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeljwGCaFeHbw=", "_parent": {"$ref": "AAAAAAFeljwGCaFdjFk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 75.97900390625, "top": 10, "width": 92.0634765625, "height": 13, "autoResize": false, "underline": false, "text": "berlingerLogger", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljwGCaFfj14=", "_parent": {"$ref": "AAAAAAFeljwGCaFdjFk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 10, "top": 10, "width": 60.97900390625, "height": 13, "autoResize": false, "underline": false, "text": "interaction", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 5, "top": 5, "width": 588, "height": 452, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeljwGCaFeHbw="}, "frameTypeLabel": {"$ref": "AAAAAAFeljwGCaFfj14="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeljxXTaFs1fk=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFeljxXTaFrp9k="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeljxXTqFtUSU=", "_parent": {"$ref": "AAAAAAFeljxXTaFs1fk="}, "model": {"$ref": "AAAAAAFeljxXTaFrp9k="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeljxXTqFuPa4=", "_parent": {"$ref": "AAAAAAFeljxXTqFtUSU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljxXTqFvt44=", "_parent": {"$ref": "AAAAAAFeljxXTqFtUSU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 101, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "User", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljxXTqFwAAA=", "_parent": {"$ref": "AAAAAAFeljxXTqFtUSU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljxXTqFxMy4=", "_parent": {"$ref": "AAAAAAFeljxXTqFtUSU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 96, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeljxXTqFuPa4="}, "nameLabel": {"$ref": "AAAAAAFeljxXTqFvt44="}, "namespaceLabel": {"$ref": "AAAAAAFeljxXTqFwAAA="}, "propertyLabel": {"$ref": "AAAAAAFeljxXTqFxMy4="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeljxXTqFyJm4=", "_parent": {"$ref": "AAAAAAFeljxXTaFs1fk="}, "model": {"$ref": "AAAAAAFeljxXTaFrp9k="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 132, "top": 80, "width": 1, "height": 345, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 96, "top": 40, "width": 72.736328125, "height": 385, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeljxXTqFtUSU="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeljxXTqFyJm4="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeljx7x6GMSXU=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeljx7x6GNZ7s=", "_parent": {"$ref": "AAAAAAFeljx7x6GMSXU="}, "model": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeljx7x6GOP1Y=", "_parent": {"$ref": "AAAAAAFeljx7x6GNZ7s="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljx7yKGP9oI=", "_parent": {"$ref": "AAAAAAFeljx7x6GNZ7s="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 373, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "CCIS", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljx7yKGQhuo=", "_parent": {"$ref": "AAAAAAFeljx7x6GNZ7s="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljx7yKGRSWU=", "_parent": {"$ref": "AAAAAAFeljx7x6GNZ7s="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 368, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeljx7x6GOP1Y="}, "nameLabel": {"$ref": "AAAAAAFeljx7yKGP9oI="}, "namespaceLabel": {"$ref": "AAAAAAFeljx7yKGQhuo="}, "propertyLabel": {"$ref": "AAAAAAFeljx7yKGRSWU="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeljx7yKGS9+M=", "_parent": {"$ref": "AAAAAAFeljx7x6GMSXU="}, "model": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 404, "top": 80, "width": 1, "height": 345, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 368, "top": 40, "width": 72.736328125, "height": 385, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeljx7x6GNZ7s="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeljx7yKGS9+M="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljyj26Gr1O8=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFeljyj26GqspA="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljyj26GsKVg=", "_parent": {"$ref": "AAAAAAFeljyj26Gr1O8="}, "model": {"$ref": "AAAAAAFeljyj26GqspA="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 177, "top": 97, "width": 174, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljyj26Gr1O8="}, "edgePosition": 1, "underline": false, "text": "1 : Upload berlinger logger file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljyj3KGtTbc=", "_parent": {"$ref": "AAAAAAFeljyj26Gr1O8="}, "model": {"$ref": "AAAAAAFeljyj26GqspA="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 264, "top": 82, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljyj26Gr1O8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljyj3KGuPt4=", "_parent": {"$ref": "AAAAAAFeljyj26Gr1O8="}, "model": {"$ref": "AAAAAAFeljyj26GqspA="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 264, "top": 117, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljyj26Gr1O8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljyj3KGveck=", "_parent": {"$ref": "AAAAAAFeljyj26Gr1O8="}, "model": {"$ref": "AAAAAAFeljyj26GqspA="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 397, "top": 113, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "tail": {"$ref": "AAAAAAFeljxXTqFyJm4="}, "lineStyle": 0, "points": "132:113;397:113", "nameLabel": {"$ref": "AAAAAAFeljyj26GsKVg="}, "stereotypeLabel": {"$ref": "AAAAAAFeljyj3KGtTbc="}, "propertyLabel": {"$ref": "AAAAAAFeljyj3KGuPt4="}, "activation": {"$ref": "AAAAAAFeljyj3KGveck="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljzavqHBYWA=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFeljzavaHAS7Y="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljzavqHCmnw=", "_parent": {"$ref": "AAAAAAFeljzavqHBYWA="}, "model": {"$ref": "AAAAAAFeljzavaHAS7Y="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 384, "top": 147, "width": 118.5107421875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljzavqHBYWA="}, "edgePosition": 1, "underline": false, "text": "2 : Parse file content", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljzavqHDCCI=", "_parent": {"$ref": "AAAAAAFeljzavqHBYWA="}, "model": {"$ref": "AAAAAAFeljzavaHAS7Y="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 458, "top": 147, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljzavqHBYWA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljzavqHErfU=", "_parent": {"$ref": "AAAAAAFeljzavqHBYWA="}, "model": {"$ref": "AAAAAAFeljzavaHAS7Y="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 424, "top": 148, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljzavqHBYWA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljzavqHF3xk=", "_parent": {"$ref": "AAAAAAFeljzavqHBYWA="}, "model": {"$ref": "AAAAAAFeljzavaHAS7Y="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 397, "top": 164, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "tail": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "lineStyle": 0, "points": "404:144;434:144;434:164;410:164", "nameLabel": {"$ref": "AAAAAAFeljzavqHCmnw="}, "stereotypeLabel": {"$ref": "AAAAAAFeljzavqHDCCI="}, "propertyLabel": {"$ref": "AAAAAAFeljzavqHErfU="}, "activation": {"$ref": "AAAAAAFeljzavqHF3xk="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFelj0NraHX9ow=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFelj0NraHWsD0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFelj0NrqHYcC8=", "_parent": {"$ref": "AAAAAAFelj0NraHX9ow="}, "model": {"$ref": "AAAAAAFelj0NraHWsD0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 367, "top": 187, "width": 164, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelj0NraHX9ow="}, "edgePosition": 1, "underline": false, "text": "3 : verify content with crypto", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelj0NrqHZz40=", "_parent": {"$ref": "AAAAAAFelj0NraHX9ow="}, "model": {"$ref": "AAAAAAFelj0NraHWsD0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 464, "top": 187, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFelj0NraHX9ow="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelj0NrqHa3rQ=", "_parent": {"$ref": "AAAAAAFelj0NraHX9ow="}, "model": {"$ref": "AAAAAAFelj0NraHWsD0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 430, "top": 188, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelj0NraHX9ow="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFelj0NrqHbiBI=", "_parent": {"$ref": "AAAAAAFelj0NraHX9ow="}, "model": {"$ref": "AAAAAAFelj0NraHWsD0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 397, "top": 204, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "tail": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "lineStyle": 0, "points": "410:184;440:184;440:204;410:204", "nameLabel": {"$ref": "AAAAAAFelj0NrqHYcC8="}, "stereotypeLabel": {"$ref": "AAAAAAFelj0NrqHZz40="}, "propertyLabel": {"$ref": "AAAAAAFelj0NrqHa3rQ="}, "activation": {"$ref": "AAAAAAFelj0NrqHbiBI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFelj1W5KHtUmg=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFelj1W5KHsqAU="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFelj1W5KHuQEE=", "_parent": {"$ref": "AAAAAAFelj1W5KHtUmg="}, "model": {"$ref": "AAAAAAFelj1W5KHsqAU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 337, "top": 235, "width": 213.1796875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelj1W5KHtUmg="}, "edgePosition": 1, "underline": false, "text": "4 : store data, trigger calculations", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelj1W5KHvdps=", "_parent": {"$ref": "AAAAAAFelj1W5KHtUmg="}, "model": {"$ref": "AAAAAAFelj1W5KHsqAU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 458, "top": 235, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFelj1W5KHtUmg="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelj1W5aHw/6U=", "_parent": {"$ref": "AAAAAAFelj1W5KHtUmg="}, "model": {"$ref": "AAAAAAFelj1W5KHsqAU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 424, "top": 236, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelj1W5KHtUmg="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFelj1W5aHxwZY=", "_parent": {"$ref": "AAAAAAFelj1W5KHtUmg="}, "model": {"$ref": "AAAAAAFelj1W5KHsqAU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 397, "top": 252, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "tail": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "lineStyle": 0, "points": "404:232;434:232;434:252;410:252", "nameLabel": {"$ref": "AAAAAAFelj1W5KHuQEE="}, "stereotypeLabel": {"$ref": "AAAAAAFelj1W5KHvdps="}, "propertyLabel": {"$ref": "AAAAAAFelj1W5aHw/6U="}, "activation": {"$ref": "AAAAAAFelj1W5aHxwZY="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFelj3nh6IEatk=", "_parent": {"$ref": "AAAAAAFeljwGCaFc6Ss="}, "model": {"$ref": "AAAAAAFelj3nh6ID+7g="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFelj3niKIF8Wg=", "_parent": {"$ref": "AAAAAAFelj3nh6IEatk="}, "model": {"$ref": "AAAAAAFelj3nh6ID+7g="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 229, "top": 308, "width": 83.814453125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelj3nh6IEatk="}, "edgePosition": 1, "underline": false, "text": "5 : show result", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelj3niKIGbjI=", "_parent": {"$ref": "AAAAAAFelj3nh6IEatk="}, "model": {"$ref": "AAAAAAFelj3nh6ID+7g="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 270, "top": 323, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFelj3nh6IEatk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelj3niKIH4kE=", "_parent": {"$ref": "AAAAAAFelj3nh6IEatk="}, "model": {"$ref": "AAAAAAFelj3nh6ID+7g="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 271, "top": 288, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelj3nh6IEatk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFelj3niKII4Fo=", "_parent": {"$ref": "AAAAAAFelj3nh6IEatk="}, "model": {"$ref": "AAAAAAFelj3nh6ID+7g="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 125, "top": 304, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljxXTqFyJm4="}, "tail": {"$ref": "AAAAAAFeljx7yKGS9+M="}, "lineStyle": 0, "points": "404:304;138:304", "nameLabel": {"$ref": "AAAAAAFelj3niKIF8Wg="}, "stereotypeLabel": {"$ref": "AAAAAAFelj3niKIGbjI="}, "propertyLabel": {"$ref": "AAAAAAFelj3niKIH4kE="}, "activation": {"$ref": "AAAAAAFelj3niKII4Fo="}, "showProperty": true, "showType": true}], "showSequenceNumber": true, "showSignature": true, "showActivation": true}], "visibility": "public", "isReentrant": true, "messages": [{"_type": "UMLMessage", "_id": "AAAAAAFeljyj26GqspA=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "Upload berlinger logger file", "source": {"$ref": "AAAAAAFeljxXTaFrp9k="}, "target": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljzavaHAS7Y=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "Parse file content", "source": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "target": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFelj0NraHWsD0=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "verify content with crypto", "source": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "target": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFelj1W5KHsqAU=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "store data, trigger calculations", "source": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "target": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFelj3nh6ID+7g=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "show result", "source": {"$ref": "AAAAAAFeljx7x6GLcJ0="}, "target": {"$ref": "AAAAAAFeljxXTaFrp9k="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}], "participants": [{"_type": "UMLLifeline", "_id": "AAAAAAFeljxXTaFrp9k=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "User", "visibility": "public", "represent": {"$ref": "AAAAAAFeljxXTaFqMVI="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeljx7x6GLcJ0=", "_parent": {"$ref": "AAAAAAFeljwGCaFb+0M="}, "name": "CCIS", "visibility": "public", "represent": {"$ref": "AAAAAAFeljx7x6GKRAk="}, "isMultiInstance": false}]}], "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAFeljxXTaFqMVI=", "_parent": {"$ref": "AAAAAAFeljwGCaFacf8="}, "name": "Role1", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeljx7x6GKRAk=", "_parent": {"$ref": "AAAAAAFeljwGCaFacf8="}, "name": "Role2", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}], "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}], "visibility": "public"}, {"_type": "UMLCollaboration", "_id": "AAAAAAFeljPqap+zMjM=", "_parent": {"$ref": "AAAAAAFF+h6SjaM2Hec="}, "name": "Collaboration1", "ownedElements": [{"_type": "UMLInteraction", "_id": "AAAAAAFeljPqap+0VBs=", "_parent": {"$ref": "AAAAAAFeljPqap+zMjM="}, "name": "Interaction1", "ownedElements": [{"_type": "UMLSequenceDiagram", "_id": "AAAAAAFeljPqap+1aTo=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "elproLogger", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "UMLFrameView", "_id": "AAAAAAFeljPqap+2DG0=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljPqap+1aTo="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeljPqap+3DaQ=", "_parent": {"$ref": "AAAAAAFeljPqap+2DG0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 70.97900390625, "top": 5, "width": 70.38623046875, "height": 13, "autoResize": false, "underline": false, "text": "elproLogger", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljPqeZ+4qps=", "_parent": {"$ref": "AAAAAAFeljPqap+2DG0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 5, "top": 5, "width": 60.97900390625, "height": 13, "autoResize": false, "underline": false, "text": "interaction", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 921, "height": 737, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFeljPqap+3DaQ="}, "frameTypeLabel": {"$ref": "AAAAAAFeljPqeZ+4qps="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeljTi9KAB4Tk=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljTi9KAA97Q="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeljTi9KACaqk=", "_parent": {"$ref": "AAAAAAFeljTi9KAB4Tk="}, "model": {"$ref": "AAAAAAFeljTi9KAA97Q="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeljTi9KADh9U=", "_parent": {"$ref": "AAAAAAFeljTi9KACaqk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 48, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljTi9KAEqHI=", "_parent": {"$ref": "AAAAAAFeljTi9KACaqk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 189, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "User", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljTi9KAFlxE=", "_parent": {"$ref": "AAAAAAFeljTi9KACaqk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 48, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljTi9KAGI2g=", "_parent": {"$ref": "AAAAAAFeljTi9KACaqk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 48, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 184, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeljTi9KADh9U="}, "nameLabel": {"$ref": "AAAAAAFeljTi9KAEqHI="}, "namespaceLabel": {"$ref": "AAAAAAFeljTi9KAFlxE="}, "propertyLabel": {"$ref": "AAAAAAFeljTi9KAGI2g="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeljTi9aAHbIc=", "_parent": {"$ref": "AAAAAAFeljTi9KAB4Tk="}, "model": {"$ref": "AAAAAAFeljTi9KAA97Q="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 220, "top": 80, "width": 1, "height": 617, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 184, "top": 40, "width": 72.736328125, "height": 657, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeljTi9KACaqk="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeljTi9aAHbIc="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeljVDqKAjGCs=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljVDqKAimr0="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeljVDqKAkQHA=", "_parent": {"$ref": "AAAAAAFeljVDqKAjGCs="}, "model": {"$ref": "AAAAAAFeljVDqKAimr0="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeljVDqaAlgS0=", "_parent": {"$ref": "AAAAAAFeljVDqKAkQHA="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 80, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljVDqaAmfwQ=", "_parent": {"$ref": "AAAAAAFeljVDqKAkQHA="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 453, "top": 47, "width": 62.736328125, "height": 13, "autoResize": false, "underline": false, "text": "CCIS", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljVDqaAnYIg=", "_parent": {"$ref": "AAAAAAFeljVDqKAkQHA="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 80, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljVDqaAobvI=", "_parent": {"$ref": "AAAAAAFeljVDqKAkQHA="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 80, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 448, "top": 40, "width": 72.736328125, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeljVDqaAlgS0="}, "nameLabel": {"$ref": "AAAAAAFeljVDqaAmfwQ="}, "namespaceLabel": {"$ref": "AAAAAAFeljVDqaAnYIg="}, "propertyLabel": {"$ref": "AAAAAAFeljVDqaAobvI="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeljVDqaApQpw=", "_parent": {"$ref": "AAAAAAFeljVDqKAjGCs="}, "model": {"$ref": "AAAAAAFeljVDqKAimr0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 484, "top": 80, "width": 1, "height": 609, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 448, "top": 40, "width": 72.736328125, "height": 649, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeljVDqKAkQHA="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeljVDqaApQpw="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFeljVuJ6BDakg=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFeljVuJ6BEYbg=", "_parent": {"$ref": "AAAAAAFeljVuJ6BDakg="}, "model": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFeljVuJ6BFLFo=", "_parent": {"$ref": "AAAAAAFeljVuJ6BEYbg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljVuJ6BG31k=", "_parent": {"$ref": "AAAAAAFeljVuJ6BEYbg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 701, "top": 47, "width": 144.37353515625, "height": 13, "autoResize": false, "underline": false, "text": "Elpro windows server", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljVuJ6BHXBY=", "_parent": {"$ref": "AAAAAAFeljVuJ6BEYbg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFeljVuJ6BIPJw=", "_parent": {"$ref": "AAAAAAFeljVuJ6BEYbg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 0, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 696, "top": 40, "width": 154.37353515625, "height": 40, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFeljVuJ6BFLFo="}, "nameLabel": {"$ref": "AAAAAAFeljVuJ6BG31k="}, "namespaceLabel": {"$ref": "AAAAAAFeljVuJ6BHXBY="}, "propertyLabel": {"$ref": "AAAAAAFeljVuJ6BIPJw="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFeljVuJ6BJVMk=", "_parent": {"$ref": "AAAAAAFeljVuJ6BDakg="}, "model": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 773, "top": 80, "width": 1, "height": 609, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 696, "top": 40, "width": 154.37353515625, "height": 649, "autoResize": false, "stereotypeDisplay": "label", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFeljVuJ6BEYbg="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFeljVuJ6BJVMk="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljXMHaBjiRU=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljXMHKBic5o="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljXMHaBk5DA=", "_parent": {"$ref": "AAAAAAFeljXMHaBjiRU="}, "model": {"$ref": "AAAAAAFeljXMHKBic5o="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 285, "top": 96, "width": 127.92431640625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljXMHaBjiRU="}, "edgePosition": 1, "underline": false, "text": "1 : Upload logger data", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljXMHqBl7Gg=", "_parent": {"$ref": "AAAAAAFeljXMHaBjiRU="}, "model": {"$ref": "AAAAAAFeljXMHKBic5o="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 348, "top": 81, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljXMHaBjiRU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljXMHqBmqwc=", "_parent": {"$ref": "AAAAAAFeljXMHaBjiRU="}, "model": {"$ref": "AAAAAAFeljXMHKBic5o="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 348, "top": 116, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljXMHaBjiRU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljXMHqBnAJE=", "_parent": {"$ref": "AAAAAAFeljXMHaBjiRU="}, "model": {"$ref": "AAAAAAFeljXMHKBic5o="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 112, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVDqaApQpw="}, "tail": {"$ref": "AAAAAAFeljTi9aAHbIc="}, "lineStyle": 0, "points": "220:112;477:112", "nameLabel": {"$ref": "AAAAAAFeljXMHaBk5DA="}, "stereotypeLabel": {"$ref": "AAAAAAFeljXMHqBl7Gg="}, "propertyLabel": {"$ref": "AAAAAAFeljXMHqBmqwc="}, "activation": {"$ref": "AAAAAAFeljXMHqBnAJE="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljYDH6B5YD8=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljYDHqB46ds="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljYDH6B6MhM=", "_parent": {"$ref": "AAAAAAFeljYDH6B5YD8="}, "model": {"$ref": "AAAAAAFeljYDHqB46ds="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 464, "top": 139, "width": 131, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljYDH6B5YD8="}, "edgePosition": 1, "underline": false, "text": "2 : It is an Elpro logger", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljYDH6B7WQk=", "_parent": {"$ref": "AAAAAAFeljYDH6B5YD8="}, "model": {"$ref": "AAAAAAFeljYDHqB46ds="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 139, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljYDH6B5YD8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljYDIKB8SV8=", "_parent": {"$ref": "AAAAAAFeljYDH6B5YD8="}, "model": {"$ref": "AAAAAAFeljYDHqB46ds="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 510, "top": 140, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljYDH6B5YD8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljYDIKB9PtI=", "_parent": {"$ref": "AAAAAAFeljYDH6B5YD8="}, "model": {"$ref": "AAAAAAFeljYDHqB46ds="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 156, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVDqaApQpw="}, "tail": {"$ref": "AAAAAAFeljVDqaApQpw="}, "lineStyle": 0, "points": "490:136;520:136;520:156;490:156", "nameLabel": {"$ref": "AAAAAAFeljYDH6B6MhM="}, "stereotypeLabel": {"$ref": "AAAAAAFeljYDH6B7WQk="}, "propertyLabel": {"$ref": "AAAAAAFeljYDIKB8SV8="}, "activation": {"$ref": "AAAAAAFeljYDIKB9PtI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljZEqaCP5wc=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljZEqaCOitw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljZEqqCQoV4=", "_parent": {"$ref": "AAAAAAFeljZEqaCP5wc="}, "model": {"$ref": "AAAAAAFeljZEqaCOitw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 559, "top": 168, "width": 133.68798828125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljZEqaCP5wc="}, "edgePosition": 1, "underline": false, "text": "3 : Send file for parsing", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljZEqqCRdwg=", "_parent": {"$ref": "AAAAAAFeljZEqaCP5wc="}, "model": {"$ref": "AAAAAAFeljZEqaCOitw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 625, "top": 153, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljZEqaCP5wc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljZEqqCSfwY=", "_parent": {"$ref": "AAAAAAFeljZEqaCP5wc="}, "model": {"$ref": "AAAAAAFeljZEqaCOitw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 625, "top": 188, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljZEqaCP5wc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljZEqqCT7HY=", "_parent": {"$ref": "AAAAAAFeljZEqaCP5wc="}, "model": {"$ref": "AAAAAAFeljZEqaCOitw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 766, "top": 184, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVuJ6BJVMk="}, "tail": {"$ref": "AAAAAAFeljVDqaApQpw="}, "lineStyle": 0, "points": "484:184;766:184", "nameLabel": {"$ref": "AAAAAAFeljZEqqCQoV4="}, "stereotypeLabel": {"$ref": "AAAAAAFeljZEqqCRdwg="}, "propertyLabel": {"$ref": "AAAAAAFeljZEqqCSfwY="}, "activation": {"$ref": "AAAAAAFeljZEqqCT7HY="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljcRPqC82wk=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljcRPqC7njk="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljcRPqC9qYY=", "_parent": {"$ref": "AAAAAAFeljcRPqC82wk="}, "model": {"$ref": "AAAAAAFeljcRPqC7njk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 759, "top": 227, "width": 106.9580078125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljcRPqC82wk="}, "edgePosition": 1, "underline": false, "text": "4 : parse and save", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljcRPqC+otg=", "_parent": {"$ref": "AAAAAAFeljcRPqC82wk="}, "model": {"$ref": "AAAAAAFeljcRPqC7njk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 827, "top": 227, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljcRPqC82wk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljcRPqC/yxw=", "_parent": {"$ref": "AAAAAAFeljcRPqC82wk="}, "model": {"$ref": "AAAAAAFeljcRPqC7njk="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 793, "top": 228, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljcRPqC82wk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljcRPqDA5wM=", "_parent": {"$ref": "AAAAAAFeljcRPqC82wk="}, "model": {"$ref": "AAAAAAFeljcRPqC7njk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 766, "top": 244, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVuJ6BJVMk="}, "tail": {"$ref": "AAAAAAFeljVuJ6BJVMk="}, "lineStyle": 0, "points": "773:224;803:224;803:244;779:244", "nameLabel": {"$ref": "AAAAAAFeljcRPqC9qYY="}, "stereotypeLabel": {"$ref": "AAAAAAFeljcRPqC+otg="}, "propertyLabel": {"$ref": "AAAAAAFeljcRPqC/yxw="}, "activation": {"$ref": "AAAAAAFeljcRPqDA5wM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFelja4a6ClpNE=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFelja4a6Ck5Ps="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFelja4bKCmqgk=", "_parent": {"$ref": "AAAAAAFelja4a6ClpNE="}, "model": {"$ref": "AAAAAAFelja4a6Ck5Ps="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 576, "top": 280, "width": 98.2744140625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelja4a6ClpNE="}, "edgePosition": 1, "underline": false, "text": "5 : is file parsed?", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelja4bKCnGl0=", "_parent": {"$ref": "AAAAAAFelja4a6ClpNE="}, "model": {"$ref": "AAAAAAFelja4a6Ck5Ps="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 625, "top": 265, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFelja4a6ClpNE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFelja4baCo96Y=", "_parent": {"$ref": "AAAAAAFelja4a6ClpNE="}, "model": {"$ref": "AAAAAAFelja4a6Ck5Ps="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 625, "top": 300, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFelja4a6ClpNE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFelja4baCp5z8=", "_parent": {"$ref": "AAAAAAFelja4a6ClpNE="}, "model": {"$ref": "AAAAAAFelja4a6Ck5Ps="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 766, "top": 296, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVuJ6BJVMk="}, "tail": {"$ref": "AAAAAAFeljVDqaApQpw="}, "lineStyle": 0, "points": "484:296;766:296", "nameLabel": {"$ref": "AAAAAAFelja4bKCmqgk="}, "stereotypeLabel": {"$ref": "AAAAAAFelja4bKCnGl0="}, "propertyLabel": {"$ref": "AAAAAAFelja4baCo96Y="}, "activation": {"$ref": "AAAAAAFelja4baCp5z8="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljdksaDSYHs=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljdksaDR2Bs="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljdksaDT59w=", "_parent": {"$ref": "AAAAAAFeljdksaDSYHs="}, "model": {"$ref": "AAAAAAFeljdksaDR2Bs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 591, "top": 340, "width": 78.04443359375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljdksaDSYHs="}, "edgePosition": 1, "underline": false, "text": "6 : no", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljdksaDU+UI=", "_parent": {"$ref": "AAAAAAFeljdksaDSYHs="}, "model": {"$ref": "AAAAAAFeljdksaDR2Bs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 630, "top": 355, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljdksaDSYHs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljdksaDVAKc=", "_parent": {"$ref": "AAAAAAFeljdksaDSYHs="}, "model": {"$ref": "AAAAAAFeljdksaDR2Bs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 631, "top": 320, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljdksaDSYHs="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljdksaDW1cc=", "_parent": {"$ref": "AAAAAAFeljdksaDSYHs="}, "model": {"$ref": "AAAAAAFeljdksaDR2Bs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 336, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVDqaApQpw="}, "tail": {"$ref": "AAAAAAFeljVuJ6BJVMk="}, "lineStyle": 0, "points": "773:336;490:336", "nameLabel": {"$ref": "AAAAAAFeljdksaDT59w="}, "stereotypeLabel": {"$ref": "AAAAAAFeljdksaDU+UI="}, "propertyLabel": {"$ref": "AAAAAAFeljdksaDVAKc="}, "activation": {"$ref": "AAAAAAFeljdksaDW1cc="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljeKAaDoBG0=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljeKAaDniTI="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljeKAaDpDo4=", "_parent": {"$ref": "AAAAAAFeljeKAaDoBG0="}, "model": {"$ref": "AAAAAAFeljeKAaDniTI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 576, "top": 360, "width": 98.2744140625, "height": 13, "autoResize": false, "alpha": 1.570795771239341, "distance": 18, "hostEdge": {"$ref": "AAAAAAFeljeKAaDoBG0="}, "edgePosition": 1, "underline": false, "text": "7 : is file parsed?", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljeKAaDq5wo=", "_parent": {"$ref": "AAAAAAFeljeKAaDoBG0="}, "model": {"$ref": "AAAAAAFeljeKAaDniTI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 625, "top": 353, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljeKAaDoBG0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljeKAaDrVNg=", "_parent": {"$ref": "AAAAAAFeljeKAaDoBG0="}, "model": {"$ref": "AAAAAAFeljeKAaDniTI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 625, "top": 388, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljeKAaDoBG0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljeKAaDsTkM=", "_parent": {"$ref": "AAAAAAFeljeKAaDoBG0="}, "model": {"$ref": "AAAAAAFeljeKAaDniTI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 766, "top": 384, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVuJ6BJVMk="}, "tail": {"$ref": "AAAAAAFeljVDqaApQpw="}, "lineStyle": 0, "points": "484:384;766:384", "nameLabel": {"$ref": "AAAAAAFeljeKAaDpDo4="}, "stereotypeLabel": {"$ref": "AAAAAAFeljeKAaDq5wo="}, "propertyLabel": {"$ref": "AAAAAAFeljeKAaDrVNg="}, "activation": {"$ref": "AAAAAAFeljeKAaDsTkM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljfmKaEASwk=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljfmKaD/Uzw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljfmKaEBBJ4=", "_parent": {"$ref": "AAAAAAFeljfmKaEASwk="}, "model": {"$ref": "AAAAAAFeljfmKaD/Uzw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 580, "top": 420, "width": 101.1689453125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljfmKaEASwk="}, "edgePosition": 1, "underline": false, "text": "8 : yes, sending it", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljfmKaECpso=", "_parent": {"$ref": "AAAAAAFeljfmKaEASwk="}, "model": {"$ref": "AAAAAAFeljfmKaD/Uzw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 630, "top": 435, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljfmKaEASwk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljfmKaEDOdM=", "_parent": {"$ref": "AAAAAAFeljfmKaEASwk="}, "model": {"$ref": "AAAAAAFeljfmKaD/Uzw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 631, "top": 400, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljfmKaEASwk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljfmKqEEnHQ=", "_parent": {"$ref": "AAAAAAFeljfmKaEASwk="}, "model": {"$ref": "AAAAAAFeljfmKaD/Uzw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 416, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVDqaApQpw="}, "tail": {"$ref": "AAAAAAFeljVuJ6BJVMk="}, "lineStyle": 0, "points": "773:416;490:416", "nameLabel": {"$ref": "AAAAAAFeljfmKaEBBJ4="}, "stereotypeLabel": {"$ref": "AAAAAAFeljfmKaECpso="}, "propertyLabel": {"$ref": "AAAAAAFeljfmKaEDOdM="}, "activation": {"$ref": "AAAAAAFeljfmKqEEnHQ="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljg6cqEXFqQ=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljg6cqEWoCE="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljg6cqEYPMo=", "_parent": {"$ref": "AAAAAAFeljg6cqEXFqQ="}, "model": {"$ref": "AAAAAAFeljg6cqEWoCE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 453, "top": 467, "width": 140.1689453125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljg6cqEXFqQ="}, "edgePosition": 1, "underline": false, "text": "9 : Parse new format file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljg6c6EZFNo=", "_parent": {"$ref": "AAAAAAFeljg6cqEXFqQ="}, "model": {"$ref": "AAAAAAFeljg6cqEWoCE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 538, "top": 467, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljg6cqEXFqQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljg6c6EabY0=", "_parent": {"$ref": "AAAAAAFeljg6cqEXFqQ="}, "model": {"$ref": "AAAAAAFeljg6cqEWoCE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 504, "top": 468, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljg6cqEXFqQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljg6c6Eboz0=", "_parent": {"$ref": "AAAAAAFeljg6cqEXFqQ="}, "model": {"$ref": "AAAAAAFeljg6cqEWoCE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 484, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVDqaApQpw="}, "tail": {"$ref": "AAAAAAFeljVDqaApQpw="}, "lineStyle": 0, "points": "484:464;514:464;514:484;490:484", "nameLabel": {"$ref": "AAAAAAFeljg6cqEYPMo="}, "stereotypeLabel": {"$ref": "AAAAAAFeljg6c6EZFNo="}, "propertyLabel": {"$ref": "AAAAAAFeljg6c6EabY0="}, "activation": {"$ref": "AAAAAAFeljg6c6Eboz0="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljjJlKEvWNA=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljjJlKEu1pM="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljjJlKEwkqQ=", "_parent": {"$ref": "AAAAAAFeljjJlKEvWNA="}, "model": {"$ref": "AAAAAAFeljjJlKEu1pM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 425, "top": 523, "width": 197.27880859375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljjJlKEvWNA="}, "edgePosition": 1, "underline": false, "text": "10 : Store data trigger calculations", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljjJlKExJOc=", "_parent": {"$ref": "AAAAAAFeljjJlKEvWNA="}, "model": {"$ref": "AAAAAAFeljjJlKEu1pM="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 538, "top": 523, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljjJlKEvWNA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljjJlKEyNWo=", "_parent": {"$ref": "AAAAAAFeljjJlKEvWNA="}, "model": {"$ref": "AAAAAAFeljjJlKEu1pM="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 504, "top": 524, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljjJlKEvWNA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljjJlKEzrXQ=", "_parent": {"$ref": "AAAAAAFeljjJlKEvWNA="}, "model": {"$ref": "AAAAAAFeljjJlKEu1pM="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 477, "top": 540, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljVDqaApQpw="}, "tail": {"$ref": "AAAAAAFeljVDqaApQpw="}, "lineStyle": 0, "points": "484:520;514:520;514:540;490:540", "nameLabel": {"$ref": "AAAAAAFeljjJlKEwkqQ="}, "stereotypeLabel": {"$ref": "AAAAAAFeljjJlKExJOc="}, "propertyLabel": {"$ref": "AAAAAAFeljjJlKEyNWo="}, "activation": {"$ref": "AAAAAAFeljjJlKEzrXQ="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFeljkBn6FFuiQ=", "_parent": {"$ref": "AAAAAAFeljPqap+1aTo="}, "model": {"$ref": "AAAAAAFeljkBn6FEoZw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFeljkBoKFGsLM=", "_parent": {"$ref": "AAAAAAFeljkBn6FFuiQ="}, "model": {"$ref": "AAAAAAFeljkBn6FEoZw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 309, "top": 593, "width": 91.04443359375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljkBn6FFuiQ="}, "edgePosition": 1, "underline": false, "text": "11 : show result", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljkBoKFHzLk=", "_parent": {"$ref": "AAAAAAFeljkBn6FFuiQ="}, "model": {"$ref": "AAAAAAFeljkBn6FEoZw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 608, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFeljkBn6FFuiQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFeljkBoKFIEl0=", "_parent": {"$ref": "AAAAAAFeljkBn6FFuiQ="}, "model": {"$ref": "AAAAAAFeljkBn6FEoZw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 355, "top": 573, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFeljkBn6FFuiQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFeljkBoKFJ+VI=", "_parent": {"$ref": "AAAAAAFeljkBn6FFuiQ="}, "model": {"$ref": "AAAAAAFeljkBn6FEoZw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 213, "top": 589, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFeljTi9aAHbIc="}, "tail": {"$ref": "AAAAAAFeljVDqaApQpw="}, "lineStyle": 0, "points": "484:589;226:589", "nameLabel": {"$ref": "AAAAAAFeljkBoKFGsLM="}, "stereotypeLabel": {"$ref": "AAAAAAFeljkBoKFHzLk="}, "propertyLabel": {"$ref": "AAAAAAFeljkBoKFIEl0="}, "activation": {"$ref": "AAAAAAFeljkBoKFJ+VI="}, "showProperty": true, "showType": true}], "showSequenceNumber": true, "showSignature": true, "showActivation": true}], "visibility": "public", "isReentrant": true, "messages": [{"_type": "UMLMessage", "_id": "AAAAAAFeljXMHKBic5o=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "Upload logger data", "source": {"$ref": "AAAAAAFeljTi9KAA97Q="}, "target": {"$ref": "AAAAAAFeljVDqKAimr0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljYDHqB46ds=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "It is an Elpro logger", "source": {"$ref": "AAAAAAFeljVDqKAimr0="}, "target": {"$ref": "AAAAAAFeljVDqKAimr0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljZEqaCOitw=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "Send file for parsing", "source": {"$ref": "AAAAAAFeljVDqKAimr0="}, "target": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljcRPqC7njk=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "parse and save", "source": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "target": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFelja4a6Ck5Ps=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "is file parsed?", "source": {"$ref": "AAAAAAFeljVDqKAimr0="}, "target": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljdksaDR2Bs=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "no", "source": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "target": {"$ref": "AAAAAAFeljVDqKAimr0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljeKAaDniTI=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "is file parsed?", "source": {"$ref": "AAAAAAFeljVDqKAimr0="}, "target": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljfmKaD/Uzw=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "yes, sending it", "source": {"$ref": "AAAAAAFeljVuJ6BCgrA="}, "target": {"$ref": "AAAAAAFeljVDqKAimr0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljg6cqEWoCE=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "Parse new format file", "source": {"$ref": "AAAAAAFeljVDqKAimr0="}, "target": {"$ref": "AAAAAAFeljVDqKAimr0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljjJlKEu1pM=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "Store data trigger calculations", "source": {"$ref": "AAAAAAFeljVDqKAimr0="}, "target": {"$ref": "AAAAAAFeljVDqKAimr0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFeljkBn6FEoZw=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "show result", "source": {"$ref": "AAAAAAFeljVDqKAimr0="}, "target": {"$ref": "AAAAAAFeljTi9KAA97Q="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}], "participants": [{"_type": "UMLLifeline", "_id": "AAAAAAFeljTi9KAA97Q=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "User", "visibility": "public", "represent": {"$ref": "AAAAAAFeljTi85//XVU="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeljVDqKAimr0=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "CCIS", "visibility": "public", "represent": {"$ref": "AAAAAAFeljVDqKAhKXM="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFeljVuJ6BCgrA=", "_parent": {"$ref": "AAAAAAFeljPqap+0VBs="}, "name": "Elpro windows server", "visibility": "public", "represent": {"$ref": "AAAAAAFeljVuJqBBTXM="}, "isMultiInstance": false}]}], "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAFeljRd0p/GOT0=", "_parent": {"$ref": "AAAAAAFeljPqap+zMjM="}, "name": "Role1", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeljTi85//XVU=", "_parent": {"$ref": "AAAAAAFeljPqap+zMjM="}, "name": "Role2", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeljVDqKAhKXM=", "_parent": {"$ref": "AAAAAAFeljPqap+zMjM="}, "name": "Role3", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFeljVuJqBBTXM=", "_parent": {"$ref": "AAAAAAFeljPqap+zMjM="}, "name": "Role4", "visibility": "public", "isStatic": false, "isLeaf": false, "type": "", "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}], "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}, {"_type": "UMLUseCase", "_id": "AAAAAAFfcc8dGqYprR8=", "_parent": {"$ref": "AAAAAAFF+h6SjaM2Hec="}, "name": "ElproUpload", "ownedElements": [{"_type": "UMLInteraction", "_id": "AAAAAAFfcdB+/aY4Wuc=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "Interaction1", "ownedElements": [{"_type": "UMLSequenceDiagram", "_id": "AAAAAAFfcdB+/aY5maQ=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Elpro Upload Single File", "visible": true, "defaultDiagram": false, "ownedViews": [{"_type": "UMLFrameView", "_id": "AAAAAAFfcdB+/qY6zHw=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFfcdB+/qY7CdI=", "_parent": {"$ref": "AAAAAAFfcdB+/qY6zHw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 78.97900390625, "top": 13, "width": 140.46435546875, "height": 13, "autoResize": false, "underline": false, "text": "Elpro Upload Single File", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdB+/qY8L5Y=", "_parent": {"$ref": "AAAAAAFfcdB+/qY6zHw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 13, "top": 13, "width": 60.97900390625, "height": 13, "autoResize": false, "underline": false, "text": "interaction", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 8, "top": 8, "width": 1284, "height": 1081, "autoResize": false, "nameLabel": {"$ref": "AAAAAAFfcdB+/qY7CdI="}, "frameTypeLabel": {"$ref": "AAAAAAFfcdB+/qY8L5Y="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFfcdFstqZMiAE=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFfcdFstqZN4vU=", "_parent": {"$ref": "AAAAAAFfcdFstqZMiAE="}, "model": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFfcdFstqZOF8A=", "_parent": {"$ref": "AAAAAAFfcdFstqZN4vU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -198, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdFst6ZPfwg=", "_parent": {"$ref": "AAAAAAFfcdFstqZN4vU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 29, "top": 87, "width": 89.58447265625, "height": 13, "autoResize": false, "underline": false, "text": "Line: User", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdFst6ZQYKc=", "_parent": {"$ref": "AAAAAAFfcdFstqZN4vU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -198, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdFst6ZRE7I=", "_parent": {"$ref": "AAAAAAFfcdFstqZN4vU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -198, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 24, "top": 80, "width": 99.58447265625, "height": 25, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFfcdFstqZOF8A="}, "nameLabel": {"$ref": "AAAAAAFfcdFst6ZPfwg="}, "namespaceLabel": {"$ref": "AAAAAAFfcdFst6ZQYKc="}, "propertyLabel": {"$ref": "AAAAAAFfcdFst6ZRE7I="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFfcdFst6ZS72U=", "_parent": {"$ref": "AAAAAAFfcdFstqZMiAE="}, "model": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 74, "top": 105, "width": 1, "height": 968, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 24, "top": 40, "width": 99.58447265625, "height": 1033, "autoResize": false, "stereotypeDisplay": "icon", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFfcdFstqZN4vU="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFfcdFst6ZS72U="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFfcdLhF6Z2zEo=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFfcdLhGKZ3Cj8=", "_parent": {"$ref": "AAAAAAFfcdLhF6Z2zEo="}, "model": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFfcdLhGKZ4k9U=", "_parent": {"$ref": "AAAAAAFfcdLhGKZ3Cj8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -2, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdLhGaZ5GZA=", "_parent": {"$ref": "AAAAAAFfcdLhGKZ3Cj8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 253, "top": 87, "width": 215.25537109375, "height": 13, "autoResize": false, "underline": false, "text": "Line: LoggerReceiverEndpoint", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdLhGaZ6yMg=", "_parent": {"$ref": "AAAAAAFfcdLhGKZ3Cj8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -2, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdLhGaZ7kug=", "_parent": {"$ref": "AAAAAAFfcdLhGKZ3Cj8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -2, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 248, "top": 80, "width": 225.25537109375, "height": 25, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFfcdLhGKZ4k9U="}, "nameLabel": {"$ref": "AAAAAAFfcdLhGaZ5GZA="}, "namespaceLabel": {"$ref": "AAAAAAFfcdLhGaZ6yMg="}, "propertyLabel": {"$ref": "AAAAAAFfcdLhGaZ7kug="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFfcdLhGaZ8OeM=", "_parent": {"$ref": "AAAAAAFfcdLhF6Z2zEo="}, "model": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 361, "top": 105, "width": 1, "height": 968, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 248, "top": 40, "width": 225.25537109375, "height": 1033, "autoResize": false, "stereotypeDisplay": "icon", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFfcdLhGKZ3Cj8="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFfcdP9DaafQCw=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFfcdP9DaagpiU=", "_parent": {"$ref": "AAAAAAFfcdP9DaafQCw="}, "model": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFfcdP9DqahSOk=", "_parent": {"$ref": "AAAAAAFfcdP9DaagpiU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 238, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdP9DqaibZw=", "_parent": {"$ref": "AAAAAAFfcdP9DaagpiU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 629, "top": 87, "width": 198.64990234375, "height": 13, "autoResize": false, "underline": false, "text": "Line: Elpro Windows Server", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdP9Dqaj4HA=", "_parent": {"$ref": "AAAAAAFfcdP9DaagpiU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 238, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdP9DqakOP8=", "_parent": {"$ref": "AAAAAAFfcdP9DaagpiU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 238, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 624, "top": 80, "width": 208.64990234375, "height": 25, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFfcdP9DqahSOk="}, "nameLabel": {"$ref": "AAAAAAFfcdP9DqaibZw="}, "namespaceLabel": {"$ref": "AAAAAAFfcdP9Dqaj4HA="}, "propertyLabel": {"$ref": "AAAAAAFfcdP9DqakOP8="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFfcdP9DqalkDM=", "_parent": {"$ref": "AAAAAAFfcdP9DaafQCw="}, "model": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 728, "top": 105, "width": 1, "height": 968, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 624, "top": 40, "width": 208.64990234375, "height": 1033, "autoResize": false, "stereotypeDisplay": "icon", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFfcdP9DaagpiU="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFfcdP9DqalkDM="}}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFfcdSZqqbEqMs=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdSZqqbDzdo="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFfcdSZq6bFe1A=", "_parent": {"$ref": "AAAAAAFfcdSZqqbEqMs="}, "model": {"$ref": "AAAAAAFfcdSZqqbDzdo="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFfcdSZq6bGGGA=", "_parent": {"$ref": "AAAAAAFfcdSZq6bFe1A="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1430, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdSZq6bH/sI=", "_parent": {"$ref": "AAAAAAFfcdSZq6bFe1A="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1077, "top": 87, "width": 176.28076171875, "height": 13, "autoResize": false, "underline": false, "text": "Line: FileBackupSystem", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdSZq6bIXE0=", "_parent": {"$ref": "AAAAAAFfcdSZq6bFe1A="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1430, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcdSZq6bJmzU=", "_parent": {"$ref": "AAAAAAFfcdSZq6bFe1A="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1430, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1072, "top": 80, "width": 186.28076171875, "height": 25, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFfcdSZq6bGGGA="}, "nameLabel": {"$ref": "AAAAAAFfcdSZq6bH/sI="}, "namespaceLabel": {"$ref": "AAAAAAFfcdSZq6bIXE0="}, "propertyLabel": {"$ref": "AAAAAAFfcdSZq6bJmzU="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFfcdSZq6bKm9E=", "_parent": {"$ref": "AAAAAAFfcdSZqqbEqMs="}, "model": {"$ref": "AAAAAAFfcdSZqqbDzdo="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1165, "top": 105, "width": 1, "height": 968, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1072, "top": 40, "width": 186.28076171875, "height": 1033, "autoResize": false, "stereotypeDisplay": "icon", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFfcdSZq6bFe1A="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFfcdSZq6bKm9E="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcdZkgabwReE=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdZkgKbvHqs="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcdZkgabx6c4=", "_parent": {"$ref": "AAAAAAFfcdZkgabwReE="}, "model": {"$ref": "AAAAAAFfcdZkgKbvHqs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 134, "top": 99, "width": 161.88427734375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdZkgabwReE="}, "edgePosition": 1, "underline": false, "text": "1 : Upload Elpro logger data", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdZkgabyzIk=", "_parent": {"$ref": "AAAAAAFfcdZkgabwReE="}, "model": {"$ref": "AAAAAAFfcdZkgKbvHqs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 214, "top": 84, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcdZkgabwReE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdZkgqbzwBk=", "_parent": {"$ref": "AAAAAAFfcdZkgabwReE="}, "model": {"$ref": "AAAAAAFfcdZkgKbvHqs="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 214, "top": 119, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdZkgabwReE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcdZkgqb08YA=", "_parent": {"$ref": "AAAAAAFfcdZkgabwReE="}, "model": {"$ref": "AAAAAAFfcdZkgKbvHqs="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 115, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "lineStyle": 0, "points": "74:115;354:115", "nameLabel": {"$ref": "AAAAAAFfcdZkgabx6c4="}, "stereotypeLabel": {"$ref": "AAAAAAFfcdZkgabyzIk="}, "propertyLabel": {"$ref": "AAAAAAFfcdZkgqbzwBk="}, "activation": {"$ref": "AAAAAAFfcdZkgqb08YA="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcdfVRacRwk0=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdfVRacQ57A="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcdfVRacSOsY=", "_parent": {"$ref": "AAAAAAFfcdfVRacRwk0="}, "model": {"$ref": "AAAAAAFfcdfVRacQ57A="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 306, "top": 131, "width": 200, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdfVRacRwk0="}, "edgePosition": 1, "underline": false, "text": "2 : It is a pdf file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdfVRqcTZTM=", "_parent": {"$ref": "AAAAAAFfcdfVRacRwk0="}, "model": {"$ref": "AAAAAAFfcdfVRacQ57A="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 421, "top": 131, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcdfVRacRwk0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdfVRqcUjbY=", "_parent": {"$ref": "AAAAAAFfcdfVRacRwk0="}, "model": {"$ref": "AAAAAAFfcdfVRacQ57A="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 387, "top": 132, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdfVRacRwk0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcdfVRqcVSC0=", "_parent": {"$ref": "AAAAAAFfcdfVRacRwk0="}, "model": {"$ref": "AAAAAAFfcdfVRacQ57A="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 148, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "367:128;397:128;397:148;367:148", "nameLabel": {"$ref": "AAAAAAFfcdfVRacSOsY="}, "stereotypeLabel": {"$ref": "AAAAAAFfcdfVRqcTZTM="}, "propertyLabel": {"$ref": "AAAAAAFfcdfVRqcUjbY="}, "activation": {"$ref": "AAAAAAFfcdfVRqcVSC0="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcdhpPKcrQKA=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdhpPKcqLsg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcdhpPacs6as=", "_parent": {"$ref": "AAAAAAFfcdhpPKcrQKA="}, "model": {"$ref": "AAAAAAFfcdhpPKcqLsg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 719, "top": 165, "width": 81.65625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdhpPKcrQKA="}, "edgePosition": 1, "underline": false, "text": "3 : Backup file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdhpPactCM8=", "_parent": {"$ref": "AAAAAAFfcdhpPKcrQKA="}, "model": {"$ref": "AAAAAAFfcdhpPKcqLsg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 759, "top": 150, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcdhpPKcrQKA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdhpPacuAJY=", "_parent": {"$ref": "AAAAAAFfcdhpPKcrQKA="}, "model": {"$ref": "AAAAAAFfcdhpPKcqLsg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 759, "top": 185, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdhpPKcrQKA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcdhpPacv1Us=", "_parent": {"$ref": "AAAAAAFfcdhpPKcrQKA="}, "model": {"$ref": "AAAAAAFfcdhpPKcqLsg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 1158, "top": 181, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdSZq6bKm9E="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:181;1158:181", "nameLabel": {"$ref": "AAAAAAFfcdhpPacs6as="}, "stereotypeLabel": {"$ref": "AAAAAAFfcdhpPactCM8="}, "propertyLabel": {"$ref": "AAAAAAFfcdhpPacuAJY="}, "activation": {"$ref": "AAAAAAFfcdhpPacv1Us="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcdiyT6dBTtE=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcdiyT6dAWqw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcdiyUKdC7Xk=", "_parent": {"$ref": "AAAAAAFfcdiyT6dBTtE="}, "model": {"$ref": "AAAAAAFfcdiyT6dAWqw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 715, "top": 212, "width": 93.9580078125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdiyT6dBTtE="}, "edgePosition": 1, "underline": false, "text": "4 : Backup done", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdiyUKdDn9I=", "_parent": {"$ref": "AAAAAAFfcdiyT6dBTtE="}, "model": {"$ref": "AAAAAAFfcdiyT6dAWqw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 761, "top": 227, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcdiyT6dBTtE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcdiyUKdEDsg=", "_parent": {"$ref": "AAAAAAFfcdiyT6dBTtE="}, "model": {"$ref": "AAAAAAFfcdiyT6dAWqw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 762, "top": 192, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcdiyT6dBTtE="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcdiyUKdFPlg=", "_parent": {"$ref": "AAAAAAFfcdiyT6dBTtE="}, "model": {"$ref": "AAAAAAFfcdiyT6dAWqw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 208, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdSZq6bKm9E="}, "lineStyle": 0, "points": "1158:208;367:208", "nameLabel": {"$ref": "AAAAAAFfcdiyUKdC7Xk="}, "stereotypeLabel": {"$ref": "AAAAAAFfcdiyUKdDn9I="}, "propertyLabel": {"$ref": "AAAAAAFfcdiyUKdEDsg="}, "activation": {"$ref": "AAAAAAFfcdiyUKdFPlg="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcd8/DqeSEBM=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcd8/DqeR7Xc="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcd8/D6eTnmk=", "_parent": {"$ref": "AAAAAAFfcd8/DqeSEBM="}, "model": {"$ref": "AAAAAAFfcd8/DqeR7Xc="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 309, "top": 235, "width": 195.09521484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcd8/DqeSEBM="}, "edgePosition": 1, "underline": false, "text": "5 : Update status - Backup written", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcd8/D6eUKUQ=", "_parent": {"$ref": "AAAAAAFfcd8/DqeSEBM="}, "model": {"$ref": "AAAAAAFfcd8/DqeR7Xc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 421, "top": 235, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcd8/DqeSEBM="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcd8/D6eVqu8=", "_parent": {"$ref": "AAAAAAFfcd8/DqeSEBM="}, "model": {"$ref": "AAAAAAFfcd8/DqeR7Xc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 387, "top": 236, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcd8/DqeSEBM="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcd8/D6eWGRA=", "_parent": {"$ref": "AAAAAAFfcd8/DqeSEBM="}, "model": {"$ref": "AAAAAAFfcd8/DqeR7Xc="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 252, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "367:232;397:232;397:252;367:252", "nameLabel": {"$ref": "AAAAAAFfcd8/D6eTnmk="}, "stereotypeLabel": {"$ref": "AAAAAAFfcd8/D6eUKUQ="}, "propertyLabel": {"$ref": "AAAAAAFfcd8/D6eVqu8="}, "activation": {"$ref": "AAAAAAFfcd8/D6eWGRA="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcd0Bgqd6qjk=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcd0Bgad561g="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcd0Bgqd7kDc=", "_parent": {"$ref": "AAAAAAFfcd0Bgqd6qjk="}, "model": {"$ref": "AAAAAAFfcd0Bgad561g="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 144, "top": 284, "width": 151.734375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcd0Bgqd6qjk="}, "edgePosition": 1, "underline": false, "text": "6 : <PERSON><PERSON> written", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcd0Bgqd8FU0=", "_parent": {"$ref": "AAAAAAFfcd0Bgqd6qjk="}, "model": {"$ref": "AAAAAAFfcd0Bgad561g="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 219, "top": 299, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcd0Bgqd6qjk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcd0Bgqd9lWM=", "_parent": {"$ref": "AAAAAAFfcd0Bgqd6qjk="}, "model": {"$ref": "AAAAAAFfcd0Bgad561g="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 220, "top": 264, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcd0Bgqd6qjk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcd0Bgqd+FXM=", "_parent": {"$ref": "AAAAAAFfcd0Bgqd6qjk="}, "model": {"$ref": "AAAAAAFfcd0Bgad561g="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 67, "top": 280, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:280;80:280", "nameLabel": {"$ref": "AAAAAAFfcd0Bgqd7kDc="}, "stereotypeLabel": {"$ref": "AAAAAAFfcd0Bgqd8FU0="}, "propertyLabel": {"$ref": "AAAAAAFfcd0Bgqd9lWM="}, "activation": {"$ref": "AAAAAAFfcd0Bgqd+FXM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfceDD+KewzwA=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfceDD+KevUeI="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfceDD+aexhdQ=", "_parent": {"$ref": "AAAAAAFfceDD+KewzwA="}, "model": {"$ref": "AAAAAAFfceDD+KevUeI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 443, "top": 272, "width": 196.5361328125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfceDD+KewzwA="}, "edgePosition": 1, "underline": false, "text": "7 : Send file for conversion(async)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfceDD+aeyuws=", "_parent": {"$ref": "AAAAAAFfceDD+KewzwA="}, "model": {"$ref": "AAAAAAFfceDD+KevUeI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 541, "top": 257, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfceDD+KewzwA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfceDD+aezEXs=", "_parent": {"$ref": "AAAAAAFfceDD+KewzwA="}, "model": {"$ref": "AAAAAAFfceDD+KevUeI="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 541, "top": 292, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfceDD+KewzwA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfceDD+ae08MI=", "_parent": {"$ref": "AAAAAAFfceDD+KewzwA="}, "model": {"$ref": "AAAAAAFfceDD+KevUeI="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 721, "top": 288, "width": 14, "height": 369, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:288;721:288", "nameLabel": {"$ref": "AAAAAAFfceDD+aexhdQ="}, "stereotypeLabel": {"$ref": "AAAAAAFfceDD+aeyuws="}, "propertyLabel": {"$ref": "AAAAAAFfceDD+aezEXs="}, "activation": {"$ref": "AAAAAAFfceDD+ae08MI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfceMb9KgJYEI=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfceMb9KgI834="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfceMb9KgKCuY=", "_parent": {"$ref": "AAAAAAFfceMb9KgJYEI="}, "model": {"$ref": "AAAAAAFfceMb9KgI834="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 278, "top": 299, "width": 244.22607421875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfceMb9KgJYEI="}, "edgePosition": 1, "underline": false, "text": "8 : Update status - File sent for conversion", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfceMb9KgLBf0=", "_parent": {"$ref": "AAAAAAFfceMb9KgJYEI="}, "model": {"$ref": "AAAAAAFfceMb9KgI834="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 415, "top": 299, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfceMb9KgJYEI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfceMb9KgMcuc=", "_parent": {"$ref": "AAAAAAFfceMb9KgJYEI="}, "model": {"$ref": "AAAAAAFfceMb9KgI834="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 381, "top": 300, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfceMb9KgJYEI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfceMb9agNnDI=", "_parent": {"$ref": "AAAAAAFfceMb9KgJYEI="}, "model": {"$ref": "AAAAAAFfceMb9KgI834="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 316, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:296;391:296;391:316;367:316", "nameLabel": {"$ref": "AAAAAAFfceMb9KgKCuY="}, "stereotypeLabel": {"$ref": "AAAAAAFfceMb9KgLBf0="}, "propertyLabel": {"$ref": "AAAAAAFfceMb9KgMcuc="}, "activation": {"$ref": "AAAAAAFfceMb9agNnDI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcefIwagt+lQ=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcefIwagsPgg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcefIwqguNx8=", "_parent": {"$ref": "AAAAAAFfcefIwagt+lQ="}, "model": {"$ref": "AAAAAAFfcefIwagsPgg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 465, "top": 354, "width": 159.68798828125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcefIwagt+lQ="}, "edgePosition": 1, "underline": false, "text": "9 : <PERSON><PERSON> converted file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcefIwqgvwYs=", "_parent": {"$ref": "AAAAAAFfcefIwagt+lQ="}, "model": {"$ref": "AAAAAAFfcefIwagsPgg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 339, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcefIwagt+lQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcefIwqgw8gk=", "_parent": {"$ref": "AAAAAAFfcefIwagt+lQ="}, "model": {"$ref": "AAAAAAFfcefIwagsPgg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 374, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcefIwagt+lQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcefIwqgxal8=", "_parent": {"$ref": "AAAAAAFfcefIwagt+lQ="}, "model": {"$ref": "AAAAAAFfcefIwagsPgg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 728, "top": 370, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:370;728:370", "nameLabel": {"$ref": "AAAAAAFfcefIwqguNx8="}, "stereotypeLabel": {"$ref": "AAAAAAFfcefIwqgvwYs="}, "propertyLabel": {"$ref": "AAAAAAFfcefIwqgw8gk="}, "activation": {"$ref": "AAAAAAFfcefIwqgxal8="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcegaM6hD4QA=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcegaM6hCe/w="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcegaM6hEY5E=", "_parent": {"$ref": "AAAAAAFfcegaM6hD4QA="}, "model": {"$ref": "AAAAAAFfcegaM6hCe/w="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 504, "top": 399, "width": 85.2744140625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcegaM6hD4QA="}, "edgePosition": 1, "underline": false, "text": "10 : Not ready", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcegaM6hFL60=", "_parent": {"$ref": "AAAAAAFfcegaM6hD4QA="}, "model": {"$ref": "AAAAAAFfcegaM6hCe/w="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 546, "top": 414, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcegaM6hD4QA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcegaNKhGJ1A=", "_parent": {"$ref": "AAAAAAFfcegaM6hD4QA="}, "model": {"$ref": "AAAAAAFfcegaM6hCe/w="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 547, "top": 379, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcegaM6hD4QA="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcegaNKhHIps=", "_parent": {"$ref": "AAAAAAFfcegaM6hD4QA="}, "model": {"$ref": "AAAAAAFfcegaM6hCe/w="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 395, "width": 14, "height": 107, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "lineStyle": 0, "points": "728:395;367:395", "nameLabel": {"$ref": "AAAAAAFfcegaM6hEY5E="}, "stereotypeLabel": {"$ref": "AAAAAAFfcegaM6hFL60="}, "propertyLabel": {"$ref": "AAAAAAFfcegaNKhGJ1A="}, "activation": {"$ref": "AAAAAAFfcegaNKhHIps="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfRjCaniMD8=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfRjCanhnmU="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfRjCqnj7Qk=", "_parent": {"$ref": "AAAAAAFfcfRjCaniMD8="}, "model": {"$ref": "AAAAAAFfcfRjCanhnmU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 167, "top": 424, "width": 100.43896484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfRjCaniMD8="}, "edgePosition": 1, "underline": false, "text": "11 : Check status", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfRjCqnkXNw=", "_parent": {"$ref": "AAAAAAFfcfRjCaniMD8="}, "model": {"$ref": "AAAAAAFfcfRjCanhnmU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 217, "top": 409, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfRjCaniMD8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfRjCqnldXM=", "_parent": {"$ref": "AAAAAAFfcfRjCaniMD8="}, "model": {"$ref": "AAAAAAFfcfRjCanhnmU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 217, "top": 444, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfRjCaniMD8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfRjCqnmf3c=", "_parent": {"$ref": "AAAAAAFfcfRjCaniMD8="}, "model": {"$ref": "AAAAAAFfcfRjCanhnmU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 361, "top": 440, "width": 14, "height": 59, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "lineStyle": 0, "points": "74:440;361:440", "nameLabel": {"$ref": "AAAAAAFfcfRjCqnj7Qk="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfRjCqnkXNw="}, "propertyLabel": {"$ref": "AAAAAAFfcfRjCqnldXM="}, "activation": {"$ref": "AAAAAAFfcfRjCqnmf3c="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfSfO6n6UgI=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfSfOqn5bn8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfSfO6n7v5E=", "_parent": {"$ref": "AAAAAAFfcfSfO6n6UgI="}, "model": {"$ref": "AAAAAAFfcfSfOqn5bn8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 140, "top": 500, "width": 159.68798828125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfSfO6n6UgI="}, "edgePosition": 1, "underline": false, "text": "12 : File sent for conversion", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfSfO6n8484=", "_parent": {"$ref": "AAAAAAFfcfSfO6n6UgI="}, "model": {"$ref": "AAAAAAFfcfSfOqn5bn8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 219, "top": 515, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfSfO6n6UgI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfSfO6n9qHc=", "_parent": {"$ref": "AAAAAAFfcfSfO6n6UgI="}, "model": {"$ref": "AAAAAAFfcfSfOqn5bn8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 220, "top": 480, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfSfO6n6UgI="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfSfPKn+FZs=", "_parent": {"$ref": "AAAAAAFfcfSfO6n6UgI="}, "model": {"$ref": "AAAAAAFfcfSfOqn5bn8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 67, "top": 496, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:496;80:496", "nameLabel": {"$ref": "AAAAAAFfcfSfO6n7v5E="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfSfO6n8484="}, "propertyLabel": {"$ref": "AAAAAAFfcfSfO6n9qHc="}, "activation": {"$ref": "AAAAAAFfcfSfPKn+FZs="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcehZg6hZ1X8=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcehZgqhYWx0="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcehZg6haN4Y=", "_parent": {"$ref": "AAAAAAFfcehZg6hZ1X8="}, "model": {"$ref": "AAAAAAFfcehZgqhYWx0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 461, "top": 520, "width": 166.91796875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcehZg6hZ1X8="}, "edgePosition": 1, "underline": false, "text": "13 : <PERSON><PERSON> converted file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcehZg6hbb00=", "_parent": {"$ref": "AAAAAAFfcehZg6hZ1X8="}, "model": {"$ref": "AAAAAAFfcehZgqhYWx0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 505, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcehZg6hZ1X8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcehZg6hclqg=", "_parent": {"$ref": "AAAAAAFfcehZg6hZ1X8="}, "model": {"$ref": "AAAAAAFfcehZgqhYWx0="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 540, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcehZg6hZ1X8="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcehZg6hdzLA=", "_parent": {"$ref": "AAAAAAFfcehZg6hZ1X8="}, "model": {"$ref": "AAAAAAFfcehZgqhYWx0="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 728, "top": 536, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:536;728:536", "nameLabel": {"$ref": "AAAAAAFfcehZg6haN4Y="}, "stereotypeLabel": {"$ref": "AAAAAAFfcehZg6hbb00="}, "propertyLabel": {"$ref": "AAAAAAFfcehZg6hclqg="}, "activation": {"$ref": "AAAAAAFfcehZg6hdzLA="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfceh/p6hvzbk=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfceh/p6huQEY="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfceh/p6hwVAQ=", "_parent": {"$ref": "AAAAAAFfceh/p6hvzbk="}, "model": {"$ref": "AAAAAAFfceh/p6huQEY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 504, "top": 567, "width": 85.2744140625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfceh/p6hvzbk="}, "edgePosition": 1, "underline": false, "text": "14 : Not ready", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfceh/qKhxKuA=", "_parent": {"$ref": "AAAAAAFfceh/p6hvzbk="}, "model": {"$ref": "AAAAAAFfceh/p6huQEY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 546, "top": 582, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfceh/p6hvzbk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfceh/qKhydk0=", "_parent": {"$ref": "AAAAAAFfceh/p6hvzbk="}, "model": {"$ref": "AAAAAAFfceh/p6huQEY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 547, "top": 547, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfceh/p6hvzbk="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfceh/qKhzOiE=", "_parent": {"$ref": "AAAAAAFfceh/p6hvzbk="}, "model": {"$ref": "AAAAAAFfceh/p6huQEY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 563, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "lineStyle": 0, "points": "728:563;367:563", "nameLabel": {"$ref": "AAAAAAFfceh/p6hwVAQ="}, "stereotypeLabel": {"$ref": "AAAAAAFfceh/qKhxKuA="}, "propertyLabel": {"$ref": "AAAAAAFfceh/qKhydk0="}, "activation": {"$ref": "AAAAAAFfceh/qKhzOiE="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfTkmKoQjrY=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfTkmKoPKag="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfTkmaoRmJM=", "_parent": {"$ref": "AAAAAAFfcfTkmKoQjrY="}, "model": {"$ref": "AAAAAAFfcfTkmKoPKag="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 164, "top": 618, "width": 100.43896484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfTkmKoQjrY="}, "edgePosition": 1, "underline": false, "text": "15 : Check status", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfTkmaoSvH0=", "_parent": {"$ref": "AAAAAAFfcfTkmKoQjrY="}, "model": {"$ref": "AAAAAAFfcfTkmKoPKag="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 214, "top": 603, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfTkmKoQjrY="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfTkmaoTQOc=", "_parent": {"$ref": "AAAAAAFfcfTkmKoQjrY="}, "model": {"$ref": "AAAAAAFfcfTkmKoPKag="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 214, "top": 638, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfTkmKoQjrY="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfTkmaoU3RU=", "_parent": {"$ref": "AAAAAAFfcfTkmKoQjrY="}, "model": {"$ref": "AAAAAAFfcfTkmKoPKag="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 634, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "lineStyle": 0, "points": "74:634;354:634", "nameLabel": {"$ref": "AAAAAAFfcfTkmaoRmJM="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfTkmaoSvH0="}, "propertyLabel": {"$ref": "AAAAAAFfcfTkmaoTQOc="}, "activation": {"$ref": "AAAAAAFfcfTkmaoU3RU="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcejXMaiFl6k=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcejXMaiE7/A="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcejXMaiGmO8=", "_parent": {"$ref": "AAAAAAFfcejXMaiFl6k="}, "model": {"$ref": "AAAAAAFfcejXMaiE7/A="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 712, "top": 651, "width": 123.58251953125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcejXMaiFl6k="}, "edgePosition": 1, "underline": false, "text": "16 : Conversion done", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcejXMqiHZdg=", "_parent": {"$ref": "AAAAAAFfcejXMaiFl6k="}, "model": {"$ref": "AAAAAAFfcejXMaiE7/A="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 788, "top": 651, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcejXMaiFl6k="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcejXMqiIqXw=", "_parent": {"$ref": "AAAAAAFfcejXMaiFl6k="}, "model": {"$ref": "AAAAAAFfcejXMaiE7/A="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 754, "top": 652, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcejXMaiFl6k="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcejXMqiJuGE=", "_parent": {"$ref": "AAAAAAFfcejXMaiFl6k="}, "model": {"$ref": "AAAAAAFfcejXMaiE7/A="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 721, "top": 668, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "tail": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "lineStyle": 0, "points": "734:648;764:648;764:668;734:668", "nameLabel": {"$ref": "AAAAAAFfcejXMaiGmO8="}, "stereotypeLabel": {"$ref": "AAAAAAFfcejXMqiHZdg="}, "propertyLabel": {"$ref": "AAAAAAFfcejXMqiIqXw="}, "activation": {"$ref": "AAAAAAFfcejXMqiJuGE="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqLifelineView", "_id": "AAAAAAFfcfDWL6j00Ao=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfDWL6jz/rU="}, "subViews": [{"_type": "UMLNameCompartmentView", "_id": "AAAAAAFfcfDWMKj14M4=", "_parent": {"$ref": "AAAAAAFfcfDWL6j00Ao="}, "model": {"$ref": "AAAAAAFfcfDWL6jz/rU="}, "subViews": [{"_type": "LabelView", "_id": "AAAAAAFfcfDWMKj2ig8=", "_parent": {"$ref": "AAAAAAFfcfDWMKj14M4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -108, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcfDWMKj3t10=", "_parent": {"$ref": "AAAAAAFfcfDWMKj14M4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;1", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 901, "top": 87, "width": 140.8671875, "height": 13, "autoResize": false, "underline": false, "text": "Lifeline1: Data Storage", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcfDWMKj4a7s=", "_parent": {"$ref": "AAAAAAFfcfDWMKj14M4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -108, "top": 0, "width": 106.20263671875, "height": 13, "autoResize": false, "underline": false, "text": "(from Interaction1)", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "LabelView", "_id": "AAAAAAFfcfDWMKj54fs=", "_parent": {"$ref": "AAAAAAFfcfDWMKj14M4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": -108, "top": 0, "width": 0, "height": 13, "autoResize": false, "underline": false, "horizontalAlignment": 1, "verticalAlignment": 5, "wordWrap": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 896, "top": 80, "width": 150.8671875, "height": 25, "autoResize": false, "stereotypeLabel": {"$ref": "AAAAAAFfcfDWMKj2ig8="}, "nameLabel": {"$ref": "AAAAAAFfcfDWMKj3t10="}, "namespaceLabel": {"$ref": "AAAAAAFfcfDWMKj4a7s="}, "propertyLabel": {"$ref": "AAAAAAFfcfDWMKj54fs="}}, {"_type": "UMLLinePartView", "_id": "AAAAAAFfcfDWMaj6WyQ=", "_parent": {"$ref": "AAAAAAFfcfDWL6j00Ao="}, "model": {"$ref": "AAAAAAFfcfDWL6jz/rU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 971, "top": 105, "width": 1, "height": 968, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 896, "top": 40, "width": 150.8671875, "height": 1033, "autoResize": false, "stereotypeDisplay": "icon", "showVisibility": true, "showNamespace": false, "showProperty": true, "showType": true, "nameCompartment": {"$ref": "AAAAAAFfcfDWMKj14M4="}, "wordWrap": false, "linePart": {"$ref": "AAAAAAFfcfDWMaj6WyQ="}}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfUDLqombo0=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfUDLqolYMU="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfUDLqonGWg=", "_parent": {"$ref": "AAAAAAFfcfUDLqombo0="}, "model": {"$ref": "AAAAAAFfcfUDLqolYMU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 140, "top": 668, "width": 159.68798828125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfUDLqombo0="}, "edgePosition": 1, "underline": false, "text": "17 : File sent for conversion", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfUDL6oolzs=", "_parent": {"$ref": "AAAAAAFfcfUDLqombo0="}, "model": {"$ref": "AAAAAAFfcfUDLqolYMU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 219, "top": 683, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfUDLqombo0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfUDL6opsXA=", "_parent": {"$ref": "AAAAAAFfcfUDLqombo0="}, "model": {"$ref": "AAAAAAFfcfUDLqolYMU="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 220, "top": 648, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfUDLqombo0="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfUDL6oqdZo=", "_parent": {"$ref": "AAAAAAFfcfUDLqombo0="}, "model": {"$ref": "AAAAAAFfcfUDLqolYMU="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 67, "top": 664, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:664;80:664", "nameLabel": {"$ref": "AAAAAAFfcfUDLqonGWg="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfUDL6oolzs="}, "propertyLabel": {"$ref": "AAAAAAFfcfUDL6opsXA="}, "activation": {"$ref": "AAAAAAFfcfUDL6oqdZo="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcekZ+Kibqlc=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcekZ+KiaOpw="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcekZ+KicvE4=", "_parent": {"$ref": "AAAAAAFfcekZ+Kibqlc="}, "model": {"$ref": "AAAAAAFfcekZ+KiaOpw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 458, "top": 708, "width": 166.91796875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcekZ+Kibqlc="}, "edgePosition": 1, "underline": false, "text": "18 : <PERSON><PERSON> converted file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcekZ+KidKww=", "_parent": {"$ref": "AAAAAAFfcekZ+Kibqlc="}, "model": {"$ref": "AAAAAAFfcekZ+KiaOpw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 541, "top": 693, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcekZ+Kibqlc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcekZ+Kie2A0=", "_parent": {"$ref": "AAAAAAFfcekZ+Kibqlc="}, "model": {"$ref": "AAAAAAFfcekZ+KiaOpw="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 541, "top": 728, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcekZ+Kibqlc="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcekZ+Kifg+0=", "_parent": {"$ref": "AAAAAAFfcekZ+Kibqlc="}, "model": {"$ref": "AAAAAAFfcekZ+KiaOpw="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 721, "top": 724, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:724;721:724", "nameLabel": {"$ref": "AAAAAAFfcekZ+KicvE4="}, "stereotypeLabel": {"$ref": "AAAAAAFfcekZ+KidKww="}, "propertyLabel": {"$ref": "AAAAAAFfcekZ+Kie2A0="}, "activation": {"$ref": "AAAAAAFfcekZ+Kifg+0="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcelI8aixdFo=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcelI8aiwSRg="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcelI8qiyIHs=", "_parent": {"$ref": "AAAAAAFfcelI8aixdFo="}, "model": {"$ref": "AAAAAAFfcelI8aiwSRg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 465, "top": 749, "width": 157.5361328125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcelI8aixdFo="}, "edgePosition": 1, "underline": false, "text": "19 : Converted file returned", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcelI8qizjAw=", "_parent": {"$ref": "AAAAAAFfcelI8aixdFo="}, "model": {"$ref": "AAAAAAFfcelI8aiwSRg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 543, "top": 764, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcelI8aixdFo="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcelI8qi0bZo=", "_parent": {"$ref": "AAAAAAFfcelI8aixdFo="}, "model": {"$ref": "AAAAAAFfcelI8aiwSRg="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 544, "top": 729, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcelI8aixdFo="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcelI8qi1ZGY=", "_parent": {"$ref": "AAAAAAFfcelI8aixdFo="}, "model": {"$ref": "AAAAAAFfcelI8aiwSRg="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 745, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdP9DqalkDM="}, "lineStyle": 0, "points": "721:745;367:745", "nameLabel": {"$ref": "AAAAAAFfcelI8qiyIHs="}, "stereotypeLabel": {"$ref": "AAAAAAFfcelI8qizjAw="}, "propertyLabel": {"$ref": "AAAAAAFfcelI8qi0bZo="}, "activation": {"$ref": "AAAAAAFfcelI8qi1ZGY="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfce6DE6jKRmU=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfce6DE6jJm9U="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfce6DFKjL2rg=", "_parent": {"$ref": "AAAAAAFfce6DE6jKRmU="}, "model": {"$ref": "AAAAAAFfce6DE6jJm9U="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 289, "top": 771, "width": 235.56787109375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfce6DE6jKRmU="}, "edgePosition": 1, "underline": false, "text": "20 : Update status - Conversion received", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfce6DFKjM+1g=", "_parent": {"$ref": "AAAAAAFfce6DE6jKRmU="}, "model": {"$ref": "AAAAAAFfce6DE6jJm9U="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 421, "top": 771, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfce6DE6jKRmU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfce6DFKjN4VM=", "_parent": {"$ref": "AAAAAAFfce6DE6jKRmU="}, "model": {"$ref": "AAAAAAFfce6DE6jJm9U="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 387, "top": 772, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfce6DE6jKRmU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfce6DFKjOq4c=", "_parent": {"$ref": "AAAAAAFfce6DE6jKRmU="}, "model": {"$ref": "AAAAAAFfce6DE6jJm9U="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 788, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "367:768;397:768;397:788;367:788", "nameLabel": {"$ref": "AAAAAAFfce6DFKjL2rg="}, "stereotypeLabel": {"$ref": "AAAAAAFfce6DFKjM+1g="}, "propertyLabel": {"$ref": "AAAAAAFfce6DFKjN4VM="}, "activation": {"$ref": "AAAAAAFfce6DFKjOq4c="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfF+Yak0ek4=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfF+YakzfAE="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfF+Yak1HCE=", "_parent": {"$ref": "AAAAAAFfcfF+Yak0ek4="}, "model": {"$ref": "AAAAAAFfcfF+YakzfAE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 336, "top": 811, "width": 140.18798828125, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfF+Yak0ek4="}, "edgePosition": 1, "underline": false, "text": "21 : Parse converted file", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfF+Yak28Zs=", "_parent": {"$ref": "AAAAAAFfcfF+Yak0ek4="}, "model": {"$ref": "AAAAAAFfcfF+YakzfAE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 421, "top": 811, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfF+Yak0ek4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfF+Yqk3kJI=", "_parent": {"$ref": "AAAAAAFfcfF+Yak0ek4="}, "model": {"$ref": "AAAAAAFfcfF+YakzfAE="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 387, "top": 812, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfF+Yak0ek4="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfF+Yqk4iK8=", "_parent": {"$ref": "AAAAAAFfcfF+Yak0ek4="}, "model": {"$ref": "AAAAAAFfcfF+YakzfAE="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 828, "width": 14, "height": 36, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "367:808;397:808;397:828;367:828", "nameLabel": {"$ref": "AAAAAAFfcfF+Yak1HCE="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfF+Yak28Zs="}, "propertyLabel": {"$ref": "AAAAAAFfcfF+Yqk3kJI="}, "activation": {"$ref": "AAAAAAFfcfF+Yqk4iK8="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfU6yqo8VnQ=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfU6yqo7T3s="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfU6yqo9BB0=", "_parent": {"$ref": "AAAAAAFfcfU6yqo8VnQ="}, "model": {"$ref": "AAAAAAFfcfU6yqo7T3s="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 167, "top": 816, "width": 100.43896484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfU6yqo8VnQ="}, "edgePosition": 1, "underline": false, "text": "22 : Check status", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfU6y6o+shg=", "_parent": {"$ref": "AAAAAAFfcfU6yqo8VnQ="}, "model": {"$ref": "AAAAAAFfcfU6yqo7T3s="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 217, "top": 801, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfU6yqo8VnQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfU6y6o/JD0=", "_parent": {"$ref": "AAAAAAFfcfU6yqo8VnQ="}, "model": {"$ref": "AAAAAAFfcfU6yqo7T3s="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 217, "top": 836, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfU6yqo8VnQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfU6y6pA18U=", "_parent": {"$ref": "AAAAAAFfcfU6yqo8VnQ="}, "model": {"$ref": "AAAAAAFfcfU6yqo7T3s="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 361, "top": 832, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "lineStyle": 0, "points": "74:832;361:832", "nameLabel": {"$ref": "AAAAAAFfcfU6yqo9BB0="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfU6y6o+shg="}, "propertyLabel": {"$ref": "AAAAAAFfcfU6y6o/JD0="}, "activation": {"$ref": "AAAAAAFfcfU6y6pA18U="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfMI8KlrYgw=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfMI8KlqUPQ="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfMI8KlskR4=", "_parent": {"$ref": "AAAAAAFfcfMI8KlrYgw="}, "model": {"$ref": "AAAAAAFfcfMI8KlqUPQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 607, "top": 840, "width": 125.02978515625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfMI8KlrYgw="}, "edgePosition": 1, "underline": false, "text": "23 : Store logger data", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfMI8KltabQ=", "_parent": {"$ref": "AAAAAAFfcfMI8KlrYgw="}, "model": {"$ref": "AAAAAAFfcfMI8KlqUPQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 669, "top": 825, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfMI8KlrYgw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfMI8KluIao=", "_parent": {"$ref": "AAAAAAFfcfMI8KlrYgw="}, "model": {"$ref": "AAAAAAFfcfMI8KlqUPQ="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 669, "top": 860, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfMI8KlrYgw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfMI8KlvBOM=", "_parent": {"$ref": "AAAAAAFfcfMI8KlrYgw="}, "model": {"$ref": "AAAAAAFfcfMI8KlqUPQ="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 964, "top": 856, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcfDWMaj6WyQ="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "374:856;964:856", "nameLabel": {"$ref": "AAAAAAFfcfMI8KlskR4="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfMI8KltabQ="}, "propertyLabel": {"$ref": "AAAAAAFfcfMI8KluIao="}, "activation": {"$ref": "AAAAAAFfcfMI8KlvBOM="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfVdcKpSlvU=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfVdcKpRvf4="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfVdcKpTWs4=", "_parent": {"$ref": "AAAAAAFfcfVdcKpSlvU="}, "model": {"$ref": "AAAAAAFfcfVdcKpRvf4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 148, "top": 869, "width": 143.7998046875, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfVdcKpSlvU="}, "edgePosition": 1, "underline": false, "text": "24 : Conversion received", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfVdcapUtCU=", "_parent": {"$ref": "AAAAAAFfcfVdcKpSlvU="}, "model": {"$ref": "AAAAAAFfcfVdcKpRvf4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 219, "top": 884, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfVdcKpSlvU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfVdcapVnlY=", "_parent": {"$ref": "AAAAAAFfcfVdcKpSlvU="}, "model": {"$ref": "AAAAAAFfcfVdcKpRvf4="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 220, "top": 849, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfVdcKpSlvU="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfVdcapWCtA=", "_parent": {"$ref": "AAAAAAFfcfVdcKpSlvU="}, "model": {"$ref": "AAAAAAFfcfVdcKpRvf4="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 67, "top": 865, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:865;80:865", "nameLabel": {"$ref": "AAAAAAFfcfVdcKpTWs4="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfVdcapUtCU="}, "propertyLabel": {"$ref": "AAAAAAFfcfVdcapVnlY="}, "activation": {"$ref": "AAAAAAFfcfVdcapWCtA="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfMwOKmBwiw=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfMwOKmA5uc="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfMwOKmCBw0=", "_parent": {"$ref": "AAAAAAFfcfMwOKmBwiw="}, "model": {"$ref": "AAAAAAFfcfMwOKmA5uc="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 619, "top": 886, "width": 90.33984375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfMwOKmBwiw="}, "edgePosition": 1, "underline": false, "text": "25 : data stored", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfMwOKmDfRE=", "_parent": {"$ref": "AAAAAAFfcfMwOKmBwiw="}, "model": {"$ref": "AAAAAAFfcfMwOKmA5uc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 664, "top": 901, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfMwOKmBwiw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfMwOKmEHGg=", "_parent": {"$ref": "AAAAAAFfcfMwOKmBwiw="}, "model": {"$ref": "AAAAAAFfcfMwOKmA5uc="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 665, "top": 866, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfMwOKmBwiw="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfMwOKmFvkc=", "_parent": {"$ref": "AAAAAAFfcfMwOKmBwiw="}, "model": {"$ref": "AAAAAAFfcfMwOKmA5uc="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 882, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcfDWMaj6WyQ="}, "lineStyle": 0, "points": "964:882;367:882", "nameLabel": {"$ref": "AAAAAAFfcfMwOKmCBw0="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfMwOKmDfRE="}, "propertyLabel": {"$ref": "AAAAAAFfcfMwOKmEHGg="}, "activation": {"$ref": "AAAAAAFfcfMwOKmFvkc="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfOZeqm2h/s=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfOZeqm14TY="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfOZeqm3YeY=", "_parent": {"$ref": "AAAAAAFfcfOZeqm2h/s="}, "model": {"$ref": "AAAAAAFfcfOZeqm14TY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 335, "top": 907, "width": 143.0634765625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfOZeqm2h/s="}, "edgePosition": 1, "underline": false, "text": "26 : Perform calculations", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfOZeqm4CBA=", "_parent": {"$ref": "AAAAAAFfcfOZeqm2h/s="}, "model": {"$ref": "AAAAAAFfcfOZeqm14TY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 421, "top": 907, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfOZeqm2h/s="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfOZeqm5mVw=", "_parent": {"$ref": "AAAAAAFfcfOZeqm2h/s="}, "model": {"$ref": "AAAAAAFfcfOZeqm14TY="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 387, "top": 908, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfOZeqm2h/s="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfOZeqm6ohs=", "_parent": {"$ref": "AAAAAAFfcfOZeqm2h/s="}, "model": {"$ref": "AAAAAAFfcfOZeqm14TY="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 924, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "367:904;397:904;397:924;367:924", "nameLabel": {"$ref": "AAAAAAFfcfOZeqm3YeY="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfOZeqm4CBA="}, "propertyLabel": {"$ref": "AAAAAAFfcfOZeqm5mVw="}, "activation": {"$ref": "AAAAAAFfcfOZeqm6ohs="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfPAN6nMd+M=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfPANqnL2S8="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfPAN6nNnpE=", "_parent": {"$ref": "AAAAAAFfcfPAN6nMd+M="}, "model": {"$ref": "AAAAAAFfcfPANqnL2S8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 311, "top": 955, "width": 179.92431640625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfPAN6nMd+M="}, "edgePosition": 1, "underline": false, "text": "27 : Update status - Successful", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfPAN6nObV4=", "_parent": {"$ref": "AAAAAAFfcfPAN6nMd+M="}, "model": {"$ref": "AAAAAAFfcfPANqnL2S8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 415, "top": 955, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfPAN6nMd+M="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfPAN6nPrtQ=", "_parent": {"$ref": "AAAAAAFfcfPAN6nMd+M="}, "model": {"$ref": "AAAAAAFfcfPANqnL2S8="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 381, "top": 956, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfPAN6nMd+M="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfPAN6nQKNI=", "_parent": {"$ref": "AAAAAAFfcfPAN6nMd+M="}, "model": {"$ref": "AAAAAAFfcfPANqnL2S8="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 972, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "361:952;391:952;391:972;367:972", "nameLabel": {"$ref": "AAAAAAFfcfPAN6nNnpE="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfPAN6nObV4="}, "propertyLabel": {"$ref": "AAAAAAFfcfPAN6nPrtQ="}, "activation": {"$ref": "AAAAAAFfcfPAN6nQKNI="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfWOV6pou/s=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfWOV6pn67w="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfWOWKppGNU=", "_parent": {"$ref": "AAAAAAFfcfWOV6pou/s="}, "model": {"$ref": "AAAAAAFfcfWOV6pn67w="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 164, "top": 997, "width": 100.43896484375, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfWOV6pou/s="}, "edgePosition": 1, "underline": false, "text": "28 : Check status", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfWOWKpqsB8=", "_parent": {"$ref": "AAAAAAFfcfWOV6pou/s="}, "model": {"$ref": "AAAAAAFfcfWOV6pn67w="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 214, "top": 982, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfWOV6pou/s="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfWOWKprW6s=", "_parent": {"$ref": "AAAAAAFfcfWOV6pou/s="}, "model": {"$ref": "AAAAAAFfcfWOV6pn67w="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 214, "top": 1017, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfWOV6pou/s="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfWOWKpsWeY=", "_parent": {"$ref": "AAAAAAFfcfWOV6pou/s="}, "model": {"$ref": "AAAAAAFfcfWOV6pn67w="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 354, "top": 1013, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "tail": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "lineStyle": 0, "points": "74:1013;354:1013", "nameLabel": {"$ref": "AAAAAAFfcfWOWKppGNU="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfWOWKpqsB8="}, "propertyLabel": {"$ref": "AAAAAAFfcfWOWKprW6s="}, "activation": {"$ref": "AAAAAAFfcfWOWKpsWeY="}, "showProperty": true, "showType": true}, {"_type": "UMLSeqMessageView", "_id": "AAAAAAFfcfYDmKqDhaQ=", "_parent": {"$ref": "AAAAAAFfcdB+/aY5maQ="}, "model": {"$ref": "AAAAAAFfcfYDl6qCF7Y="}, "subViews": [{"_type": "EdgeLabelView", "_id": "AAAAAAFfcfYDmKqEJcA=", "_parent": {"$ref": "AAAAAAFfcfYDmKqDhaQ="}, "model": {"$ref": "AAAAAAFfcfYDl6qCF7Y="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 172, "top": 1042, "width": 88.15625, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfYDmKqDhaQ="}, "edgePosition": 1, "underline": false, "text": "29 : Successful", "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfYDmKqFswM=", "_parent": {"$ref": "AAAAAAFfcfYDmKqDhaQ="}, "model": {"$ref": "AAAAAAFfcfYDl6qCF7Y="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 216, "top": 1057, "width": 0, "height": 13, "autoResize": false, "alpha": 1.5707963267948966, "distance": 25, "hostEdge": {"$ref": "AAAAAAFfcfYDmKqDhaQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "EdgeLabelView", "_id": "AAAAAAFfcfYDmKqGs6k=", "_parent": {"$ref": "AAAAAAFfcfYDmKqDhaQ="}, "model": {"$ref": "AAAAAAFfcfYDl6qCF7Y="}, "visible": false, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 217, "top": 1022, "width": 0, "height": 13, "autoResize": false, "alpha": -1.5707963267948966, "distance": 10, "hostEdge": {"$ref": "AAAAAAFfcfYDmKqDhaQ="}, "edgePosition": 1, "underline": false, "horizontalAlignment": 2, "verticalAlignment": 5, "wordWrap": false}, {"_type": "UMLActivationView", "_id": "AAAAAAFfcfYDmKqHI/I=", "_parent": {"$ref": "AAAAAAFfcfYDmKqDhaQ="}, "model": {"$ref": "AAAAAAFfcfYDl6qCF7Y="}, "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "left": 67, "top": 1038, "width": 14, "height": 29, "autoResize": false}], "visible": true, "enabled": true, "lineColor": "#000000", "fillColor": "#ffffff", "fontColor": "#000000", "font": "<PERSON><PERSON>;13;0", "showShadow": true, "containerChangeable": false, "containerExtending": false, "head": {"$ref": "AAAAAAFfcdFst6ZS72U="}, "tail": {"$ref": "AAAAAAFfcdLhGaZ8OeM="}, "lineStyle": 0, "points": "354:1038;80:1038", "nameLabel": {"$ref": "AAAAAAFfcfYDmKqEJcA="}, "stereotypeLabel": {"$ref": "AAAAAAFfcfYDmKqFswM="}, "propertyLabel": {"$ref": "AAAAAAFfcfYDmKqGs6k="}, "activation": {"$ref": "AAAAAAFfcfYDmKqHI/I="}, "showProperty": true, "showType": true}], "showSequenceNumber": true, "showSignature": true, "showActivation": true}], "visibility": "public", "isReentrant": true, "messages": [{"_type": "UMLMessage", "_id": "AAAAAAFfcdZkgKbvHqs=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Upload Elpro logger data", "source": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcdfVRacQ57A=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "It is a pdf file", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcdhpPKcqLsg=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Backup file", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdSZqqbDzdo="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcdiyT6dAWqw=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Backup done", "source": {"$ref": "AAAAAAFfcdSZqqbDzdo="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcd8/DqeR7Xc=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Update status - Backup written", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcd0Bgad561g=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Backup written", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfceDD+KevUeI=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Send file for conversion", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "visibility": "public", "messageSort": "asynchCall", "arguments": "async", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfceMb9KgI834=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Update status - File sent for conversion", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcefIwagsPgg=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Fetch converted file", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcegaM6hCe/w=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Not ready", "source": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfRjCanhnmU=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Check status", "source": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfSfOqn5bn8=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "File sent for conversion", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcehZgqhYWx0=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Fetch converted file", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfceh/p6huQEY=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Not ready", "source": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfTkmKoPKag=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Check status", "source": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcejXMaiE7/A=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Conversion done", "source": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "target": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfUDLqolYMU=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "File sent for conversion", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcekZ+KiaOpw=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Fetch converted file", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcelI8aiwSRg=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Converted file returned", "source": {"$ref": "AAAAAAFfcdP9DaaeeA0="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfce6DE6jJm9U=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Update status - Conversion received", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfF+YakzfAE=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Parse converted file", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfU6yqo7T3s=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Check status", "source": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfMI8KlqUPQ=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Store logger data", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcfDWL6jz/rU="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfVdcKpRvf4=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Conversion received", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfMwOKmA5uc=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "data stored", "source": {"$ref": "AAAAAAFfcfDWL6jz/rU="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfOZeqm14TY=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Perform calculations", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfPANqnL2S8=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Update status - Successful", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfWOV6pn67w=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Check status", "source": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "target": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}, {"_type": "UMLMessage", "_id": "AAAAAAFfcfYDl6qCF7Y=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Successful", "source": {"$ref": "AAAAAAFfcdLhF6Z1ZSw="}, "target": {"$ref": "AAAAAAFfcdFstaZLRjk="}, "visibility": "public", "messageSort": "synchCall", "isConcurrentIteration": false}], "participants": [{"_type": "UMLLifeline", "_id": "AAAAAAFfcdFstaZLRjk=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Line", "visibility": "public", "represent": {"$ref": "AAAAAAFfcdFstaZKaXg="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFfcdLhF6Z1ZSw=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Line", "visibility": "public", "represent": {"$ref": "AAAAAAFfcdLhF6Z07u0="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFfcdP9DaaeeA0=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Line", "visibility": "public", "represent": {"$ref": "AAAAAAFfcdP9DKadkFk="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFfcdSZqqbDzdo=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Line", "visibility": "public", "represent": {"$ref": "AAAAAAFfcdSZqqbCt5s="}, "isMultiInstance": false}, {"_type": "UMLLifeline", "_id": "AAAAAAFfcfDWL6jz/rU=", "_parent": {"$ref": "AAAAAAFfcdB+/aY4Wuc="}, "name": "Lifeline1", "visibility": "public", "represent": {"$ref": "AAAAAAFfcfDWL6jyXuY="}, "isMultiInstance": false}]}, {"_type": "UMLActor", "_id": "AAAAAAFfcdFS7qZGIA8=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "User", "visibility": "public", "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}, {"_type": "UMLComponent", "_id": "AAAAAAFfcdKWP6Zwzu8=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "LoggerReceiverEndpoint", "visibility": "public", "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false, "isIndirectlyInstantiated": true}, {"_type": "UMLNode", "_id": "AAAAAAFfcdPIZ6aZf4k=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "Elpro Windows Server", "visibility": "public", "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}, {"_type": "UMLComponent", "_id": "AAAAAAFfcdRsQaa+HNo=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "FileBackupSystem", "visibility": "public", "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false, "isIndirectlyInstantiated": true}, {"_type": "UMLComponent", "_id": "AAAAAAFfcfCv7Kju55w=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "Data Storage", "visibility": "public", "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false, "isIndirectlyInstantiated": true}], "visibility": "public", "attributes": [{"_type": "UMLAttribute", "_id": "AAAAAAFfcdFstaZKaXg=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "User", "visibility": "public", "isStatic": false, "isLeaf": false, "type": {"$ref": "AAAAAAFfcdFS7qZGIA8="}, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFfcdLhF6Z07u0=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "Role1", "visibility": "public", "isStatic": false, "isLeaf": false, "type": {"$ref": "AAAAAAFfcdKWP6Zwzu8="}, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFfcdP9DKadkFk=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "Role2", "visibility": "public", "isStatic": false, "isLeaf": false, "type": {"$ref": "AAAAAAFfcdPIZ6aZf4k="}, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFfcdSZqqbCt5s=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "Role3", "visibility": "public", "isStatic": false, "isLeaf": false, "type": {"$ref": "AAAAAAFfcdRsQaa+HNo="}, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}, {"_type": "UMLAttribute", "_id": "AAAAAAFfcfDWL6jyXuY=", "_parent": {"$ref": "AAAAAAFfcc8dGqYprR8="}, "name": "Role4", "visibility": "public", "isStatic": false, "isLeaf": false, "type": {"$ref": "AAAAAAFfcfCv7Kju55w="}, "isReadOnly": false, "isOrdered": false, "isUnique": false, "isDerived": false, "aggregation": "none", "isID": false}], "isAbstract": false, "isFinalSpecialization": false, "isLeaf": false}]}