/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.clinical.external.rest.exception.mapping;

import se.tss.ccis.clinical.external.rest.exception.IllegalShipmentStatusException;
import se.tss.ccis.clinical.external.rest.model.shipment.ExtShipmentStatus;
import se.tss.ccis.rest.exceptions.ExternalRollbackExceptionMapper;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;
import java.util.Arrays;
import java.util.stream.Collectors;

import static se.tss.ccis.clinical.external.rest.ExternalResponseCodes.ILLEGAL_SHIPMENT_STATUS;

@Provider
public class IllegalShipmentStatusExceptionMapper extends ExternalRollbackExceptionMapper<IllegalShipmentStatusException> {
    @Override
    protected Response.Status status() {
        return Response.Status.BAD_REQUEST;
    }

    @Override
    protected int errorCode() {
        return ILLEGAL_SHIPMENT_STATUS;
    }

    @Override
    protected String errorMessage(IllegalShipmentStatusException e) {
        return String.format("Shipment #%s on trial %s had invalid status '%s' (expected '%s')",
                e.getShipmentNumber(), e.getTrialName(), e.getReceivedStatus(),
                Arrays.stream(e.getExpectedStatuses()).map(ExtShipmentStatus::name).collect(Collectors.joining("/")));
    }
}