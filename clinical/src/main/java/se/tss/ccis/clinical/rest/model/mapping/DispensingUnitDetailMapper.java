/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */
package se.tss.ccis.clinical.rest.model.mapping;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import se.tss.ccis.clinical.rest.mapping.ProfileMapper;
import se.tss.ccis.clinical.rest.model.dun.DispensingUnitDetailDto;
import se.tss.ccis.clinical.rest.model.dun.DispensingUnitEventDetailDto;
import se.tss.ccis.clinical.rest.model.dun.DispensingUnitEventDto;
import se.tss.ccis.clinical.rest.model.dun.DispensingUnitEventDto.EventType;
import se.tss.ccis.clinical.rest.model.dun.DunUploadMissionDto;
import se.tss.ccis.core.math.MathUtils;
import se.tss.ccis.core.persistence.entities.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(uses = {ProfileMapper.class, DispensingUnitMapper.class})
@DecoratedWith(DispensingUnitDetailDecorator.class)
public abstract class DispensingUnitDetailMapper {
    public static final DispensingUnitDetailMapper INSTANCE = Mappers.getMapper(DispensingUnitDetailMapper.class);

    @Mapping(source = "dunEntity.uuid", target = "uuid")
    @Mapping(ignore = true, target = "dunUploadMissions")
    @Mapping(source = "dunEntity.dunEvents", target = "dispensingUnitEvents")
    @Mapping(source = "dunEntity.profileVersion", target = "profile")
    @Mapping(source = "dunEntity", target = "dispensingUnit")
    @Mapping(source = "gap", target = "gap")
    public abstract DispensingUnitDetailDto toDto(DispensingUnitEntity dunEntity,
                                                  Function<ClinicalLoggerMissionEntity, Optional<Date>> startDateExtractor,
                                                  Function<ClinicalLoggerMissionEntity, Optional<Date>> endDateExtractor,
                                                  Long gap);

    // create DunUploadMissionDto from the missions that affect the dun.
    protected List<DunUploadMissionDto> toDunMissions(List<DispensingUnitMissionLinkEntity> missionLinks,
                                                    Function<ClinicalLoggerMissionEntity, Optional<Date>> startDateExtractor,
                                                    Function<ClinicalLoggerMissionEntity, Optional<Date>> endDateExtractor) {
        return missionLinks.stream()
                .map(dunMissionLink -> Triple.of(dunMissionLink, startDateExtractor.apply(dunMissionLink.getMission()).orElse(null), endDateExtractor.apply(dunMissionLink.getMission()).orElse(null)))
                // remove the missions from List are not affected duns that  StartDate and EndDate of the missions are not between the dunMissionLink StartDate (not null) and EndDate (not null).
                .filter(dulAndMissionStartEndDate -> MathUtils.compare(false, dulAndMissionStartEndDate.getLeft().getStartDate(), dulAndMissionStartEndDate.getRight()) <= 0 &&
                        MathUtils.compare(true, dulAndMissionStartEndDate.getLeft().getEndDate(), dulAndMissionStartEndDate.getMiddle()) >= 0)

                // firstMeasurementTimestamp: if dunMissionLink.startDate == null use mission startDate, otherwise max(dunMissionLink startDate, mission startDate)
                // lastMeasurementTimestamp: if dunMissionLink.endDate == null use mission endDate, otherwise min(dunMissionLink endDate, mission endDate)
                .map(dulAndMissionStartEndDate -> toDunMissionDto(dulAndMissionStartEndDate.getLeft().getMission(),
                        MathUtils.max(false, dulAndMissionStartEndDate.getLeft().getStartDate(), dulAndMissionStartEndDate.getMiddle()),
                        MathUtils.min(false, dulAndMissionStartEndDate.getLeft().getEndDate(), dulAndMissionStartEndDate.getRight())
                ))
                .collect(
                        Collectors.collectingAndThen(
                                Collectors.toMap(DunUploadMissionDto::getId,
                                        Function.identity(),
                                        (left, right) -> {
                                            left.setFirstMeasurementTimestamp(MathUtils.min(false,left.getFirstMeasurementTimestamp(), right.getFirstMeasurementTimestamp()));
                                            left.setLastMeasurementTimestamp(MathUtils.max(false,left.getLastMeasurementTimestamp(), right.getLastMeasurementTimestamp()));
                                            return left;
                                        }),
                                m -> new ArrayList<>(m.values())
                        )
                );
    }

    @Mapping(source = "dunMission.serialNumber", target = "serialNumber")
    @Mapping(source = "dunMission.missionRef", target = "missionRef")
    @Mapping(source = "dunMission.uploadDate", target = "uploadDate")
    @Mapping(source = "dunMission.logger.id", target = "loggerId")
    @Mapping(source = "dunMission.shipment.id", target = "shipmentId")
    @Mapping(source = "dunMission.shipment.shipmentNo", target = "shipmentNo")
    @Mapping(source = "startDate", target = "firstMeasurementTimestamp")
    @Mapping(source = "endDate", target = "lastMeasurementTimestamp")
    abstract DunUploadMissionDto toDunMissionDto(ClinicalLoggerMissionEntity dunMission, Date startDate, Date endDate);

    List<DispensingUnitEventDto> toDunEvents(List<DispensingUnitEventEntity> dunEvents) {
        return dunEvents.stream().sorted(Comparator
                .comparing(DispensingUnitEventEntity::getTimestamp)
                .reversed())
                .map(this::toDispensingUnitEventDtoApp)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DispensingUnitEventDto toDispensingUnitEventDtoApp(DispensingUnitEventEntity dunEvent) {
        DispensingUnitEventDto eventDto = toDispensingUnitEventDto(dunEvent);
        if(CollectionUtils.isEmpty(dunEvent.getIntervalResults())
           && CollectionUtils.isEmpty(dunEvent.getPotentialIntervalResults())
           && CollectionUtils.isEmpty(dunEvent.getGapResults())
           && CollectionUtils.isEmpty(dunEvent.getAdjustments())){
            return eventDto;
        }
        if(CollectionUtils.isNotEmpty(dunEvent.getIntervalResults())){
            eventDto.addEventType(EventType.EXCURSION);
            eventDto.addEventDetails(excursionToDunEventDetails(dunEvent.getIntervalResults()));
        }
        if(CollectionUtils.isNotEmpty(dunEvent.getPotentialIntervalResults())){
            eventDto.addEventType(EventType.POTENTIAL_EXCURSION);
            eventDto.addEventDetails(potentialExcursionToDunEventDetails(dunEvent.getPotentialIntervalResults()));
        }
        if(CollectionUtils.isNotEmpty(dunEvent.getGapResults())){
            eventDto.addEventType(EventType.GAP);
            eventDto.addEventDetails(gapToDunEventDetails(dunEvent.getGapResults()));
        }
        if(CollectionUtils.isNotEmpty(dunEvent.getAdjustments())){
            eventDto.addEventDetails(adjustmentToDunEventDetails(dunEvent.getAdjustments()));
            if (CollectionUtils.isNotEmpty(eventDto.getEventDetails())) {
                eventDto.getEventDetails().forEach(ed -> eventDto.addEventType(ed.getEventType()));
            } else {
                eventDto.addEventType(EventType.ADJUSTMENT);
            }
        }
        eventDto.getEventDetails().sort(Comparator.nullsFirst(
                Comparator.comparing(DispensingUnitEventDetailDto::getEventStartTime, Comparator.nullsFirst(Comparator.naturalOrder()))));

        eventDto.setEventMissionIdRefs(getEventMissions(eventDto.getEventDetails()));
        return eventDto;
    }

    private Set<String> getEventMissions(List<DispensingUnitEventDetailDto> eventDetails){
        return eventDetails.stream()
                .filter(ed -> CollectionUtils.isNotEmpty(ed.getMissionIdRefs()))
                .flatMap(ed -> ed.getMissionIdRefs().stream())
                .collect(Collectors.toSet());
    }

    @Mapping(source = "timestamp", target = "reportDate")
    @Mapping(source = "logger.unitSerialNo", target = "loggerSerialNo")
    @Mapping(source = "logger.id", target = "loggerId")
    @Mapping(source = "shipment.id", target = "shipmentId")
    @Mapping(source = "shipment.shipmentNo", target = "shipmentNo")
    @Mapping(ignore = true, target = "eventDetails")
    @Mapping(ignore = true, target = "eventTypes")
    @Mapping(ignore = true, target = "eventMissionIdRefs")
    protected abstract DispensingUnitEventDto toDispensingUnitEventDto(DispensingUnitEventEntity dunEvent);

    List<DispensingUnitEventDetailDto> excursionToDunEventDetails(List<EventIntervalResultEntity> excursionIntervalEvents) {
        return excursionIntervalEvents.stream()
                .flatMap(eir -> eir.getExcursions().stream())
                .map(this::excursionToDunEventDetailDto)
                .collect(Collectors.toList());

    }

    List<DispensingUnitEventDetailDto> gapToDunEventDetails(List<EventGapResultEntity> gapResults) {
        return gapResults.stream()
                .flatMap(gap -> gap.getExcursions().stream())
                .map(this::excursionToDunEventDetailDto)
                .collect(Collectors.toList());
    }

    @Mapping(expression="java( excursionEvent.getEventGap() != null ? DispensingUnitEventDto.EventType.GAP: DispensingUnitEventDto.EventType.EXCURSION )", target = "eventType")
    @Mapping(expression="java( excursionEvent.getEventGap() != null ? null : excursionEvent.getEventInterval().getStabilityInterval().toIntervalString())", target = "eventInfo")
    @Mapping(source = "timeInMillis", target = "eventStartTime")
    @Mapping(source = "timeOutMillis", target = "eventEndTime")
    @Mapping(expression="java(excursionEvent.getMinutesInExcursion())", target = "eventDuration")
    @Mapping(ignore = true, target = "adjustmentId")
    @Mapping(ignore = true, target = "adjustmentName")
    @Mapping(ignore = true, target = "loggerId")
    @Mapping(ignore = true, target = "loggerSerialNo")
    @Mapping(ignore = true, target = "deviationId")
    @Mapping(ignore = true, target = "deviationName")
    @Mapping(expression="java(toMissionIdRefs(excursionEvent.getDiversion() != null ? excursionEvent.getDiversion().getSourceDiversions() :null))", target = "missionIdRefs")
    protected abstract DispensingUnitEventDetailDto excursionToDunEventDetailDto(ExcursionEventEntity excursionEvent);

    List<DispensingUnitEventDetailDto> potentialExcursionToDunEventDetails(List<PotentialEventIntervalResultEntity> potentialIntervalResults) {
        return potentialIntervalResults.stream()
                .flatMap(potentialE -> potentialE.getPotentialExcursions().stream())
                .map(this::potentialExcursionToDunEventDetailDto)
                .collect(Collectors.toList());
    }

    @Mapping(expression="java( DispensingUnitEventDto.EventType.POTENTIAL_EXCURSION )", target = "eventType")
    @Mapping(expression="java( potentialExcursionEvent.getPotentialEventInterval().getStabilityInterval().toIntervalString() )", target = "eventInfo")
    @Mapping(source = "timeInMillis", target = "eventStartTime")
    @Mapping(source = "timeOutMillis", target = "eventEndTime")
    @Mapping(expression="java(potentialExcursionEvent.getMinutesInExcursion())", target = "eventDuration")
    @Mapping(ignore = true, target = "adjustmentId")
    @Mapping(ignore = true, target = "adjustmentName")
    @Mapping(ignore = true, target = "loggerId")
    @Mapping(ignore = true, target = "loggerSerialNo")
    @Mapping(ignore = true, target = "deviationId")
    @Mapping(ignore = true, target = "deviationName")
    @Mapping(expression="java(toMissionIdRefs(potentialExcursionEvent.getDiversion() != null ? potentialExcursionEvent.getDiversion().getSourceDiversions() :null))", target = "missionIdRefs")
    abstract DispensingUnitEventDetailDto potentialExcursionToDunEventDetailDto(PotentialExcursionEventEntity potentialExcursionEvent);

    List<DispensingUnitEventDetailDto> adjustmentToDunEventDetails(List<AdjustmentEventEntity> adjustmentEvents) {
        return adjustmentEvents.stream()
                .map(this::adjustmentToDunEventDetailDto)
                .collect(Collectors.toList());
    }

    @Mapping(target = "eventType", expression=
            "java( adjustmentEventEntity.getAdjustmentSet() == null ? DispensingUnitEventDto.EventType.ADJUSTMENT : DispensingUnitEventDto.EventType.valueOf ( adjustmentEventEntity.getAdjustmentSet().getType().name() ) )")
    @Mapping(target = "eventInfo", expression="java(adjustmentEventEntity.getStabilityInterval().toIntervalString())")
    @Mapping(target = "eventStartTime", source = "timedTemperatureAdjustment.startMillis")
    @Mapping(target = "eventEndTime", source = "timedTemperatureAdjustment.endMillis")
    @Mapping(target = "eventDuration", expression="java(adjustmentEventEntity.getMillisAdjusted()/60000)")
    @Mapping(target = "adjustmentId", source = "adjustmentSet.id")
    @Mapping(target = "adjustmentName", source = "adjustmentSet.presentationId")
    @Mapping(target = "loggerId", source = "timedTemperatureAdjustment.loggerAdjustment.logger.id")
    @Mapping(target = "loggerSerialNo", source = "timedTemperatureAdjustment.loggerAdjustment.logger.unitSerialNo")
    @Mapping(target = "deviationId", source = "adjustmentSet.treatedDeviation.id")
    @Mapping(target = "deviationName", source = "adjustmentSet.treatedDeviation.presentationId")
    @Mapping(target = "missionIdRefs", ignore =  true)
    abstract DispensingUnitEventDetailDto adjustmentToDunEventDetailDto(AdjustmentEventEntity adjustmentEventEntity);

    protected Set<String> toMissionIdRefs(List<DiversionSourceEntity> sourceDiversions){
        return sourceDiversions == null ? new HashSet<>() : sourceDiversions.stream()
                .map(DiversionSourceEntity::getMission)
                .map(mission->mission.getId() + "," + mission.getMissionRef())
                .collect(Collectors.toSet());
    }
}

