/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.clinical.rest.model.logger;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

// Smaller Logger dto class to be used in list results and similar cases where not all information in the logger is needed
public class ClinicalLoggerListResponseDto {
    @SuppressWarnings("squid:S00115") //camelCase used to match the property names of the dtos
    public enum SortParam { unitSerialNo, clinicalLoggerModel, description, clinicalLoggerType, loggerManufacturerName, expiryDate, active, lastUpload, uploaderFullName }
    
    @ApiModelProperty
    private Long id;
    @ApiModelProperty(required = true)
    @NotEmpty
    @Length(max = 64)
    private String unitSerialNo;
    @ApiModelProperty(required = true)
    @NotEmpty
    private String description;
    @ApiModelProperty(required = true)
    @FutureOrPresent
    @NotNull
    private Date expiryDate;
    @ApiModelProperty(required = true)
    @NotNull
    private List<LoggerTrialUnitDto> clinicalTrialUnits;
    @ApiModelProperty(required = true)
    @NotNull
    private Long clinicalLoggerTypeId;
    @ApiModelProperty(required = true)
    @NotEmpty
    private String clinicalLoggerTypeName;

    @ApiModelProperty(required = true)
    @NotEmpty
    private String loggerManufacturerName;

    @ApiModelProperty(required = true)
    @NotEmpty
    private String clinicalLoggerModelName;

    @ApiModelProperty
    private LoggerUploadDto latestUpload;
    @ApiModelProperty(required = true)
    @NotNull
    private boolean active;

    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnitSerialNo() {
        return unitSerialNo;
    }

    public void setUnitSerialNo(String unitSerialNo) {
        this.unitSerialNo = unitSerialNo;
    }

    public String getDescription() {
        return description;
    }
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<LoggerTrialUnitDto> getClinicalTrialUnits() {
        return clinicalTrialUnits;
    }

    public void setClinicalTrialUnits(List<LoggerTrialUnitDto> clinicalTrialUnits) {
        this.clinicalTrialUnits = clinicalTrialUnits;
    }

    public String getClinicalLoggerTypeName() {
        return clinicalLoggerTypeName;
    }

    public Long getClinicalLoggerTypeId() {
        return clinicalLoggerTypeId;
    }

    public void setClinicalLoggerTypeId(Long clinicalLoggerTypeId) {
        this.clinicalLoggerTypeId = clinicalLoggerTypeId;
    }

    public void setClinicalLoggerTypeName(String clinicalLoggerTypeName) {
        this.clinicalLoggerTypeName = clinicalLoggerTypeName;
    }

    public String getClinicalLoggerModelName() {
        return clinicalLoggerModelName;
    }

    public void setClinicalLoggerModelName(String clinicalLoggerModelName) {
        this.clinicalLoggerModelName = clinicalLoggerModelName;
    }

    public String getLoggerManufacturerName() {
        return loggerManufacturerName;
    }

    public void setLoggerManufacturerName(String loggerManufacturerName) {
        this.loggerManufacturerName = loggerManufacturerName;
    }

    public LoggerUploadDto getLatestUpload() {
        return latestUpload;
    }
    
    public void setLatestUpload(LoggerUploadDto latestUpload) {
        this.latestUpload = latestUpload;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }
}
