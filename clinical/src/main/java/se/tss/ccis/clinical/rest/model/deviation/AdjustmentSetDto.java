/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.clinical.rest.model.deviation;

import io.swagger.annotations.ApiModelProperty;
import se.tss.ccis.core.model.FileDto;

import java.util.List;
import java.util.Set;

public class AdjustmentSetDto extends AdjustmentSetSimpleDto {

    @ApiModelProperty("The set of manual adjustments")
    private List<ManualAdjustmentDto> manualAdjustments;
    @ApiModelProperty("The set of batch pre adjustments")
    private List<BatchPreAdjustmentDto> batchPreAdjustments;
    @ApiModelProperty("The set of dun pre adjustments")
    private List<DunPreAdjustmentDto> dunPreAdjustments;
    @ApiModelProperty("The set of logger adjustments")
    private List<LoggerAdjustmentDto> loggerAdjustments;
    @ApiModelProperty
    private Boolean autoPopulatedAdjustments;
    @ApiModelProperty("Optional linked deviation")
    private DeviationDto deviation;
    @ApiModelProperty
    private Long nextAdmissibleId;
    @ApiModelProperty
    private Boolean isAdmissible;
    @ApiModelProperty
    private Set<FileDto> attachedFiles;

    public List<ManualAdjustmentDto> getManualAdjustments() {
        return manualAdjustments;
    }

    public void setManualAdjustments(List<ManualAdjustmentDto> manualAdjustments) {
        this.manualAdjustments = manualAdjustments;
    }

    public List<BatchPreAdjustmentDto> getBatchPreAdjustments() {
        return batchPreAdjustments;
    }

    public void setBatchPreAdjustments(List<BatchPreAdjustmentDto> batchPreAdjustments) {
        this.batchPreAdjustments = batchPreAdjustments;
    }

    public List<DunPreAdjustmentDto> getDunPreAdjustments() {
        return dunPreAdjustments;
    }

    public void setDunPreAdjustments(List<DunPreAdjustmentDto> dunPreAdjustments) {
        this.dunPreAdjustments = dunPreAdjustments;
    }

    public List<LoggerAdjustmentDto> getLoggerAdjustments() {
        return loggerAdjustments;
    }

    public void setLoggerAdjustments(List<LoggerAdjustmentDto> loggerAdjustments) {
        this.loggerAdjustments = loggerAdjustments;
    }

    public Boolean getAutoPopulatedAdjustments() {
        return autoPopulatedAdjustments;
    }

    public void setAutoPopulatedAdjustments(Boolean autoPopulatedAdjustments) {
        this.autoPopulatedAdjustments = autoPopulatedAdjustments;
    }

    /**
     * Detailed deviation DTO
     * @return
     */
    @Override
    public DeviationDto getDeviation() {
        return deviation;
    }

    public void setDeviation(DeviationDto deviation) {
        this.deviation = deviation;
    }

    public Long getNextAdmissibleId() {
        return nextAdmissibleId;
    }

    public void setNextAdmissibleId(Long nextAdmissibleId) {
        this.nextAdmissibleId = nextAdmissibleId;
    }

    public Boolean getIsAdmissible() {
        return isAdmissible;
    }

    public void setIsAdmissible(Boolean isAdmissible) {
        this.isAdmissible = isAdmissible;
    }

    public Set<FileDto> getAttachedFiles() {
        return attachedFiles;
    }

    public void setAttachedFiles(Set<FileDto> attachedFiles) {
        this.attachedFiles = attachedFiles;
    }
}
