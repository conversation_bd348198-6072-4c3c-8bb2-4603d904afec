/*
ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
*/
import React, { Fragment, Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { List } from 'immutable';
import { injectIntl, intlShape } from 'react-intl';
import { recordFromJs } from 'redux-immutable-tools';
import {
  Profile,
  LoggerTypeShapeList,
  historyShape,
  locationShape,
  UserShape,
  profileCreationMethods,
  versionStatusString,
  CompanyShape,
} from 'tss-lib-common';
import {
  fetchProfiles,
  fetchBlindedStabilityData,
  fetchBlindableProfileVersions,
  fetchLoggerTypes,
  sendAddNewProfile,
  clearProfiles,
} from 'tss-lib-data';
import { Loading, FadeOut, MessageDialog, Prompt } from 'tss-lib-components';

import AddNewProfilePage from '../pages/AddNewProfilePage';
import ConfirmSendForReviewApprovalDialog from '../components/ConfirmSendForReviewApprovalDialog';
import { STABILITY_INTERVAL_TYPES } from '../constants';
import { ProfileListRoute, ProfileDetailsRoute } from '../routes';

class AddNewProfileContainer extends Component {
  static contextTypes = {};

  static propTypes = {
    intl: intlShape.isRequired,
    user: UserShape.isRequired,
    history: historyShape.isRequired,
    location: locationShape.isRequired,
    loggerTypes: LoggerTypeShapeList.isRequired,
    loggerTypesInitialized: PropTypes.bool.isRequired,
    company: CompanyShape.isRequired,
    fetchLoggerTypes: PropTypes.func.isRequired,
    sendAddNewProfile: PropTypes.func.isRequired,
    clearProfiles: PropTypes.func.isRequired,
  };

  static defaultProps = {};

  constructor( props ) {
    super( props );
    const duplicate =
      ( this.props.location.state && this.props.location.state.duplicate ) || {};
    const derivedFromDuplicate = duplicate.versionId !== undefined;
    const pts = duplicate.productTypeIds;

    let newProfile = recordFromJs( Profile, Profile.normalize( {
      stabilityIntervals: [
        {
          highLimit: 0.1,
          lowLimit: 0,
          minutesAllowed: 0,
          type: STABILITY_INTERVAL_TYPES[ 1 ].value,
          unlimited: true,

          meta: {
            _internalId: 1,
          },
        },
      ],
      // If profile is created via duplicate,
      // use shipmentNarrowerIntervalMargin from 'parent', otherwise use system setting.
      shipmentNarrowerIntervalMargin: derivedFromDuplicate ? duplicate.shipmentNarrowerIntervalMargin : this.getDefaultShipmentNarrowIntervalMargin(),
      ...duplicate,
    } ) );

    newProfile = newProfile.set( 'creationMethod', profileCreationMethods.MANUAL );
    if ( List.isList( duplicate.productTypeIds ) ) {
      newProfile = newProfile.set( 'productTypeIds', duplicate.productTypeIds );
    } else {
      let profileIds = [];
      if ( pts ) {
        Object.keys( pts ).forEach( ( key ) => {
          if ( pts[ key ] && Array.isArray( Object.values( pts[ key ] ) ) && Object.values( pts[ key ] ).length ) {
            profileIds = new List( Object.values( pts[ key ] )[ 0 ] );
          }
        } );
        newProfile = newProfile.set( 'productTypeIds', new List( profileIds ) );
      }
    }

    if ( this.props.company ) {
      newProfile = newProfile.set( 'allowExtended', this.props.company.defaultAllowExtendedStability );
    }

    let profileChanged = false;
    if ( this.props.location.state && this.props.location.state.duplicate ) {
      profileChanged = true;
    }

    this.state = {
      profile: newProfile,
      profiles: new List(),
      compatibleProfiles: new List(),
      savingDraft: false,
      sendingForReview: false,
      profileChanged,
      createBlinded: false,
      createAutoBlinded: false,
      createManualBlinded: false,
      selectedProfileVersionIds: [],
      selectedProfiles: [],
      blindingValidationErrors: [],
      commentChangedByUser: false,
    };
  }

  componentDidMount() {
    this.fetchData();
  }

  componentWillReceiveProps( nextProps ) {
    if ( nextProps.history.location.pathname !== this.props.location.pathname ) {
      this.setState( { profileChanged: false } );
    }
  }

  getDefaultShipmentNarrowIntervalMargin = () => ( this.props.company.defaultShipmentNarrowIntervalMargin > 0 ?
    this.props.company.defaultShipmentNarrowIntervalMargin : null );

  validate = () => {
    const validatedProfile = this.state.profile.validateAndStore( true );
    const blindingValidationErrors = [];
    if ( this.state.selectedProfileVersionIds.length === 0 ) {
      if (
        validatedProfile.creationMethod ===
        profileCreationMethods.AUTOMATIC_BLINDING
      ) {
        blindingValidationErrors.push(
          'module.trials.profile.create.blinded.no.main.error',
        );
      }
      if (
        validatedProfile.creationMethod ===
        profileCreationMethods.MANUAL_BLINDING
      ) {
        blindingValidationErrors.push(
          'module.trials.profile.create.manual.blinded.no.main.error',
        );
      }
    } else if ( this.state.selectedProfileVersionIds.length === 1 ) {
      if (
        validatedProfile.creationMethod ===
        profileCreationMethods.AUTOMATIC_BLINDING
      ) {
        blindingValidationErrors.push(
          'module.trials.profile.create.blinded.no.secondary.error',
        );
      }
      if (
        validatedProfile.creationMethod ===
        profileCreationMethods.MANUAL_BLINDING
      ) {
        blindingValidationErrors.push(
          'module.trials.profile.create.manual.blinded.no.secondary.error',
        );
      }
    }
    this.setState( { profile: validatedProfile, blindingValidationErrors } );

    return {
      validatedProfile,
      isValid:
        validatedProfile.isValid() && blindingValidationErrors.length === 0,
    };
  };

  fetchData = async () => {
    await this.props.fetchLoggerTypes();
    this.setState( { dataInitialized: true } );
  };

  handleProfileChange = ( profile, state ) => {
    if ( this.state.creationMethod !== ( state && state.creationMethod ) ) {
      this.setState( {
        blindingValidationErrors: [],
      } );
    }
    ( async () => {
      await this.props.clearProfiles();
    } )();
    if ( profile ) {
      this.setState( {
        profile,
        profileChanged: true,
        commentChangedByUser:
          profile.comment &&
          profile.comment.length &&
          profile.comment !== this.state.profile.comment,
      } );
    } else {
      let sProfile = this.state.profile;
      if ( state.stabilityData ) {
        sProfile = sProfile
          .set( 'creationMethod', state.creationMethod )
          .set( 'allowExtended', state.stabilityData.allowExtended )
          .set( 'useStrictLimit', state.profile.useStrictLimit )
          .set( 'blindingProfileVersionIds', new List( state.selectedProfileVersionIds ) )
          .set( 'shipmentNarrowerIntervalMargin', state.creationMethod === profileCreationMethods.AUTOMATIC_BLINDING ? null : this.getDefaultShipmentNarrowIntervalMargin() );
      } else {
        sProfile = sProfile
          .set( 'creationMethod', state.creationMethod )
          .set( 'useStrictLimit', state.profile.useStrictLimit )
          .set( 'blindingProfileVersionIds', new List( state.selectedProfileVersionIds ) )
          .set( 'shipmentNarrowerIntervalMargin', state.creationMethod === profileCreationMethods.AUTOMATIC_BLINDING ? null : this.getDefaultShipmentNarrowIntervalMargin() );
      }
      this.setState( {
        profile: sProfile,
        creationMethod: state.creationMethod,
        profileChanged: true,
        stabilityData: state.stabilityData,
        selectedProfileVersionIds: state.selectedProfileVersionIds,
        selectedProfiles: state.selectedProfiles,
      } );
    }
  };

  clearBlinded = async ( ) => {
    await this.props.clearProfiles();
    const profile = this.state.profile;
    const stabilityData = {
      allowExtended: false,
      stabilityIntervals: [
        {
          highLimit: 0.1,
          lowLimit: 0,
          minutesAllowed: 0,
          type: STABILITY_INTERVAL_TYPES[ 1 ].value,
          unlimited: true,
        },
      ],
    };

    const duplicate = profile.serialize();
    delete duplicate.stabilityIntervals;

    const derivedFromDuplicate = duplicate.versionId !== undefined;

    const cleared = recordFromJs( Profile, Profile.normalize( {
      ...duplicate,
      stabilityIntervals: stabilityData.stabilityIntervals,
      creationMethod: this.state.creationMethod,
      // If profile is created via duplicate,
      // use shipmentNarrowerIntervalMargin from 'parent', otherwise use system setting.
      shipmentNarrowerIntervalMargin: derivedFromDuplicate ? duplicate.shipmentNarrowerIntervalMargin : this.getDefaultShipmentNarrowIntervalMargin(),
    } ) );
    await this.setState( {
      profile: cleared,
      stabilityData: null,
      blindingValidationErrors: [],
    } );
  };

  handleCreateBlindedChange = ( createBlinded ) => {
    this.clearBlinded();
    if ( !createBlinded ) {
      this.setState( {
        createBlinded,
        createAutoBlinded: false,
        createManualBlinded: false,
        creationMethod: profileCreationMethods.MANUAL,
      } );
    } else {
      this.setState( {
        createBlinded,
        createAutoBlinded: true,
        createManualBlinded: false,
        creationMethod: profileCreationMethods.AUTOMATIC_BLINDING,
      } );
    }
  };

  recentToTableRow = ( profile ) => {
    const user = this.props.user;
    const label = versionStatusString(
      profile.status,
      profile.parentStatus,
      this.props.intl,
    );
    return [
      {
        id: profile.versionId,
        url: ProfileDetailsRoute.url(
          { id: profile.id },
          { versionId: profile.versionId },
        ),
        data: [
          `${ profile.name } [v. ${ profile.versionSuffix }]`,
          profile.description,
          user ? user.fullName : 'N/A',
          label,
        ],
      },
    ];
  };
  handleSaveAsDraft = async () => {
    const { validatedProfile, isValid } = this.validate();

    if ( isValid ) {
      this.setState( { savingDraft: true } );
      try {
        const result = await this.props.sendAddNewProfile(
          this.state.profile.serialize(),
        );
        this.setState( { profileChanged: false }, () =>
          this.props.history.push( {
            pathname: ProfileListRoute.url(),
            state: {
              profileChanged: false,
              recentChangedData: this.recentToTableRow( result ),
              profile: result,
            },
          } ),
        );
      } catch ( err ) {
        if ( err && err.data ) {
          this.setState( { profile: validatedProfile } );
          this.setState( {
            profile: validatedProfile.setValidationError(
              err.data.attribute,
              err.data.message,
            ),
            showSaveDraftFailedDialog: false,
          } );
        } else {
          this.setState( { showSaveDraftFailedDialog: true } );
        }
      } finally {
        this.setState( { savingDraft: false } );
      }
    }
  };

  resetChangedData = () => {
    this.setState( { profileChanged: false } );
  };

  render() {
    if ( !this.props.loggerTypesInitialized || !this.state.dataInitialized ) {
      return (
        <FadeOut>
          <Loading fill />
        </FadeOut>
      );
    }

    const { profile } = this.state;
    const breadcrumbs = [
      { location: { pathname: '/' }, label: 'TSS' },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.monitor.administration' } ) },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.trials.profile.list.heading' } ) },
      { location: { pathname: '' }, label: this.props.intl.formatMessage( { id: 'module.trials.profile.add' } ) },
    ];

    return (
      <Fragment>
        <AddNewProfilePage
          resetChangedData={ ( ) => this.resetChangedData }
          defaultShipmentNarrowIntervalMargin={ this.props.company.defaultShipmentNarrowIntervalMargin }
          profile={ profile }
          profiles={ this.state.profiles }
          compatibleProfiles={ this.state.compatibleProfiles }
          loggerTypes={ this.props.loggerTypes }
          breadcrumbs={ breadcrumbs }
          createAutoBlinded={ this.state.createAutoBlinded }
          createBlinded={ this.state.createBlinded }
          createManualBlinded={ this.state.createManualBlinded }
          blindingValidationErrors={ this.state.blindingValidationErrors }
          stabilityData={ this.state.stabilityData }
          sendingForReview={ this.state.sendingForReview }
          savingDraft={ this.state.savingDraft }
          onProfileChange={ this.handleProfileChange }
          onSaveAsDraft={ this.handleSaveAsDraft }
          onCreateBlindedChange={ this.handleCreateBlindedChange }
          onSelectBlindingTypeChange={ this.handleBlindingTypeChange }
        />
        { // Save as draft error dialog
          this.state.showSaveDraftFailedDialog ? (
            <MessageDialog
              heading={ this.props.intl.formatMessage( {
                id:
                'module.trials.profile.add.new.profile.failure.message.heading',
              } ) }
              content={ this.props.intl.formatMessage( {
                id:
                'module.trials.profile.add.new.profile.failure.message.content',
              } ) }
              error
              onOk={ () => {
                this.setState( { showSaveDraftFailedDialog: false } );
              } }
            />
          ) : null }
        { this.state.showConfirmSendForReviewDialog ? (
          <ConfirmSendForReviewApprovalDialog
            profile={ profile }
            creationMethod={ this.state.creationMethod }
            stabilityData={ this.state.stabilityData }
            translationKeyPrefix="module.trials.send.for.review.confirm.dialog"
            loggerTypes={ this.props.loggerTypes }
            selectedProfiles={ this.state.selectedProfiles }
            onSubmit={ this.handleSubmitConfirmSendForReview }
            onCancel={ () =>
              this.setState( {
                showConfirmSendForReviewDialog: false,
                sendingForReview: false,
              } )
            }
          />
        ) : null }
        <Prompt
          key="prompt"
          when={ this.state.profileChanged }
          message={ this.props.intl.formatMessage( {
            id: 'module.common.navigation.warning',
          } ) }
        />
      </Fragment>
    );
  }
}

function mapStateToProps( state ) {
  return {
    user: state.authState.user,
    profiles: state.profileState.profiles,
    loggerTypes: state.loggerState.types,
    loggerTypesInitialized: state.loggerState.typesInitialized,
    company: state.companyState.company,
  };
}

function mapDispatchToProps( dispatch ) {
  return bindActionCreators(
    {
      fetchProfiles,
      fetchBlindedStabilityData,
      fetchBlindableProfileVersions,
      fetchLoggerTypes,
      sendAddNewProfile,
      clearProfiles,
    },
    dispatch,
  );
}

export default connect( mapStateToProps, mapDispatchToProps )(
  injectIntl( AddNewProfileContainer ),
);
