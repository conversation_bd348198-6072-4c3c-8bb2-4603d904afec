a {
    text-decoration: none;
    color: rgb(49, 46, 81);
}
.rc-menu {
    font-size: 1.4rem;
    color: rgb(49, 46, 81);
    font-weight: 600;
    border: none;
    box-shadow: none;
}

.rc-menu-item {
    border-left: 4px solid rgb(255, 255, 255);
}
.rc-menu-item a {
    cursor: pointer;
}

.rc-menu-submenu-title {
    border-left: 4px solid rgb(255, 255, 255);
    cursor: pointer;
}

.rc-menu-submenu-selected {
    background-color: white;
}

.rc-menu-item-selected {
    background-color: white;
    border-left: 4px solid rgb(153, 204, 0);
}
.rc-menu-item-selected a {
    color:  rgb(153, 204, 0);
}

.rc-menu-inline .rc-menu-submenu-arrow:before {
    content: "\f0da" !important;
}

.rc-menu-item-active {
    background-color: white;
    border-left: 4px solid rgba(153, 204, 0, 0.5);
}
.rc-menu-item-active a {
    color: rgba(153, 204, 0, 0.5);
    background-color: white;
}
.rc-menu-submenu-active > .rc-menu-submenu-title{
    color: rgba(153, 204, 0, 0.5);
    background-color: white;
    border-left: 4px solid rgba(153, 204, 0, 0.5);
}

.rc-menu-inline .rc-menu-submenu-arrow {
    line-height: 86%;
    font-size: 20px;
}

.slideOut {
    margin-left: 0;
    transition: margin-left 0.5s;
}
.slideIn {
    margin-left: -190px;
    transition: margin-left 0.5s;
}

.innerContentWithMenu {
  padding-left: 190px;
  transition: padding-left 0.5s;
}
.innerContentWithOutMenu {
    padding-left: 0;
    transition: padding-left 0.5s;
}