import PropTypes from 'prop-types';
import { stylePropType } from 'tss-lib-common';

export const ColumnShape = PropTypes.shape( {
  label: PropTypes.string,
  labelKey: PropTypes.string,
  sortable: PropTypes.bool,
  selectable: PropTypes.oneOfType( [ PropTypes.bool, PropTypes.func ] ),
  style: stylePropType,
  cellStyle: stylePropType,
  centered: PropTypes.bool,
  render: PropTypes.func,
} );

export const DataShape = PropTypes.oneOfType( [
  PropTypes.string,
  PropTypes.number,
  PropTypes.bool,
  PropTypes.func,
  PropTypes.element,
] );

export const DataRowShape = PropTypes.shape( {
  data: PropTypes.arrayOf( DataShape ),
  id: PropTypes.oneOfType( [ PropTypes.string, PropTypes.number ] ).isRequired,
  url: PropTypes.string,
  expanded: PropTypes.bool,
} );
