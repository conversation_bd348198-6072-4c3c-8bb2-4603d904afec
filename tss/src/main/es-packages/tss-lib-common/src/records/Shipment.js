/*
ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
*/
import { List, Record as record } from 'immutable';
import PropTypes from 'prop-types';
import ImmutablePropTypes from 'react-immutable-proptypes';
import { listOf } from 'redux-immutable-tools';
import { Batch, BatchShapeList } from './Batch';
import { ShipmentMission, ShipmentMissionShapeList, UploadedFile, UploadedFileShapeList } from './Mission';


export const UploadResult = record( {
  id: null,
  uid: null,
  uploadUri: null,
  fileUris: List,
  files: listOf( UploadedFile ),
  uploadFullName: null,
  uploadDate: null,
  uploadUserId: null,
  shipmentMissions: listOf( ShipmentMission ),
} );

UploadResult.normalize = ( data ) => ( {
  ...data,
} );


UploadResult.prototype.serialize = function serialize() {
  return {
    id: this.id,
    uuid: this.uuid,
  };
};

export const UploadResultShape = ImmutablePropTypes.recordOf( {
  id: PropTypes.number,
  uid: PropTypes.string.isRequired,
  uploadUri: PropTypes.string,
  fileUris: ImmutablePropTypes.listOf( PropTypes.string ),
  files: UploadedFileShapeList,
  uploadFullName: PropTypes.string,
  uploadDate: PropTypes.number,
  uploadUserId: PropTypes.number,
  shipmentMissions: ShipmentMissionShapeList,
} );

export const UploadResultShapeList = ImmutablePropTypes.listOf( UploadResultShape );


export const ShipmentUpload = record( {
  id: null,
  shipmentNo: null,
  shipmentStatus: null,
  siteNumber: null,
  trialUnitId: null,
  trialName: null,
  trialActive: null,
  batches: listOf( Batch ),
  uploadResults: listOf( UploadResult ),
  earliestTimestamp: null,
  latestTimestamp: null,
} );

ShipmentUpload.normalize = ( data ) => ( {
  ...data,
} );

export const ShipmentUploadShapeObj = {
  id: PropTypes.number,
  shipmentNo: PropTypes.string,
  shipmentStatus: PropTypes.string,
  siteNumber: PropTypes.string,
  trialUnitId: PropTypes.number,
  trialName: PropTypes.string,
  trialActive: PropTypes.bool,
  batches: BatchShapeList.isRequired,
  uploadResults: UploadResultShapeList.isRequired,
  earliestTimestamp: PropTypes.number,
  latestTimestamp: PropTypes.number.isRequired,
};

export const ShipmentUploadShape = ImmutablePropTypes.recordOf( ShipmentUploadShapeObj );

export const ShipmentUploadShapeList = ImmutablePropTypes.listOf( ShipmentUploadShape );


export const Shipment = record( {
  id: null,
  fileUris: List,
  files: listOf( UploadedFile ),
  batches: listOf( Batch ),
  shipmentNo: null,
  shipmentStatus: null,
  siteNumber: null,
  trialUnitId: null,
  trialName: null,
  uploadUris: List,
  trialActive: null,
} );

Shipment.normalize = ( data ) => ( {
  ...data,
} );

Shipment.prototype.getStatus = function getStatus() {
  if ( this.fileUris.size > 0 ) {
    return 'module.loggers.upload.shipments.status.processed';
  }
  return 'module.loggers.upload.shipments.status.pending';
};

Shipment.prototype.isProcessed = function isPending() {
  return this.fileUris.size > 0;
};
export const shipmentShapeObj = {
  id: PropTypes.number,
  fileUris: ImmutablePropTypes.listOf( PropTypes.string ),
  files: UploadedFileShapeList.isRequired,
  batches: BatchShapeList.isRequired,
  shipmentNo: PropTypes.string,
  shipmentStatus: PropTypes.string,
  siteNumber: PropTypes.string,
  trialUnitId: PropTypes.number,
  trialName: PropTypes.string,
  uploadUris: ImmutablePropTypes.listOf( PropTypes.string ),
  trialActive: PropTypes.bool,
};

export const ShipmentShape = ImmutablePropTypes.recordOf( shipmentShapeObj );

export const ShipmentShapeList = ImmutablePropTypes.listOf( ShipmentShape );
