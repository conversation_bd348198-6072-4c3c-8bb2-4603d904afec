/* 
ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.  
The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights. 
The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,  
reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,  
disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing. 
TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take  
any and all actions deemed appropriate to safeguard its rights in and to the content of this document. 
*/
import { apiFetch, checkResponseOk, POST, GET, toJSON, queryUrl } from 'tss-lib-common';

const BASE_PATH = 'clinical/rest';

export const cloudLaunchShipmentLoggers = ( shipmentLoggersData ) => apiFetch( `${ BASE_PATH }/shipments/loggers/register`, {
  ...POST,
  body: JSON.stringify( shipmentLoggersData ),
} )
  .then( checkResponseOk )
  .then( toJSON );

export const fetchShipmentNumbers = ( trialId, searchQuery, limit = 1000 ) => {
  const query = { limit };
  if ( trialId ) {
    query.trialId = trialId;
  }
  if ( searchQuery ) {
    query.searchQuery = searchQuery;
  }
  query.status = [ 'PENDING' ];
  return apiFetch( queryUrl( `${ BASE_PATH }/shipments/search/numbers`, query ), GET )
    .then( checkResponseOk )
    .then( toJSON );
};
