# Clinical Module Microservices Migration - Implementation Guide

## Detailed Migration Timeline

### Phase 1: Infrastructure Services (Months 1-2)

#### Week 1-2: Configuration Service
**Objective:** Extract configuration management from monolith

**Tasks:**
1. Create Spring Boot project for Configuration Service
2. Extract ConfigManager and related classes
3. Implement REST API for configuration access
4. Set up MySQL database for configuration data
5. Implement caching with Redis
6. Update monolith to use Configuration Service API

**Code Changes:**
```java
// New Configuration Service API
@RestController
@RequestMapping("/api/v1/config")
public class ConfigurationController {
    
    @GetMapping("/{key}")
    public ConfigurationValue getConfiguration(@PathVariable String key) {
        return configurationService.getConfiguration(key);
    }
    
    @PostMapping
    public ConfigurationValue updateConfiguration(@RequestBody ConfigurationRequest request) {
        return configurationService.updateConfiguration(request);
    }
}
```

**Migration Strategy:**
- Dual-write pattern: Update both monolith and service
- Gradual read migration: Start reading from service
- Remove monolith configuration code after validation

#### Week 3-4: Audit Service
**Objective:** Centralize audit trail management

**Tasks:**
1. Create Audit Service with Spring Boot
2. Extract AuditTrailManager and related entities
3. Implement event-driven audit logging
4. Set up Kafka for audit events
5. Create audit query APIs
6. Migrate existing audit data

**Event Schema:**
```json
{
  "eventId": "uuid",
  "eventType": "AuditEvent",
  "timestamp": "2024-01-01T00:00:00Z",
  "source": "trial-service",
  "data": {
    "action": "TRIAL_CREATED",
    "userId": "user123",
    "entityId": "trial456",
    "changes": [...]
  }
}
```

#### Week 5-6: Notification Service
**Objective:** Extract notification and messaging functionality

**Tasks:**
1. Create Notification Service
2. Extract NotificationManager and email functionality
3. Implement message queuing with Azure Service Bus
4. Create notification templates system
5. Set up email service integration
6. Implement notification scheduling

#### Week 7-8: Integration Testing & Deployment
**Tasks:**
1. End-to-end testing of infrastructure services
2. Performance testing and optimization
3. Production deployment with monitoring
4. Documentation and team training

### Phase 2: User Management Service (Months 3-4)

#### Week 9-10: User Service Foundation
**Objective:** Extract user management core functionality

**Tasks:**
1. Create User Management Service
2. Extract UserEntity, CompanyEntity, OfficeEntity
3. Implement user CRUD operations
4. Set up dedicated user database
5. Implement data migration scripts

**Database Schema:**
```sql
-- User Service Database
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(255) UNIQUE,
    email VARCHAR(255),
    company_id BIGINT,
    office_id BIGINT,
    created_date TIMESTAMP,
    last_login TIMESTAMP
);

CREATE TABLE companies (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    tvn_id VARCHAR(50),
    settings JSON
);
```

#### Week 11-12: Authentication & Authorization
**Tasks:**
1. Implement JWT token service
2. Create OAuth2/OIDC integration
3. Implement role-based access control
4. Create session management
5. Set up API security

**JWT Implementation:**
```java
@Service
public class JwtTokenService {
    
    public String generateToken(UserEntity user) {
        return Jwts.builder()
            .setSubject(user.getUsername())
            .claim("companyId", user.getCompanyId())
            .claim("roles", user.getRoles())
            .setExpiration(Date.from(Instant.now().plus(Duration.ofHours(24))))
            .signWith(SignatureAlgorithm.HS512, secretKey)
            .compact();
    }
}
```

#### Week 13-14: Service Integration
**Tasks:**
1. Update all services to use User Service for authentication
2. Implement service-to-service authentication
3. Create user context propagation
4. Test cross-service authorization

#### Week 15-16: Migration & Testing
**Tasks:**
1. Migrate user data to new service
2. Update monolith to use User Service APIs
3. Comprehensive testing
4. Production deployment

### Phase 3: Business Services (Months 5-8)

#### Month 5: Profile Management Service
**Objective:** Extract profile and configuration management

**Key Components:**
- ProfileEntity and ProfileVersionEntity
- StabilityConfigurationEntity
- Profile versioning and approval workflows
- Configuration templates

**API Design:**
```java
@RestController
@RequestMapping("/api/v1/profiles")
public class ProfileController {
    
    @GetMapping
    public Page<ProfileDto> getProfiles(@RequestParam Map<String, String> filters) {
        return profileService.searchProfiles(filters);
    }
    
    @PostMapping
    public ProfileDto createProfile(@RequestBody CreateProfileRequest request) {
        return profileService.createProfile(request);
    }
    
    @PostMapping("/{id}/versions")
    public ProfileVersionDto createVersion(@PathVariable Long id, 
                                         @RequestBody CreateVersionRequest request) {
        return profileService.createVersion(id, request);
    }
}
```

#### Month 6: Integration Service
**Objective:** Extract external integration functionality

**Key Components:**
- IRT integration management
- KAA system integration
- Message transformation and routing
- Integration configuration

**Integration Patterns:**
```java
@Component
public class IrtIntegrationService {
    
    @EventListener
    public void handleTrialCreated(TrialCreatedEvent event) {
        // Send trial data to IRT system
        irtClient.createTrial(mapToIrtFormat(event.getData()));
    }
    
    @EventListener
    public void handleKitStatusUpdate(KitStatusUpdateEvent event) {
        // Update kit status in IRT
        irtClient.updateKitStatus(event.getKitId(), event.getStatus());
    }
}
```

#### Month 7: Logger Management Service
**Objective:** Extract logger and sensor management

**Key Components:**
- ClinicalLoggerEntity and SensorLoggerEntity
- Logger registration and management
- Sensor data collection
- Live data subscriptions

**Event-Driven Architecture:**
```java
@EventListener
public void handleTrialCreated(TrialCreatedEvent event) {
    // Initialize loggers for new trial
    loggerService.initializeTrialLoggers(event.getTrialId());
    
    // Publish logger initialization event
    eventPublisher.publishEvent(new LoggersInitializedEvent(
        event.getTrialId(), 
        initializedLoggerIds
    ));
}
```

#### Month 8: Service Integration & Testing
**Tasks:**
1. Implement event-driven communication between services
2. Set up distributed tracing
3. Performance testing and optimization
4. End-to-end testing scenarios

### Phase 4: Core Business Services (Months 9-12)

#### Month 9: Trial Management Service
**Objective:** Extract trial management core functionality

**Complex Workflows:**
- Trial creation with multiple sites
- Trial approval workflows
- Site management and relationships
- Trial-logger associations

**Saga Pattern Implementation:**
```java
@Component
public class TrialCreationSaga {
    
    @SagaOrchestrationStart
    public void createTrial(CreateTrialCommand command) {
        // Step 1: Create trial
        sagaManager.choreography()
            .step("createTrial")
            .invokeParticipant(trialService)
            .withCompensation(trialService::deleteTrial)
            
            // Step 2: Create sites
            .step("createSites")
            .invokeParticipant(siteService)
            .withCompensation(siteService::deleteSites)
            
            // Step 3: Initialize loggers
            .step("initializeLoggers")
            .invokeParticipant(loggerService)
            .withCompensation(loggerService::removeLoggers)
            
            .execute();
    }
}
```

#### Month 10: Shipment Management Service
**Objective:** Extract shipment and kit management

**Key Features:**
- Shipment tracking and status updates
- Kit inventory management
- Batch management
- Integration with IRT for kit updates

#### Month 11: Deviation Management Service
**Objective:** Extract deviation and adjustment management

**Complex Business Logic:**
- Temperature deviation detection
- Adjustment set creation and approval
- Manual adjustment processing
- Compliance reporting

#### Month 12: Final Integration & Go-Live
**Tasks:**
1. Complete end-to-end testing
2. Performance optimization
3. Production deployment
4. Monitoring and alerting setup
5. Team training and documentation

## Service Communication Patterns

### Synchronous Communication
**Use Cases:**
- User authentication requests
- Real-time data queries
- Configuration lookups

**Implementation:**
```java
@FeignClient(name = "user-service")
public interface UserServiceClient {
    
    @GetMapping("/api/v1/users/{id}")
    UserDto getUser(@PathVariable Long id);
    
    @PostMapping("/api/v1/auth/validate")
    ValidationResult validateToken(@RequestBody String token);
}
```

### Asynchronous Communication
**Use Cases:**
- Audit trail logging
- Notification sending
- Data synchronization
- Business event processing

**Event Publishing:**
```java
@Service
public class TrialService {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public TrialDto createTrial(CreateTrialRequest request) {
        TrialEntity trial = trialRepository.save(mapToEntity(request));
        
        // Publish event asynchronously
        eventPublisher.publishEvent(new TrialCreatedEvent(
            trial.getId(),
            trial.getName(),
            trial.getCompanyId()
        ));
        
        return mapToDto(trial);
    }
}
```

## Data Consistency Strategies

### Eventual Consistency
**Pattern:** Event-driven updates with compensation
**Use Cases:** Non-critical data synchronization

### Strong Consistency
**Pattern:** Distributed transactions with 2PC or Saga
**Use Cases:** Financial transactions, critical business operations

### Read Replicas
**Pattern:** CQRS with read-optimized views
**Use Cases:** Reporting and analytics queries

## Monitoring and Observability

### Metrics Collection
```java
@Component
public class ServiceMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter requestCounter;
    private final Timer responseTimer;
    
    @EventListener
    public void handleRequest(RequestEvent event) {
        requestCounter.increment(
            Tags.of("service", event.getServiceName(),
                   "endpoint", event.getEndpoint())
        );
    }
}
```

### Distributed Tracing
```java
@RestController
public class TrialController {
    
    @NewSpan("create-trial")
    @PostMapping("/trials")
    public TrialDto createTrial(@RequestBody CreateTrialRequest request) {
        Span.current().addEvent("Validating trial request");
        // Business logic
        return trialService.createTrial(request);
    }
}
```

### Health Checks
```java
@Component
public class DatabaseHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // Check database connectivity
            dataSource.getConnection().isValid(5);
            return Health.up()
                .withDetail("database", "Available")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("database", "Unavailable")
                .withException(e)
                .build();
        }
    }
}
```

This implementation guide provides detailed technical specifications for each phase of the migration, including code examples, database schemas, and architectural patterns specific to the clinical module's requirements.
