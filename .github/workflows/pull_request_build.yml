---
  name: Gradle build and test

  on:
    pull_request:
      types: [opened, synchronize, edited, ready_for_review, reopened]
      branches:
        - main

  jobs:
    ci-pr:
      permissions:
        contents: read
        packages: read
        pull-requests: write
      uses: tss-ab/iac-github-actions-workflows/.github/workflows/java-workflows-pr-gradle-build.yml@gradle-v1.0.0
      secrets: inherit
      with:
        runner_label: ubuntu-latest-tss
        gradle_binary: "gradle"
        gradle_version: 7.6
        java_version: "8"
        node_version: 14
        sonarscan: true
        sonarqube_projectKey: TSS-AB_clinical-module_AYxn94FkFvJ7fVQReZG_

        gradle_command_build: build distAndTest collectScanLibs jacocoTestReport
        jacoco_build_report_results: true
