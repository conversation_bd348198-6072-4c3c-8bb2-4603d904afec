# Local Tomcat/CCIS Server Setup

## Setup a separate local Tomcat 8 server
1. Download a [local Tomcat 8 distribution](https://tomcat.apache.org/download-80.cgi), e.g. [the 64 bit version](https://dlcdn.apache.org/tomcat/tomcat-8/v8.5.87/bin/apache-tomcat-8.5.87-windows-x64.zip), unpack it somewhere outside any git/project repos, e.g. `C:\tss\apache-tomcat-8.5.87`
2. Add new local environment variable `CLINICAL_MODULE_HOME` on your system pointing to your clinical module repo base folder, e.g. 
```
CLINICAL_MODULE_HOME=C:\tss\git\clinical-module
```
3. Copy contents of the [tomcat8 repo folder](tomcat8) to the base directory of your local tomcat from step 1, i.e. overwriting the `context.xml`/`server.xml`/`catalina.properties` in the `conf` folder, and creating folders `certificate` on the top level, and `Catalina/localhost` under `conf`.
   - In `catalina.properties`, the important change is to turn on environment variable usage with `org.apache.tomcat.util.digester.PROPERTY_SOURCE=org.apache.tomcat.util.digester.EnvironmentPropertySource`
   - Note that you only need xml files for the modules/contexts you actually want to deploy locally in `conf/Catalina/localhost`, so you can e.g. skip [tomcat8/conf/Catalina/localhost/gateway.xml](tomcat8/conf/Catalina/localhost/gateway.xml) if you won't work with the gateway context.

## Run using IntelliJ
1. Build the contexts/modules you want to deploy using
```
gradle clean war
```
2. Create a new run configuration in IntelliJ
   1. Choose "Add new configuration" -> "Tomcat Server" -> "Local"
   2. Add your local separate Tomcat 8 server as Application Server
   3. Optionally uncheck "Open browser"/ "After Launch"
   4. Check "Deploy applications configured in Tomcat instance
3. Save the run configuration and execute using either "Run" or "Debug" in IntelliJ
   - If `CLINICAL_MODULE_HOME` environment variable is not found, try restarting IntelliJ

   

## Activate JMX Remote Access

1. **Edit the Java Options**:
   - Add the necessary JMX configuration options to the `setenv.sh` file located in `/path/to/tomcat/bin`.
   - Example content for `setenv.sh`:
     ```bash
     CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote \
                                   -Dcom.sun.management.jmxremote.port=7071 \
                                   -Dcom.sun.management.jmxremote.rmi.port=7071 \
                                   -Dcom.sun.management.jmxremote.authenticate=true \
                                   -Dcom.sun.management.jmxremote.ssl=false \
                                   -Djava.rmi.server.hostname=127.0.0.1"
     ```

2. **Create and Modify the `jmxremote.access` and `jmxremote.password` Files**:
   - These files should be created or modified in the `$JAVA_HOME/lib/management/` directory.
   - **`jmxremote.access`**: Defines roles and their access levels.
     ```plaintext
     monitorRole readonly
     controlRole readwrite
     ```
   - **`jmxremote.password`**: Maps usernames to passwords.
     ```plaintext
     monitorRole monitorPassword
     controlRole controlPassword
     ```

3. **Set File Permissions**:
   - Set the permissions of the `jmxremote.password` file to `600` to ensure it is only accessible by the owner.
   - Use the following command:
     ```bash
     chmod 600 /path/to/jmxremote.password
     ```
   - Grant ownership of the file to the Tomcat user (e.g., `tomcat8`):
     ```bash
     chown tomcat8:tomcat8 /path/to/jmxremote.password
     chown tomcat8:tomcat8 /path/to/jmxremote.access
     ```

4. **Restart Tomcat**:
   - Restart Tomcat to apply the changes:
     ```bash
     systemctl start tomcat8.service
     ```
