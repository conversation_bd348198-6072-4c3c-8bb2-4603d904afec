<?xml version='1.0' encoding='utf-8'?>
<Server port="8005" shutdown="SHUTDOWN">

  <Service name="Catalina">

    <Connector port="8080" protocol="HTTP/1.1"
               connectionTimeout="20000"
               redirectPort="8443" />
	
<!-- Creating a new pkcs12 keystore: openssl pkcs12 -export -in local_ccis_se.crt -inkey local.ccis.se.key.txt -out local_ccis_se.p12   -->
	
	<Connector protocol="org.apache.coyote.http11.Http11NioProtocol"
				port="8443" maxThreads="200"
				scheme="https" secure="true" SSLEnabled="true"
				keystoreFile="${catalina.home}/certificate/wildcard.ccis.se.1.pfx" keystorePass="tsswa1234re"
				clientAuth="false" sslProtocol="TLSv1.2"/>

    <Engine name="Catalina" defaultHost="localhost" backgroundProcessorDelay="1">

      <Host name="localhost" unpackWARs="true">
        <Valve className="org.apache.catalina.valves.AccessLogValve" directory="logs"
               prefix="localhost_access_log" suffix=".txt"
               pattern="%h %l %u %t &quot;%r&quot; %s %b" />

      </Host>
    </Engine>
  </Service>
</Server>
