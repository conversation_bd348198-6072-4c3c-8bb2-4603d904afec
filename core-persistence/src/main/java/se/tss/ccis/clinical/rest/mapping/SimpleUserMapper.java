/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.clinical.rest.mapping;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import se.tss.ccis.core.model.user.UserResponseDtoSimple;
import se.tss.ccis.core.persistence.entities.UserEntity;

import static se.tss.ccis.clinical.rest.mapping.UserMapper.*;

@Mapper
public abstract class SimpleUserMapper {
    public static final SimpleUserMapper INSTANCE = Mappers.getMapper(SimpleUserMapper.class);

    /**
     * Auto mapping userEntity to small and simple user dtos
     */
    public abstract UserResponseDtoSimple userEntityToResponseDtoSimple(UserEntity userEntity);

    /**
     * Note will automatically be used for mapping Integers to string by mappers linked to this
     */
    public String userStatusString(Integer userStatusCode) {
        if (userStatusCode == 0) {
            return STATUS_DISABLED;
        } else if (userStatusCode == 1) {
            return STATUS_ACTIVE;
        } else {
            return STATUS_NOT_ACTIVE;
        }
    }
}
