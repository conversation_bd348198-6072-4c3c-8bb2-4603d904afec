/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.core.persistence.managers;

import org.hibernate.criterion.Restrictions;
import se.tss.ccis.core.persistence.entities.*;
import se.tss.ccis.core.persistence.entities.adjustments.AdjustmentSetEntity;
import se.tss.ccis.core.persistence.entities.versioned.TrialEntity;

import java.util.Optional;

/**
 * No dependencies, to allow other managers to easily access entities by id, without spreading dependencies to complex managers,
 * for this simple tasks. It would lead to inevitable cyclic dependencies.
 */
public class ByIdManager extends HibernateManager {

    public Optional<UserEntity> getUserById(long id) {
        return get(UserEntity.class, id);
    }

    public Optional<TrialEntity> getTrialById(long id) {
        return get(TrialEntity.class, id);
    }

    public Optional<SiteEntity> getSiteById(long id) {
        return get(SiteEntity.class, id);
    }

    public Optional<ShipmentEntity> getShipmentById(long id) {
        return get(ShipmentEntity.class, id);
    }

    public Optional<ClinicalLoggerTypeEntity> getLoggerTypeById(long id) {
        return get(ClinicalLoggerTypeEntity.class, id);
    }

    public Optional<LoggerManufacturerEntity> getLoggerManufaturerById(long id) {
        return get(LoggerManufacturerEntity.class, id);
    }

    public Optional<ClinicalLoggerModelEntity> getLoggerModelById(long id) {
        return get(ClinicalLoggerModelEntity.class, id);
    }

    public Optional<TrialUnitEntity> getTrialUnitBySiteAndTrial(long trialId, long siteId) {
        return Optional.ofNullable((TrialUnitEntity) getSession().createCriteria(TrialUnitEntity.class)
                .add(Restrictions.eq(TrialUnitEntity.PROP_SITE + ".id", siteId))
                .add(Restrictions.eq(TrialUnitEntity.PROP_TRIAL + ".id", trialId)).uniqueResult());
    }

    public Optional<AdjustmentSetEntity> getAdjustmentSetById(long id) {
        return get(AdjustmentSetEntity.class, id);
    }

    public Optional<CountryEntity> getCountryById(long id) {
        return get(CountryEntity.class, id);
    }
}
