/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.core.persistence.versioning;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.Criteria;
import org.hibernate.criterion.*;
import se.tss.ccis.core.model.ApprovalStatusMapper;
import se.tss.ccis.core.persistence.entities.UserEntity;
import se.tss.ccis.core.persistence.exceptions.NotFoundException;
import se.tss.ccis.core.persistence.filters.FilterDefinition;
import se.tss.ccis.core.persistence.filters.FilterValues;
import se.tss.ccis.core.persistence.managers.AuditTrailManager;
import se.tss.ccis.core.persistence.managers.ByIdManager;
import se.tss.ccis.core.persistence.managers.NotificationManager;
import se.tss.ccis.core.persistence.managers.clinical.ApprovalStatusManager;
import se.tss.ccis.core.persistence.notification.NotificationAccessName;
import se.tss.ccis.core.persistence.util.CollectionIdUtils;
import se.tss.ccis.core.persistence.util.hibernate.HibernateUtil;
import se.tss.ccis.core.util.functional.StreamsUtil;
import se.tss.ccis.core.util.functional.TriConsumer;

import java.time.Instant;
import java.util.*;
import java.util.function.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static se.tss.ccis.core.persistence.versioning.VersioningUtil.*;

public abstract class VersionManager<
        T extends VersionedEntity<T, V>,
        V extends VersionDraftEntity<V, T>,
        P>
        extends ApprovalStatusManager<V, DraftStatus> {

    protected static final String ALIAS_PARENT_COMPANY = "pppccc";

    private static final Junction USE_PARENT_UPDATED_BY_CONDITION = Restrictions.conjunction()
            .add(Restrictions.eq(VersionDraftEntity.PROP_APPROVAL_STATUS, DraftStatus.APPROVED))
            .add(Restrictions.gtProperty(ALIAS_PARENT + "." + VersionedEntity.PROP_POST_APPROVAL_STATUS_DATE,
                    VersionDraftEntity.PROP_APPROVAL_STATUS_DATE));

    private final FilterDefinition versionUpdatedByFilterDef;
    private final FilterDefinition parentUpdatedByFilterDef;

    public VersionManager(ByIdManager byIdManager, AuditTrailManager auditManager, NotificationManager notificationManager) {
        super(auditManager, notificationManager);
        final Function<Long, Optional<String>> userFullNameProvider = id -> byIdManager.getUserById(id).map(UserEntity::getFullName);
        versionUpdatedByFilterDef = new FilterDefinition(Collections.emptyList(), "versionUpdatedByUser",
                ALIAS_USER + "." + UserEntity.PROP_ID, ALIAS_USER + "." + UserEntity.PROP_FULLNAME, userFullNameProvider);
        parentUpdatedByFilterDef = new FilterDefinition(Collections.emptyList(), "parentUpdatedByUser",
                ALIAS_PARENT_USER + "." + UserEntity.PROP_ID, ALIAS_PARENT_USER + "." + UserEntity.PROP_FULLNAME, userFullNameProvider);

    }

    protected abstract Criteria versionedEntityCriteria(long companyId);

    protected abstract Criteria draftedVersionCriteria(long companyId);

    protected abstract NotFoundException versionedEntityNotFoundException(String msg);

    protected abstract NotFoundException versionNotFoundException(String msg);

    protected abstract IllegalArgumentException duplicateSerialException(String msg);

    protected abstract RuntimeException invalidStatusException(String msg);

    protected abstract void removeDeprecatedDbReferences(V deprecatedVersion);

    protected abstract void throwIfDeactivationNotAllowed(T parent);

    protected abstract Set<String> getEmailRecipients(T parent, NotificationAccessName notificationAccessName);

    protected abstract String parentUrl(T parent);

    protected abstract String versionUrl(V version);

    @Override
    protected boolean isSubmittingNewDraftForReview(boolean nextStep, DraftStatus expectedCurState) {
        return nextStep && expectedCurState == DraftStatus.DRAFT;
    }

    @SuppressWarnings("unchecked")
    public Optional<V> getVersionById(long companyId, long id) {
        return Optional.ofNullable((V) draftedVersionCriteria(companyId).add(Restrictions.eq("id", id)).uniqueResult());
    }

    @SuppressWarnings("unchecked")
    public V getVersionByIdOrThrow(long companyId, long id) {
        return Optional.ofNullable((V) draftedVersionCriteria(companyId).add(Restrictions.eq("id", id)).uniqueResult())
                .orElseThrow(() -> versionNotFoundException("Version not found for id '" + id + "'"));
    }

    /**
     * @throws IllegalArgumentException If a version is not found for every single provided id
     */
    public List<V> getVersionsByIds(long companyId, Set<Long> versionIds) {
        if (versionIds == null || versionIds.isEmpty()) {
            return Collections.emptyList();
        }
        @SuppressWarnings("unchecked") final List<V> foundVersions = draftedVersionCriteria(companyId)
                .add(Restrictions.in(VersionDraftEntity.PROP_ID, versionIds)).list();
        CollectionIdUtils.assertIdCollectionsMatch(foundVersions, versionIds);
        return foundVersions;
    }

    public Optional<T> getVersionedEntityById(long companyId, long id) {
        return uniqueResult(versionedEntityCriteria(companyId).add(Restrictions.eq("id", id)));
    }

    @SuppressWarnings("unchecked")
    public T getVersionedEntityByIdOrThrow(long companyId, long id) {
        return Optional.ofNullable((T) versionedEntityCriteria(companyId).add(Restrictions.eq("id", id))
                .uniqueResult()).orElseThrow(() -> versionedEntityNotFoundException("Entity not found for id '" + id + "'"));
    }

    protected Optional<T> getVersionedEntity(long companyId, String serial) {
        return uniqueResult(versionedEntityCriteria(companyId)
                .add(Restrictions.eq(VersionedEntity.PROP_SERIAL, serial)));
    }

    T getVersionedEntityOrThrow(long companyId, String serial) {
        return getVersionedEntity(companyId, serial).orElseThrow(() ->
                versionedEntityNotFoundException("Entity not found for serial '" + serial + "'"));
    }

    @SuppressWarnings("unchecked")
    protected List<T> getVersionedEntities(long companyId) {
        return versionedEntityCriteria(companyId).list();
    }

    @SuppressWarnings("unchecked")
    protected List<T> getVersionedEntitiesWithStatus(long companyId, PostApprovalStatus status) {
        return versionedEntityCriteria(companyId).add(Restrictions.eq(T.PROP_POST_APPROVAL_STATUS, status)).list();
    }

    List<V> getVersions(long companyId, long parentId) {
        final Optional<T> versionedEntity = getVersionedEntityById(companyId, parentId);
        return versionedEntity.map(VersionedEntity::getVersions).orElse(Collections.emptyList());
    }

    List<V> getVersions(long companyId, long parentId, Predicate<? super V> filter, boolean sortedDesc) {
        Stream<V> stream = getVersions(companyId, parentId).stream();
        if (filter != null)
            stream = stream.filter(filter);
        if (sortedDesc) {
            final Comparator<V> comparator = Comparator.comparingInt(VersionDraftEntity::getVersion);
            stream = stream.sorted(comparator.reversed()); //desc
        }

        stream = stream.sorted((o1, o2) -> Integer.compare(o2.getVersion(), o1.getVersion()));//descending order
        return stream.collect(Collectors.toList());
    }

    protected Optional<V> getVersionWithHighestNumber(long companyId, long parentId) {
        return getVersions(companyId, parentId, null, true).stream().findFirst();
    }

    public Optional<V> getLatestVersion(long companyId, long parentId) {
        return getLatestVersion(getVersionedEntityByIdOrThrow(companyId, parentId));
    }

    public Optional<V> getLatestVersion(T parent) {
        //desc sort by status, enum declared order. then desc secondary sorted by version number
        return parent.getVersions().stream().max(Comparator.comparing(VersionDraftEntity::getVersion));
    }

    public V getLatestVersionOrThrow(T parent) {
        return parent.getLatestVersion();
    }


    Optional<V> getVersionByNumber(long companyId, long parentId, int versionNumber) {
        return getVersions(companyId, parentId, v -> versionNumber == v.getVersion(), false).stream().findFirst();
    }

    V getVersionByNumberOrThrow(long companyId, long parentId, int versionNumber) {
        return getVersionByNumber(companyId, parentId, versionNumber).orElseThrow(() ->
                versionNotFoundException("Version# " + versionNumber + " not found for serial '" + parentId + "'"));
    }

    /**
     * Get latest the latest versions of all versioned objects
     *
     * @return
     */
    public List<V> getLatestVersions(long companyId) {
        return getVersionedEntities(companyId).stream().map(this::getLatestVersionOrThrow).collect(Collectors.toList());
    }

    /**
     * Perform a draft action on a version entity, updating both the version and the parent, if needed.
     * This methods logs these to the audit trail.
     *
     * @param version
     * @param nextStep         the action to perform
     * @param expectedCurState the expected state of the versioned entity BEFORE the action is performed
     * @return the resulting state of the entity AFTER the action was performed
     */
    public V draftApprovalAction(V version, final boolean nextStep, final DraftStatus expectedCurState,
                                 UserEntity updatedByUser) {
        if (!version.getParent().getApprovalStatus().isDraftableStatus()) {
            throw invalidStatusException("Draft actions not allowed when parent in status: " + version.getParent().getApprovalStatus());
        }
        Instant now = Instant.now();
        final V updatedVersion = super.draftApprovalAction(version, nextStep, expectedCurState, updatedByUser, now);
        sendNotification(updatedVersion, nextStep, expectedCurState, updatedByUser);
        return updatedVersion;
    }

    private void sendNotification(V version, boolean isOutcomeApproved, ApprovalStatus prevState, UserEntity updatedByUser) {
        Pair<String, String> urlAndAction = getUrlAndAction(version, prevState);
        prevState.typesForStep(isOutcomeApproved).forEach(type ->
                sendApprovalStepNotification(
                        urlAndAction.getLeft(),
                        urlAndAction.getRight(),
                        version.getParent().entityClassDisplayName(),
                        updatedByUser.getCurrentSignature(),
                        version,
                        notificationAccessNameByType(type),
                        type,
                        isOutcomeApproved,
                        null, null, null //no trial or site written to trial/profile approval notification
                ));

    }

    private Pair<String, String> getUrlAndAction(V version, ApprovalStatus prevState) {
        if (prevState instanceof DraftStatus) {
            switch (((DraftStatus) prevState)) {
                case DRAFT:
                case DRAFT_PENDING_REVIEW:
                case DRAFT_PENDING_APPROVAL:
                    return Pair.of(versionUrl(version), "activation");
                case SUPERSEDED:
                case APPROVED:
                case REJECTED:
                default:
                    throw new IllegalArgumentException("DraftStatus had unexpected value: " + prevState.getClass());
            }
        } else if (prevState instanceof PostApprovalStatus) {
            String action;
            switch (((PostApprovalStatus) prevState)) {
                case NOT_APPROVED:
                case INACTIVATED:
                case REACTIVATION_PENDING_REVIEW:
                case REACTIVATION_PENDING_APPROVAL:
                    action = "activation";
                    break;
                case ACTIVE:
                case INACTIVATION_PENDING_REVIEW:
                case INACTIVATION_PENDING_APPROVAL:
                    action = "inactivation";
                    break;
                default:
                    throw new IllegalArgumentException("PostApprovalStatus had unexpected value: " + prevState.getClass());
            }
            return Pair.of(parentUrl(version.getParent()), action);
        } else {
            throw new IllegalArgumentException("ApprovalStatus was of unexpected class " + prevState.getClass());
        }
    }

    @Override
    protected void onFinalApproval(UserEntity approvalUser, Instant approvalTime, V entity) {
        entity.updateVersionSuffix();
        auditManager.updateWithAuditSuccess(approvalUser, entity.auditAction(),
                String.format("A new version of %s was approved", entity.auditEntityName()), entity.getParent(),
                p -> {
                    supersedeDeprecatedVersions(p, approvalUser);
                    if (p.getApprovalStatus() == PostApprovalStatus.NOT_APPROVED) {
                        changeStatus(p, PostApprovalStatus.NOT_APPROVED, true, approvalUser, approvalTime);
                    }
                });
    }

    /**
     * Perform an action on a versioned entity
     *
     * @param parent           the versioned entity
     * @param nextStep         the action to perform
     * @param expectedCurState the expected state of the versioned entity BEFORE the action is performed
     * @return the resulting state of the entity AFTER the action was performed
     */
    public T postApprovalAction(T parent, final boolean nextStep, final PostApprovalStatus expectedCurState,
                                UserEntity updatedByUser) {
        //If we are trying to start deactivation, make sure we are allowed to do it
        if (isAdvancingDeactivationFlow(nextStep, expectedCurState)) {
            throwIfDeactivationNotAllowed(parent);
        }

        throwIfInvalidStatus(parent, expectedCurState);
        if (expectedCurState == PostApprovalStatus.NOT_APPROVED && nextStep) {
            throw invalidStatusException("Shouldn't postapproval parent from " + PostApprovalStatus.NOT_APPROVED +
                    " to " + PostApprovalStatus.ACTIVE + ", should happen when draft is final approved");
        }
        if (expectedCurState.isStepThatMayRequireOtherUser(nextStep)) {
            validateUserForApproval(updatedByUser, parent);
        }
        V latestVersion = getLatestVersionOrThrow(parent);
        if (latestVersion.getApprovalStatus().isPendingDraft()) {
            throw invalidStatusException("Can't perform post approval action when latest version is in status: " + latestVersion.getApprovalStatus());
        }
        final Instant now = Instant.now();
        auditManager.updateWithAuditSuccess(updatedByUser, parent.auditAction(), "Status change of " +
                parent.auditEntityName(), parent, p -> {
            //If rejecting deactivation, or approving reactivation, new parent status will always be "ACTIVE"
            if (isRejectingDeactivation(nextStep, expectedCurState) || isApprovingReactivation(nextStep, expectedCurState)) {
                applyNewStatusValues(p, PostApprovalStatus.ACTIVE, updatedByUser, now);
            } else {
                changeStatus(p, expectedCurState, nextStep, updatedByUser, now);
            }
        });
        sendNotification(parent.getLatestVersion(), nextStep, expectedCurState, updatedByUser);
        return parent;
    }

    private boolean isRejectingDeactivation(boolean nextStep, PostApprovalStatus expectedCurState) {
        return !nextStep &&
                (expectedCurState == PostApprovalStatus.INACTIVATION_PENDING_REVIEW
                        || expectedCurState == PostApprovalStatus.INACTIVATION_PENDING_APPROVAL);
    }

    private boolean isApprovingReactivation(boolean nextStep, PostApprovalStatus expectedCurState) {
        return nextStep && expectedCurState == PostApprovalStatus.REACTIVATION_PENDING_APPROVAL;
    }

    protected boolean isAdvancingDeactivationFlow(boolean nextStep, PostApprovalStatus expectedCurState) {
        return nextStep && (expectedCurState == PostApprovalStatus.ACTIVE ||
                expectedCurState == PostApprovalStatus.INACTIVATION_PENDING_REVIEW ||
                expectedCurState == PostApprovalStatus.INACTIVATION_PENDING_APPROVAL);
    }

    /**
     * Set older parent's versions to inactive.
     * Does not log to audit trail.
     *
     * @param parent
     * @param updatedByUser
     */
    private void supersedeDeprecatedVersions(T parent, UserEntity updatedByUser) {
        //Make all versions of the entity inactive
        final List<V> versions = parent.getVersions();
        for (int i = 1; i < versions.size(); i++) { //do not inactivate highest version
            if (versions.get(i).getApprovalStatus() != DraftStatus.REJECTED &&
                    versions.get(i).getApprovalStatus() != DraftStatus.SUPERSEDED) {
                updateStatusEntity(versions.get(i), DraftStatus.SUPERSEDED, updatedByUser, Instant.now());
            }
        }
    }

    /**
     * Like {@link #createNewVersionedEntity(VersionedEntity, VersionDraftEntity, UserEntity, Consumer)}, without custom action
     */
    protected V createNewVersionedEntity(T versionedEntity, V initalVersion, UserEntity updatedByUser) {
        if (versionedEntity.getSerial() == null) {
            throw new IllegalArgumentException("Can't create new versioned entity: serial must be set");
        }
        initalVersion.setParent(versionedEntity);
        return saveNewVersionedEntity(updatedByUser.getCompanyId(), versionedEntity, initalVersion, updatedByUser, a -> {
        });
    }

    /**
     * @param versionedEntity must have serial set
     * @param initalVersion
     * @param creationAction  addition actions to perform on the new version, after it is saved, but before the creation
     *                        is logged to the audittrail
     * @return
     */
    protected V createNewVersionedEntity(T versionedEntity, V initalVersion, UserEntity updatedByUser,
                                         Consumer<V> creationAction) {
        if (versionedEntity.getSerial() == null) {
            throw new IllegalArgumentException("Can't create new versioned entity: serial must be set");
        }
        initalVersion.setParent(versionedEntity);
        return saveNewVersionedEntity(updatedByUser.getCompanyId(), versionedEntity, initalVersion, updatedByUser, creationAction);
    }

    protected V saveDraft(long versionedId, V draft, V ofVersion, UserEntity updatedByUser, TriConsumer<Boolean, V, V> onUpdateCallback) {
        final T versionedEntity = getVersionedEntityByIdOrThrow(updatedByUser.getCompanyId(), versionedId);
        draft.setParent(versionedEntity);
        return saveNewDraftVersion(updatedByUser.getCompanyId(), draft, ofVersion, updatedByUser, onUpdateCallback);
    }

    /**
     * Save a draft version of a VersionEntity
     *
     * @param ofVersion if creating a draft of a previous version, specify it here, otherwise set to null
     * @param draft     the entity to save. Version and status will be set automatically by this method
     * @return the new version number of the inserted VersioHistory.
     */
    private V saveNewDraftVersion(long companyId, V draft, V ofVersion, UserEntity updatedByUser, TriConsumer<Boolean, V, V> onUpdateCallback) {
        final T parent = draft.getParent();

        if (!parent.isDraftUpdatable()) {
            throw invalidStatusException("Can't create/update draft, parent (" +
                    parent.getSerial() + ") or its versions statuses does not allow it");
        }

        final V highestVersion = getVersionWithHighestNumber(companyId, parent.getId()).orElseThrow(() ->
                new IllegalStateException("Entity with serial '" + parent.getId() + "' does not have any versions"));
        boolean isNewVersion = highestVersion.getApprovalStatus() != DraftStatus.DRAFT;
        //if the latest version is of status 'DRAFT', overwrite that version, otherwise create a new version
        final int newVersionNumber = isNewVersion ? highestVersion.getVersion() + 1 : highestVersion.getVersion();

        draft.setVersion(newVersionNumber);

        if (isNewVersion) {
            final Instant timestamp = Instant.now();
            auditManager.updateWithAuditSuccess(updatedByUser, parent.auditAction(),
                    "Adding new version to " + parent.auditEntityName(), parent, p -> {
                        final List<V> versions = new ArrayList<>(p.getVersions());
                        versions.add(0, draft);
                        p.setVersions(versions);
                        onUpdateCallback.accept(true, ofVersion != null ? ofVersion : highestVersion, draft);
                        //save draft after it has been added to parents version-list, e.g. to get correct version suffix in audit trail
                        saveVersion(draft, DraftStatus.DRAFT, updatedByUser, timestamp, v -> {/*noop*/});
                    });
        } else {
            draft.updateVersionSuffix(); //need to update the versionSuffix already here, since it's in the audit message
            auditManager.mergeWithAuditSuccess(updatedByUser, draft.auditAction(),
                    "Update draft of " + draft.auditEntityName(), highestVersion, draft, (oldV, newV) -> {
                        removeDeprecatedDbReferences(oldV);
                        applyNewStatusValues(newV, DraftStatus.DRAFT, updatedByUser, Instant.now());
                        onUpdateCallback.accept(false, oldV, newV);
                    });
        }
        return draft;
    }

    /**
     * Saves a version entity, and also updates its versionSuffix
     */
    private long saveVersion(V version, DraftStatus status, UserEntity user, Instant timestamp, Consumer<V> additionalAction) {
        version.updateVersionSuffix();
        return saveNewStatusEntity(version, status, user, timestamp, additionalAction);
    }

    /**
     * Create a new VersionedEntity with a first version
     *
     * @param parent the entity to save. Version and status will be set automcatically by this method
     *               (based on the status of @param firstVersion
     * @return the new version number of the inserted VersioHistory.
     */
    protected V saveNewVersionedEntity(long companyId, T parent, V firstVersion,
                                       UserEntity updatedByUser, Consumer<V> creationAction) {
        if (getVersionedEntity(companyId, parent.getSerial()).isPresent()) {
            throw duplicateSerialException("VersionedEntity with serial " + parent.getSerial() + " already exists");
        }
        if (firstVersion.getApprovalStatus() == null || firstVersion.getApprovalStatus() == DraftStatus.SUPERSEDED) {
            throw invalidStatusException("Invalid status of first version draft: " + firstVersion.getApprovalStatus());
        }

        parent.setVersions(Collections.singletonList(firstVersion));
        firstVersion.setVersion(1);
        final Instant now = Instant.now();
        firstVersion.updateVersionSuffix();
        saveNewStatusEntity(parent,
                ApprovalStatusMapper.INSTANCE.draftApprovalToPostStatus(firstVersion.getApprovalStatus()),
                updatedByUser, now);
        saveVersion(firstVersion, firstVersion.getApprovalStatus(), updatedByUser, now, creationAction);
        return firstVersion;
    }

    /**
     * @param version
     * @return the PostApprovalStatus user if the profile is approved, otherwise the DraftStatus user
     */
    protected UserEntity draftOrApprovedUser(V version) {
        return draftOrApprovedEntity(version).getUpdatedByUser();
    }

    public static <V extends VersionDraftEntity<?, ?>> ApprovalStatusEntity draftOrApprovedEntity(V profile) {
        return profile.getApprovalStatus() == DraftStatus.APPROVED ?
                profile.getParent() : profile;
    }

    protected <O extends Comparable<O>> int compare(O o1, O o2, boolean asc) {
        if (o1 == null && o2 == null) {
            return 0;
        } else if (o1 == null) {
            return asc ? -1 : 1;
        } else if (o2 == null) {
            return asc ? 1 : -1;
        }
        return asc ? o1.compareTo(o2) : o2.compareTo(o1);
    }

    protected int compareIgnoreCase(String o1, String o2, boolean asc) {
        if (o1 == null && o2 == null) {
            return 0;
        } else if (o1 == null) {
            return asc ? -1 : 1;
        } else if (o2 == null) {
            return asc ? 1 : -1;
        }
        return asc ? o1.compareToIgnoreCase(o2) : o2.compareToIgnoreCase(o1);
    }

    protected boolean equalsIfNotNull(Object nullable, Object comparing) {
        return nullable == null || nullable.equals(comparing);
    }

    protected <E> boolean containsIfNotNullOrEmpty(Collection<E> nullableList, E comparing) {
        return nullableList == null || nullableList.isEmpty() || nullableList.contains(comparing);
    }

    protected boolean filterCompleteApprovalStatuses(V version, Collection<CompleteApprovalStatus> statuses) {
        if (statuses == null || statuses.isEmpty()) {
            return true;
        }
        final Set<DraftStatus> draftStatuses = ApprovalStatusMapper.INSTANCE.completeToDraftStatuses(statuses);
        final Set<PostApprovalStatus> postStatuses = ApprovalStatusMapper.INSTANCE.completeToPostApprovalStatuses(statuses);
        return draftStatuses.contains(version.getApprovalStatus()) ||
                (version.isLatestVersion() && postStatuses.contains(version.getParent().getApprovalStatus()));
    }

    protected UserEntity latestUser(V profile) {
        final java.util.Date postApprovalStatusDate = profile.getParent().getApprovalStatusDate();
        final java.util.Date draftStatusDate = profile.getApprovalStatusDate();
        if (postApprovalStatusDate != null && draftStatusDate != null) {
            if (postApprovalStatusDate.after(draftStatusDate)) { //the latest of the draft/approval versions will be used
                return profile.getParent().getUpdatedByUser();
            } else {
                return profile.getUpdatedByUser();
            }
        } else if (postApprovalStatusDate != null) {
            return profile.getParent().getUpdatedByUser();
        } else if (draftStatusDate != null) {
            return profile.getUpdatedByUser();
        } else {
            return null;
        }
    }

    protected boolean searchQueryMatching(String nullableSearchQuery, String... nullableFields) {
        final String trimmedQuery = StringUtils.trimToEmpty(nullableSearchQuery);
        if (trimmedQuery.isEmpty()) {
            return true;
        } else {
            for (String field : nullableFields) {
                if (field != null && field.toLowerCase().contains(nullableSearchQuery.toLowerCase())) {
                    return true;
                }
            }
            return false;
        }
    }

    protected Pair<Long, List<V>> paginatedResponse(List<V> filteredVersions, P sortByParam,
                                                    boolean sortDesc, Integer offset, Integer limit,
                                                    BiFunction<P, Boolean, Comparator<V>> versionSorter) {
        final long filteredCount = (long) filteredVersions.size();
        Stream<V> filteredStream = filteredVersions.stream();
        if (sortByParam != null) {
            boolean asc = !sortDesc;

            filteredStream = filteredStream.sorted(versionSorter.apply(sortByParam, asc));
        }
        if (offset != null) {
            int lastPageOffset = limit == null || limit == 0 ? 0 : (int) (filteredCount - filteredCount % limit);
            final int adjustedOffset = offset <= filteredCount ? offset : lastPageOffset;
            filteredStream = filteredStream.skip(adjustedOffset);
        }
        if (limit != null) {
            filteredStream = filteredStream.limit(limit);
        }
        return Pair.of(filteredCount, filteredStream.collect(Collectors.toList()));
    }

    protected Criteria createBaseVersionCriteria(
            long companyId, String serial, Integer versionNumber, ApprovalStatusFilter statusFilter,
            Set<Long> updatedByUserIds, boolean onlyActiveParent, boolean includeSuperseded,
            Supplier<Criterion> additionalRestrictions) {
        final Criteria c = draftedVersionCriteria(companyId);

        if (serial != null) {
            c.add(Restrictions.eq(ALIAS_PARENT + "." + VersionedEntity.PROP_SERIAL, serial));
        }
        if (versionNumber != null) {
            c.add(Restrictions.eq(VersionDraftEntity.PROP_VERSION, versionNumber));
        }

        if (!includeSuperseded) {
            c.add(Restrictions.ne(VersionDraftEntity.PROP_APPROVAL_STATUS, DraftStatus.SUPERSEDED));
        }

        if (onlyActiveParent) {
            c.add(Restrictions.in(ALIAS_PARENT + "." + VersionedEntity.PROP_POST_APPROVAL_STATUS, PostApprovalStatus.ACTIVE_STATUSES));
        }

        if (statusFilter != null) {
            statusFilter.apply(c);
        }

        if (updatedByUserIds != null && !updatedByUserIds.isEmpty()) {
            final Criterion versionUserFilter = Restrictions.conjunction()
                    .add(Restrictions.in(ALIAS_USER + "." + UserEntity.PROP_ID, updatedByUserIds))
                    .add(Restrictions.not(USE_PARENT_UPDATED_BY_CONDITION));
            final Criterion parentUserFilter = Restrictions.conjunction()
                    .add(Restrictions.in(ALIAS_PARENT_USER + "." + UserEntity.PROP_ID, updatedByUserIds))
                    .add(USE_PARENT_UPDATED_BY_CONDITION);
            c.add(Restrictions.disjunction().add(versionUserFilter).add(parentUserFilter));
        }

        if (additionalRestrictions != null) {
            c.add(additionalRestrictions.get());
        }
        return c;
    }

    protected Pair<Pair<Long, List<V>>, List<FilterValues>> searchAllVersions(UserEntity curUser, Order order, Integer offset,
                                                                              Integer limit, String searchQuery,
                                                                              String serial, Integer versionNumber,
                                                                              String name, ApprovalStatusFilter statusFilter,
                                                                              Set<Long> updatedByUserIds,
                                                                              boolean onlyActiveParent, boolean includeSuperseded) {
        FilterValues updatedByFilterOption = findVersionListUpdatedByFilterValues(curUser, searchQuery, serial,
                versionNumber, name, statusFilter, updatedByUserIds, onlyActiveParent, includeSuperseded);
        Criteria searchCriteria = createBaseVersionSearchQuery(curUser, searchQuery, serial, versionNumber,
                name, statusFilter, updatedByUserIds, onlyActiveParent, includeSuperseded);

        final Pair<Long, List<V>> searchResults =
                HibernateUtil.paginatedResponse(searchCriteria,
                        order != null ? Collections.singletonList(order) : Collections.emptyList(),
                        offset, limit, null);
        return Pair.of(searchResults, Collections.singletonList(updatedByFilterOption));
    }

    protected abstract Criteria createBaseVersionSearchQuery(UserEntity curUser, String searchQuery, String profileSerial,
                                                             Integer versionNumber, String profileName,
                                                             ApprovalStatusFilter statusFilter, Set<Long> updatedByUserIds,
                                                             boolean onlyActiveParent, boolean includeSuperseded);

    private FilterValues findVersionListUpdatedByFilterValues(UserEntity curUser, String searchQuery, String serial,
                                                              Integer versionNumber, String name,
                                                              ApprovalStatusFilter statusFilter, Set<Long> updatedByUserIds,
                                                              boolean onlyActiveParent, boolean includeSuperseded) {
        //Need to recreate Criteria for each set of filters...
        Supplier<Criteria> criteriaFn = () -> createBaseVersionSearchQuery(curUser, searchQuery, serial,
                versionNumber, name, statusFilter, updatedByUserIds, onlyActiveParent, includeSuperseded);

        Stream<Pair<Long, String>> pResult = filterResults(criteriaFn, parentUpdatedByFilterDef,
                USE_PARENT_UPDATED_BY_CONDITION);
        Stream<Pair<Long, String>> vResult = filterResults(criteriaFn, versionUpdatedByFilterDef,
                Restrictions.not(USE_PARENT_UPDATED_BY_CONDITION));

        //merge parent users and version users
        return new FilterValues("updatedByUser",
                Stream.concat(vResult, pResult)
                        .filter(StreamsUtil.distinctByKey(Pair::getKey))
                        .collect(Collectors.toList()));
    }

    private Stream<Pair<Long, String>> filterResults(Supplier<Criteria> criteriaFn, FilterDefinition filterDef,
                                                     Criterion additionalCondition) {
        final Criteria c = criteriaFn.get();
        ProjectionList vUserFilterValues = Projections.projectionList();
        vUserFilterValues.add(Projections.distinct(Projections.property(filterDef.getIdProperty())));
        vUserFilterValues.add(Projections.property(filterDef.getNameProperty()));
        c.setProjection(vUserFilterValues);
        c.add(additionalCondition);

        @SuppressWarnings("unchecked")
        List<Object[]> vHibernateRes = c.list();
        return vHibernateRes.stream().map(v -> Pair.of((Long) v[0], v[1] == null ? null : v[1].toString()));
    }
}
