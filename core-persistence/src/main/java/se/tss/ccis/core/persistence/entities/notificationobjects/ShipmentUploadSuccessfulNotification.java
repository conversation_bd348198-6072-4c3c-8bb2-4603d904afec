/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.core.persistence.entities.notificationobjects;

import se.tss.ccis.core.persistence.entities.ClinicalLoggerMissionEntity;
import se.tss.ccis.core.persistence.entities.ShipmentEntity;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@DiscriminatorValue("shipmentUploadSuccessful")
public class ShipmentUploadSuccessfulNotification extends NotificationEntity {

    public void buildContent(ShipmentEntity shipment, List<ClinicalLoggerMissionEntity> missions,
                             boolean excursionsDetected) {
        setEmailContent(contentBuilder()
                .put("shipmentNumber", shipment.getShipmentNo())
                .put("hasExcursions", excursionsDetected)
                .put("loggerAndProducts", getPrintableLoggersAndProducts(missions))
                .build());
    }

    @Override
    public EmailType getEmailType() {
        return EmailType.BCC;
    }

    /**
     *  Return a comma-separated list of products for all shipment loggers that were uploaded
     *  Example: ["AB289738: Ipren, Alvedon", "CD917281: Voltaren, Tylenol"]
     */
    private List<String> getPrintableLoggersAndProducts(List<ClinicalLoggerMissionEntity> missions) {
        return missions.stream()
                .map(mission -> mission.getSerialNumber() + ": " +
                        mission.getDispensingUnitLinks().stream()
                                .map(link -> link.getDispensingUnit().getUnblindedBatch().getBatch().getPackType())
                                .distinct()
                                .collect(Collectors.joining(", ")))
                .collect(Collectors.toList());
    }

}
