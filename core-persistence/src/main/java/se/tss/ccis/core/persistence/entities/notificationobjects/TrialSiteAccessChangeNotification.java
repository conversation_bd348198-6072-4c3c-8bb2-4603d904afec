/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.core.persistence.entities.notificationobjects;

import se.tss.ccis.core.persistence.entities.UserEntity;
import se.tss.ccis.core.persistence.entities.UserTrialSiteAccessEntity;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@DiscriminatorValue("trialSiteAccessChange")
public class TrialSiteAccessChangeNotification extends NotificationEntity {

    public void buildContent(UserEntity user, List<UserTrialSiteAccessEntity> approvedAccesses,
                             List<UserTrialSiteAccessEntity> removedAccesses) {
        setEmailContent(contentBuilder()
                .put("fullName", user.getFullName())
                .put("trialSiteChangesDescriptions", getTrialSiteChangesDescriptions(approvedAccesses, removedAccesses))
                .build());
    }

    @Override
    public EmailType getEmailType() {
        return EmailType.TO;
    }

    private List<String> getTrialSiteChangesDescriptions(List<UserTrialSiteAccessEntity> approvedAccesses,
                                                         List<UserTrialSiteAccessEntity> removedAccesses) {
        List<String> messageList = new ArrayList<>();
        messageList.addAll(getAccessDescription(approvedAccesses, true));
        messageList.addAll(getAccessDescription(removedAccesses, false));
        return messageList;
    }

    private List<String> getAccessDescription(List<UserTrialSiteAccessEntity> tsaList, boolean isApproved) {
        return tsaList.stream()
                .map(tsa -> String.format("Your access to %s / %s (%s) has been %s.",
                        tsa.getTrial() == null ? "All trials" : tsa.getTrial().getName(),
                        tsa.getSite() == null ? "All sites" : tsa.getSite().getSiteNumber(),
                        tsa.getOffice() == null ? "Global" : tsa.getOffice().getName(),
                        isApproved ? "approved" : "removed"))
                .collect(Collectors.toList());
    }

}
