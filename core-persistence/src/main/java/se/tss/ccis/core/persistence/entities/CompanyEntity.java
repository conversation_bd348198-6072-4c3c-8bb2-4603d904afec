/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.core.persistence.entities;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import se.tss.ccis.core.model.temperature.Temperature;
import se.tss.ccis.core.persistence.notification.NotificationTask;
import se.tss.ccis.core.persistence.y.interfaces.AuditTrail;

import javax.persistence.*;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Setter
@Getter
@NoArgsConstructor
@Entity
@Table(name = "ccis_company")
public class CompanyEntity extends AuditablePrimaryKeyModel implements Serializable, AuditTrail {
    private static final long serialVersionUID = -2037421397820452L;

    public static final String PROP_NAME = "name";
    public static final Integer FIRST_MISSING_UPLOAD_DISABLE_LIMIT = 0;
    public static final Integer SECOND_MISSING_UPLOAD_DISABLE_LIMIT = 0;
    public static final Integer FIRST_LOGGER_EXPIRY_DISABLE_LIMIT = 0;
    public static final Integer SECOND_LOGGER_EXPIRY_DISABLE_LIMIT = -1;
    public static final Integer MISSING_AUTOMATED_UPLOAD_DISABLE_LIMIT = 0;

    public static final String DEFAULT_HELPDESK_CONTACT_INFO = "If you require Customer Service or Technical Support," +
            " please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>";

    @Column(nullable = false)
    private Long countryId;

    @Column(nullable = false)
    private String tvnId;

    @Column(nullable = true)
    private String name;

    @Column(nullable = true)
    private String abbreviation;

    @Column(nullable = false, columnDefinition = "int not null default 20")
    private int sessionTimeout = 20;

    @Column(nullable = false, columnDefinition = "int not null default 90")
    private int passwordChange = 90;

    @Column(nullable = false, columnDefinition = "int not null default 0")
    private int loggerExpiryWarning = FIRST_LOGGER_EXPIRY_DISABLE_LIMIT;

    @Column(nullable = false, columnDefinition = "int not null default -1")
    private int loggerSecondExpiryWarning = SECOND_LOGGER_EXPIRY_DISABLE_LIMIT;

    @Column(nullable = true)
    private Integer temperatureId = Temperature.CELSIUS.getId();

    @Column(nullable = false, columnDefinition = "varchar(32) not null default 'yyyy-MM-dd HH:mm'")
    private String timeFormat = "yyyy-MM-dd HH:mm";

    @Column(nullable = false, columnDefinition = "int not null default 1")
    private int passwordStrength = 1;

    @Column(nullable = false, columnDefinition = "int not null default 1")
    private int passwordReusal = 1;

    @Column(nullable = false, columnDefinition = "int not null default 1")
    private int recoverPasswordOption = 1;

    @Column(nullable = false, columnDefinition = "TINYINT(1)")
    private boolean timeFormatLocked;

    @Column(nullable = false, columnDefinition = "TINYINT(1)")
    private boolean temperatureScaleLocked;

    @Column(nullable = false, columnDefinition = "TINYINT(1)")
    private boolean usePasswordRecoveryApproval;

    @Column(nullable = false, columnDefinition = "TINYINT(1)")
    private boolean passwordApprovalActivated;

    /**
     * The number of months of inactivity until the users will be disabled. 0 disables this
     */
    @Column(nullable = false, columnDefinition = "INT(1) NOT NULL DEFAULT 0")
    private Integer disableInactiveUsers = 0;

    /**
     * The number of hours that a temporary password will be valid in the system. 0 disables this
     */
    @Column(nullable = false, columnDefinition = "INT(1) NOT NULL DEFAULT 0")
    private Integer disableTempPassword = 0;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "countryId", updatable = false, insertable = false)
    private CountryEntity country;

    @ManyToMany(mappedBy = "passwordRecoveryCompanySet", fetch = FetchType.LAZY)
    private Set<UserEntity> passwordRecoveryCompanySet = new HashSet<>();

    @Column(nullable = true, columnDefinition = "longblob")
    private byte[] splashLogo;

    @Column(nullable = false, columnDefinition = "VARCHAR(255) not null default '" + DEFAULT_HELPDESK_CONTACT_INFO + "'")
    private String helpdeskContactInfo = DEFAULT_HELPDESK_CONTACT_INFO;

    @Column(nullable = true, columnDefinition = "VARCHAR(255)")
    private String helpURL;

    @Column(nullable = false, columnDefinition = "tinyint not null default 1")
    private boolean saveUsernameToAuditAndSignatures = true;

    @Column(nullable = false, columnDefinition = "tinyint not null default 0")
    private boolean manualTrialCreation = Boolean.FALSE;

    @Column(nullable = false, columnDefinition = "tinyint not null default 0")
    private boolean validateLoggerId = Boolean.FALSE;

    @Column(nullable = false, columnDefinition = "int not null default 0")
    private int missingStorageLoggerUploadTimeoutDays = FIRST_MISSING_UPLOAD_DISABLE_LIMIT; //first timeout interval

    @Column(nullable = false, columnDefinition = "int not null default 0")
    private int secondMissingStorageLoggerUploadTimeoutDays = SECOND_MISSING_UPLOAD_DISABLE_LIMIT; //second timeout interval

    @Column(nullable = false, columnDefinition = "TINYINT(4) not null default 4")
    private int missingAutomatedUploadTimeoutHours = 4;

    @Column(nullable = false, columnDefinition = "int unsigned not null default 15")
    private int maxAllowedMeasurementGapTimeMinutes = 15;

    @Column(nullable = false, columnDefinition = "int unsigned not null default 15")
    private int dispensingUnitMinExcursionTimeMinutes = 15;

    @PositiveOrZero
    @Column(name = "default_shipment_narrower_interval_margin", nullable = false, columnDefinition = "DOUBLE unsigned not null default 0.5")
    private double defaultShipmentNarrowIntervalMargin = 0.5;

    @Column(length = 64)
    private String systemInformationHeading;

    @Column(length = 512)
    private String systemInformationMessage;

    @Column(name = "auto_sync_imported_users", nullable = false, columnDefinition = "TINYINT(1) default 0")
    private boolean autoSyncImportedSsoUsers = false;

    @ManyToOne
    @JoinColumn(name = "manually_approved_user_access_group_id")
    private AccessGroupEntity manuallyApprovedUserGroup;

    @Column(name = "email_base_template", columnDefinition = "text")
    private String emailBaseTemplate;

    @Column(name = "email_subject_prefix", columnDefinition = "text")
    private String emailSubjectPrefix;

    @OneToMany(mappedBy = LoggerModelCompanyConfigEntity.PROP_COMPANY, fetch = FetchType.LAZY)
    private List<LoggerModelCompanyConfigEntity> loggerModelConfigs = new ArrayList<>();

    @ManyToOne
    @JoinColumn(name = "sms_server_id")
    private NotificationServerEntity smsServer;

    @ManyToOne
    @JoinColumn(name = "default_irt_integration_id")
    private IrtIntegrationDetailsEntity defaultIrtIntegration;

    @Column(name = "exact_pending_shipment_match_req", nullable = false, columnDefinition = "tinyint not null default 0")
    private boolean exactPendingShipmentMatchRequired = false;

    @Column(nullable = false, name = "max_concurrent_launch", columnDefinition = "int unsigned not null default 20")
    private int maxConcurrentLaunch = 20;

    @Column(nullable = false, columnDefinition = "int not null default 0")
    private int launchExpiryDateThresholdDays = 0;

    @Column(name = "user_sso_unlink_enabled", nullable = false, columnDefinition = "tinyint not null default 0")
    private boolean userSsoUnlinkEnabled = false;

    @Column(name = "user_manager_sso_options_enabled", nullable = false, columnDefinition = "tinyint not null default 0")
    private boolean userManagerSsoOptionsEnabled = false;

    @Column(name = "default_allow_extended_stability", nullable = false, columnDefinition = "tinyint not null default 0")
    private boolean defaultAllowExtendedStability = false;

    @OneToOne(mappedBy = "company", cascade = CascadeType.ALL)
    private LoggerReceiverSettingsEntity loggerReceiverSettings;

    public CompanyEntity(String tvnId) {
        this.tvnId = tvnId;
    }


    public boolean hasFirstLoggerExpiryWarning() {
        return getLoggerExpiryWarning() > FIRST_LOGGER_EXPIRY_DISABLE_LIMIT;
    }

    public boolean hasSecondLoggerExpiryWarning() {
        return getLoggerSecondExpiryWarning() > SECOND_LOGGER_EXPIRY_DISABLE_LIMIT;
    }

    @Transient
    public boolean isNotificationTaskEnabled(NotificationTask notificationTask) {
        switch (notificationTask) {
            case LOGGER_EXPIRY:
                return hasFirstLoggerExpiryWarning() || hasSecondLoggerExpiryWarning();
            case MISSING_UPLOAD:
                return hasFirstMissingStorageLoggerUpload() || hasSecondMissingStorageLoggerUpload();
            case LOGGER_MISSING_AUTOMATED_UPLOAD:
                return missingAutomatedUploadWarningsEnabled();
            default:
                return false;
        }
    }


    public boolean hasFirstMissingStorageLoggerUpload() {
        return getMissingStorageLoggerUploadTimeoutDays() > FIRST_MISSING_UPLOAD_DISABLE_LIMIT;
    }

    public boolean missingAutomatedUploadWarningsEnabled() {
        return getMissingAutomatedUploadTimeoutHours() > MISSING_AUTOMATED_UPLOAD_DISABLE_LIMIT;
    }

    public boolean hasSecondMissingStorageLoggerUpload() {
        return getSecondMissingStorageLoggerUploadTimeoutDays() > SECOND_MISSING_UPLOAD_DISABLE_LIMIT;
    }

    @Transient
    public boolean doesGroupRequiresManualApproval(AccessGroupEntity accessGroup) {
        return manuallyApprovedUserGroup != null &&
                manuallyApprovedUserGroup.getId().equals(accessGroup.getId());
    }


    @Override
    public String toString() {
        return "company=" + name;
    }

    @Override
    public String flatAuditString() {
        return getName();
    }
}
