/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.updatedb;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class SetupDatabaseFor243 {
    Connection conn;

    public static void main(String[] args) {
        new SetupDatabaseFor243();
    }

    public SetupDatabaseFor243() {
        try {
            Class.forName("com.mysql.jdbc.Driver").newInstance();
            String url = "**************************************";
            conn = DriverManager.getConnection(url, "root", "n30t3c4");
            doUpgrade();
            conn.close();
        } catch (ClassNotFoundException ex) {
            System.err.println(ex.getMessage());
        } catch (IllegalAccessException ex) {
            System.err.println(ex.getMessage());
        } catch (InstantiationException ex) {
            System.err.println(ex.getMessage());
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        }
    }

    private void doUpgrade() {
        System.out.println("Starting upgrade, Step 1 checking.");


   
        addToolTips();
        
        updateTables();
		
        updateProcedure();
        
        updateAccessTable();
        
        updateColumnDefinitions();
        updateCompanyTable();
        updateCountryTable();
        alterTables();
		
        updateIncidentDataTable();
        updateIncidentNavigationPanel();
        updateIncidentFieldAccessRights();
        updateIncidentLifeCycle();
        updateIncidentReportAvailableFields();
        updateReportsIncidentsFieldsInfo();
        updateReportsQueryConfiguration();
        updateReportsChartDrilldownConfiguration();
        updateReportsIncidentsUITabInfo();
        updateIncidentEMailConfiguration();
		
        deleteTooltips();
        deleteAccess();




    }

    private void updateIncidentDataTable()
    {
        executeUpdate("update incident_data id\n" +
                "join ccis_user cu on cu.username = id.created_by \n" +
                "join ccis_company cc on cc.id = cu.companyId \n" +
                "set id.tvn_id = cc.tvnId");
    }

    private void updateIncidentEMailConfiguration()
    {

        insertIntoTable("reports_query_configuration", "INSERT INTO `incident_mail_configuration` (`id`,`action_name`,`from_state`,`mail_body`,`mail_subject`,`role_name`,`to_state`,`mail_receiver`) VALUES (1,'Update','Incident Report','Hello,<br/><br/>Incident has been updated by \\'aUserName\\' and its id is : aClaimId <br/><br/>To view details of this Incident, please click the following link:<br/><br/>https://aHost/redirect.action?nextURI=aURLPrefix/process-Incident/tratSniaClaimIddnEni/edit<br/><br/>From,<br/>TSS-Incident Management System<br/>-----------------------------------------------------------<br/>DO NOT REPLY TO THIS MESSAGE. This email account is for sending messages only.<br/>If you require Customer Service or Technical Support, please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>','Incident Updated','claims_reporter','Incident Report','owner')");
        insertIntoTable("reports_query_configuration", "INSERT INTO `incident_mail_configuration` (`id`,`action_name`,`from_state`,`mail_body`,`mail_subject`,`role_name`,`to_state`,`mail_receiver`) VALUES (2,'','','Hello,<br/><br/>New Incident Report has been created by \\'aUserName\\' and its id is : aClaimId <br/><br/>To view details of this Incident Report, please click the following link:<br/><br/>https://aHost/redirect.action?nextURI=aURLPrefix/incidents/tratSniaClaimIddnEni/edit<br/><br/>From,<br/>TSS-Incident Management System<br/>-----------------------------------------------------------<br/>DO NOT REPLY TO THIS MESSAGE. This email account is for sending messages only.<br/>If you require Customer Service or Technical Support, please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>','New Incident Report','claims_reporter','Incident Report','101')");
        insertIntoTable("reports_query_configuration", "INSERT INTO `incident_mail_configuration` (`id`,`action_name`,`from_state`,`mail_body`,`mail_subject`,`role_name`,`to_state`,`mail_receiver`) VALUES (3,'Notify Insurance','Claim','Hello,<br/><br/>New Claim has been created by \\'aUserName\\' and its id is : aClaimId <br/><br/>To view details of this Claim, please click the following link:<br/><br/>https://aHost/redirect.action?nextURI=aURLPrefix/incidents/tratSniaClaimIddnEni/edit<br/><br/>From,<br/>TSS-Incident Management System<br/>-----------------------------------------------------------<br/>DO NOT REPLY TO THIS MESSAGE. This email account is for sending messages only.<br/>If you require Customer Service or Technical Support, please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>','New Claim','claims_manager','Outstanding Claim','103')");
        insertIntoTable("reports_query_configuration", "INSERT INTO `incident_mail_configuration` (`id`,`action_name`,`from_state`,`mail_body`,`mail_subject`,`role_name`,`to_state`,`mail_receiver`) VALUES (4,'Notify Recovery','Claim','Hello,<br/><br/>New Claim has been created by \\'aUserName\\' and its id is : aClaimId <br/><br/>To view details of this Claim, please click the following link:<br/><br/>https://aHost/redirect.action?nextURI=aURLPrefix/incidents/tratSniaClaimIddnEni/edit<br/><br/>From,<br/>TSS-Incident Management System<br/>-----------------------------------------------------------<br/>DO NOT REPLY TO THIS MESSAGE. This email account is for sending messages only.<br/>If you require Customer Service or Technical Support, please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>','New Claim','claims_manager','Pending Recovery','103')");
        insertIntoTable("reports_query_configuration", "INSERT INTO `incident_mail_configuration` (`id`,`action_name`,`from_state`,`mail_body`,`mail_subject`,`role_name`,`to_state`,`mail_receiver`) VALUES (5,'Rejected','Incident Report','Hello,<br/><br/>Incident Report has been rejected by \\'aUserName\\' and its id is : aClaimId <br/><br/>To view details of this Claim, please click the following link:<br/><br/>https://aHost/redirect.action?nextURI=aURLPrefix/my-Incident/tratSniaClaimIddnEni/edit<br/><br/>From,<br/>TSS-Incident Management System<br/>-----------------------------------------------------------<br/>DO NOT REPLY TO THIS MESSAGE. This email account is for sending messages only.<br/>If you require Customer Service or Technical Support, please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>','Incident Report Rejected','claims','Closed (No Incident)','owner')");
        insertIntoTable("reports_query_configuration", "INSERT INTO `incident_mail_configuration` (`id`,`action_name`,`from_state`,`mail_body`,`mail_subject`,`role_name`,`to_state`,`mail_receiver`) VALUES (6,'Verified','Incident Report','Hello,<br/><br/>Incident Report has been verified by \\'aUserName\\' and its id is : aClaimId <br/><br/>To view details of this Claim, please click the following link:<br/><br/>https://aHost/redirect.action?nextURI=aURLPrefix/my-Incident/tratSniaClaimIddnEni/edit<br/><br/>From,<br/>TSS-Incident Management System<br/>-----------------------------------------------------------<br/>DO NOT REPLY TO THIS MESSAGE. This email account is for sending messages only.<br/>If you require Customer Service or Technical Support, please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>','Incident Report Verified','claims','Incident','owner')");

    }

    private void updateReportsQueryConfiguration()
    {

        insertIntoTable("reports_query_configuration", "INSERT INTO `reports_query_configuration` (`title`,`description`,`name`,`query`,`id`) VALUES ('Alarm Status Summary',NULL,'alarm_status_summary','select Count, Status, Color from   (   select count(*) as Count, \"Alarm\" as Status, \"#CB4B4B\" as Color from tvn_tt where alarm_triggered and configured between  \\':startDate\\'  and  \\':endDate\\'          union          select count(*) as Count, \"No Alarm\" as Status, \"#4DA74D\" as Color  from tvn_tt where NOT alarm_triggered and configured between  \\':startDate\\'  and  \\':endDate\\'   ) internal_query',1)");
        insertIntoTable("reports_query_configuration", "INSERT INTO `reports_query_configuration` (`title`,`description`,`name`,`query`,`id`) VALUES ('Freight Forwarder Performance',NULL,'freight_forwarder_performance','select freight_forwarder, Count, Status, Color from    (   select freight_forwarder ,count(*) as Count, \"Alarm\" as Status,\"#CB4B4B\" as Color    from tvn_tt where alarm_triggered    and configured between  \\':startDate\\' and  \\':endDate\\'    group by freight_forwarder    union    select freight_forwarder, count(*) as Count, \"No Alarm\" as Status,\"#4DA74D\" as Color    from tvn_tt where NOT alarm_triggered    and configured between  \\':startDate\\' and  \\':endDate\\' group by freight_forwarder   ) inner_query',2)");
        insertIntoTable("reports_query_configuration", "INSERT INTO `reports_query_configuration` (`title`,`description`,`name`,`query`,`id`) VALUES ('Logger Status Summary',NULL,'logger_status_summary','select sum(Count), Status, Color from (     select YEAR(mission_ended) AS \"Year\", MONTHNAME(mission_ended) AS \"Month\", COUNT(tt.tt_id) AS Count, \"Downloaded\" as Status, \"#CB4B4B\" as Color from tvn_tt tt     where mission_ended between  \\':startDate\\' and  \\':endDate\\'     GROUP BY YEAR(mission_ended), MONTH(mission_ended)          UNION          select YEAR(tt.configured) AS \"Year\", MONTHNAME(tt.configured) AS \"Month\", COUNT(tt.tt_id) AS Count, \"Started\" as Status, \"#4DA74D\" as Color from tvn_tt tt     where tt.configured between  \\':startDate\\' and  \\':endDate\\'     GROUP BY YEAR(tt.configured), MONTH(tt.configured)          UNION          select YEAR(tt.configured) AS \"Year\", MONTHNAME(tt.configured) AS \"Month\", COUNT(tt.tt_id) AS Count, \"Pending\" as Status, \"#EDC240\" as Color from tvn_tt tt     where tt.configured between \\':startDate\\' and \\':endDate\\'     and tt.available_mission_length is null     GROUP BY YEAR(tt.configured), MONTH(tt.configured) ) Results group by Status',3)");
        insertIntoTable("reports_query_configuration", "INSERT INTO `reports_query_configuration` (`title`,`description`,`name`,`query`,`id`) VALUES ('Seasonal Performance',NULL,'seasonal_performance','select Month, Year, Count, Status, Color      from (    select MONTH(mission_ended) as mm, MONTHNAME(mission_ended) AS \"Month\",     YEAR(mission_ended) AS \"Year\", count(*) as Count, \"Alarm\" as Status, \"#CB4B4B\" as Color     from tvn_tt where alarm_triggered and configured between  \\':startDate\\' and  \\':endDate\\'     GROUP BY MONTH(mission_ended), YEAR(mission_ended)     union     select MONTH(mission_ended) as mm, MONTHNAME(mission_ended) AS \"Month\",     YEAR(mission_ended) AS \"Year\", count(*) as Count, \"No Alarm\" as Status, \"#4DA74D\" as Color     from tvn_tt where NOT alarm_triggered and configured between \\':startDate\\' and \\':endDate\\'     GROUP BY MONTH(mission_ended), YEAR(mission_ended)) complete_result order by Year,mm;',4)");
        insertIntoTable("reports_query_configuration", "INSERT INTO `reports_query_configuration` (`title`,`description`,`name`,`query`,`id`) VALUES ('Transport Mode Summary',NULL,'transport_mode_summary','select count(*) , tt.type_of_transportation     from tvn_tt tt where tt.configured     between \\':startDate\\'  and \\':endDate\\' GROUP BY tt.type_of_transportation;',5)");


        executeUpdate("update reports_query_configuration set query = " +
                "'select Count, Status, Color from (select count(*) as Count, \"Alarm\" as Status, \"#CB4B4B\" as Color from tvn_tt " +
                "where alarm_triggered and configured between  \\':startDate\\'  and  \\':endDate\\'   and senders_tvn_id like :senders_tvn_id       " +
                "union          " +
                "select count(*) as Count, \"No Alarm\" as Status, \"#4DA74D\" as Color  from tvn_tt " +
                "where NOT alarm_triggered and configured between  \\':startDate\\'  and  \\':endDate\\'  and senders_tvn_id like :senders_tvn_id ) " +
                "internal_query ' where id = 1");
        executeUpdate("update reports_query_configuration set query = " +
                "'select freight_forwarder, Count, Status, Color from (select freight_forwarder ,count(*) as Count, \"Alarm\" as Status,\"#CB4B4B\" as Color    from tvn_tt " +
                "where alarm_triggered    and configured between  \\':startDate\\' and  \\':endDate\\'   and senders_tvn_id like :senders_tvn_id   group by freight_forwarder    " +
                "union    " +
                "select freight_forwarder, count(*) as Count, \"No Alarm\" as Status,\"#4DA74D\" as Color    from tvn_tt " +
                "where NOT alarm_triggered    and configured between  \\':startDate\\' and  \\':endDate\\'  and senders_tvn_id like :senders_tvn_id  group by freight_forwarder   ) " +
                "inner_query  ' where id = 2");
        executeUpdate("update reports_query_configuration set query = " +
                "'select sum(Count), Status, Color from (" +
                "select YEAR(mission_ended) AS \"Year\", MONTHNAME(mission_ended) AS \"Month\", COUNT(tt.tt_id) AS Count, \"Downloaded\" as Status, \"#CB4B4B\" as Color from tvn_tt tt     " +
                "where mission_ended between  \\':startDate\\' and  \\':endDate\\'   and senders_tvn_id like :senders_tvn_id    GROUP BY YEAR(mission_ended), MONTH(mission_ended)          " +
                "UNION          " +
                "select YEAR(tt.configured) AS \"Year\", MONTHNAME(tt.configured) AS \"Month\", COUNT(tt.tt_id) AS Count, \"Started\" as Status, \"#4DA74D\" as Color from tvn_tt tt     " +
                "where tt.configured between  \\':startDate\\' and  \\':endDate\\'   and senders_tvn_id like :senders_tvn_id    GROUP BY YEAR(tt.configured), MONTH(tt.configured)          " +
                "UNION          " +
                "select YEAR(tt.configured) AS \"Year\", MONTHNAME(tt.configured) AS \"Month\", COUNT(tt.tt_id) AS Count, \"Pending\" as Status, \"#EDC240\" as Color from tvn_tt tt     " +
                "where tt.configured between \\':startDate\\' and \\':endDate\\'   and senders_tvn_id like :senders_tvn_id    and tt.available_mission_length is null     GROUP BY YEAR(tt.configured), MONTH(tt.configured) ) " +
                "Results  group by Status' where id = 3");
        executeUpdate("update reports_query_configuration set query = " +
                "'select Month, Year, Count, Status, Color from (" +
                "select MONTH(mission_ended) as mm, MONTHNAME(mission_ended) AS \"Month\",     YEAR(mission_ended) AS \"Year\", count(*) as Count, \"Alarm\" as Status, \"#CB4B4B\" as Color     from tvn_tt " +
                "where alarm_triggered and configured between  \\':startDate\\' and  \\':endDate\\'   and senders_tvn_id like :senders_tvn_id    GROUP BY MONTH(mission_ended), YEAR(mission_ended)     " +
                "union     " +
                "select MONTH(mission_ended) as mm, MONTHNAME(mission_ended) AS \"Month\",     YEAR(mission_ended) AS \"Year\", count(*) as Count, \"No Alarm\" as Status, \"#4DA74D\" as Color     from tvn_tt " +
                "where NOT alarm_triggered and configured between \\':startDate\\' and \\':endDate\\'   and senders_tvn_id like :senders_tvn_id    GROUP BY MONTH(mission_ended), YEAR(mission_ended)) " +
                "complete_result  order by Year,mm' where id = 4");
        executeUpdate("update reports_query_configuration set query = " +
                "'select count(*) , if(tt.type_of_transportation is null,\"\",tt.type_of_transportation) as transporation_type, tt.senders_tvn_id     from tvn_tt tt " +
                "where tt.configured     between \\':startDate\\'  and \\':endDate\\' and tt.senders_tvn_id like :senders_tvn_id GROUP BY transporation_type ' where id = 5");

    }

    private void updateReportsIncidentsFieldsInfo()
    {

        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('actual_recoveries_gbp','','cost-currency','','financeDetails','Actual Recoveries GBP',1,'>=,==,<=','==','true','field_update',1,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('attached_documents','','file','','docsReference','Attached Image',2,NULL,'==',NULL,'file_upload',2,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('balance_outstanding_gbp','','cost-currency','','financeDetails','Balance Outstanding GBP',2,'>=,==,<=','==','true','field_update',3,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('batch',NULL,'text','','shippingDetails','Batch',13,NULL,'==',NULL,'field_update',4,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('claim_created_date','','date','','incidentDetails','Created On',6,NULL,'==',NULL,'',5,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('claim_no','','text','','knockedOff','Claim number',3,'==','==','false','',6,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('consignee',NULL,'text','','shippingDetails','Consignee',12,'==','==','true','field_update',7,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('country_of_destination',NULL,'text','AUSTRALIA, SWEDEN, USA','shippingDetails','Country of Destination',6,'==','==','true','field_update',8,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('country_of_origin',NULL,'text','AUSTRALIA, SWEDEN, USA','shippingDetails','Country of Origin',4,'==','==','true','field_update',9,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('created_by','','text','','incidentDetails','Created By',5,'==','==','true','',10,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('currency_type','USD','select','USD,EUR,RON,ILS,THB,AUD,BDT,SGD,GBP,PAB,TWD,JPY,PKR,HUF,MXN,IDR,BRL,PLN,SGD','lossDetails','Currency Type',5,NULL,'==',NULL,'field_update',11,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('customer',NULL,'text',NULL,'shippingDetails','Customer',12,'==','==','true','field_update',12,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('deductibles_gbp','','cost-currency','','financeDetails','Deductibles GBP',3,'>=,==,<=','==','false','field_update',13,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('delivery_no',NULL,'text',NULL,'shippingDetails','Delivery No',11,'==','==','true','field_update',14,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('description','','textarea','','incidentDetails','Description',2,NULL,'==',NULL,'field_update',15,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('destination',NULL,'text',NULL,'shippingDetails','Destination',5,'==','==','true','field_update',16,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('fpa',NULL,'text','','shippingDetails','FPA#',14,NULL,'==',NULL,'field_update',17,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('freight_forwarder',NULL,'text','J M Transport, Eurodifarm Srl, Iveco Eurostar, Al-Muslim Goods Transport Company, DANZAS AEI Emirates LLC','shippingDetails','Forwarding Agent',9,NULL,'==',NULL,'field_update',18,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('gross_reserve_gbp','','cost-currency','','financeDetails','Gross Reserve GBP',4,'>=,==,<=','==','false','field_update',19,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('insurer_reference',NULL,'text','','shippingDetails','Insurer reference',7,'>=,==,<=','==','true','field_update',20,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('landing_number',NULL,'text','','shippingDetails','Bill of Landing number',10,'==','==','false','field_update',21,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('loss_code','To Be Advised','select','Missing,Damaged,Theft,Break In,Fire Flood,Leakage','lossDetails','Primary Loss',3,'==','==','true','field_update',22,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('loss_code_2','Missing In Part','select','Missing In Part,Contamination,Physical Damage,Spoiled','lossDetails','Secondary Loss',4,NULL,'==',NULL,'field_update',23,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('loss_date','','date','','lossDetails','Loss Date',1,'>,==,<','==','false','field_update',24,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('loss_estimate','','cost-currency','','lossDetails','Loss Estimate',2,NULL,'==',NULL,'field_update',25,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('net_absolute_gbp','','cost-currency','','financeDetails','Net Absolute GBP',5,'>=,==,<=','==','true','field_update',26,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('notes',NULL,'text',NULL,'shippingDetails','Notes',16,NULL,NULL,NULL,NULL,27,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('order_no',NULL,'text',NULL,'shippingDetails','Order No',10,'==','==','true','field_update',28,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('origin',NULL,'text',NULL,'shippingDetails','Origin',3,'==','==','true','field_update',29,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('origin_email',NULL,'text',NULL,'shippingDetails','Origin EMail',9,'==','==','true','field_update',30,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('package_no',NULL,'text',NULL,'shippingDetails','Package No',13,'==','==','true','field_update',31,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('payments_gbp','','cost-currency','','financeDetails','Payments GBP',6,'>=,==,<=','==','true','field_update',32,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('product',NULL,'text','Divolect TR 150mg, X','shippingDetails','Product',2,NULL,'==',NULL,'field_update',33,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('remarks','','textarea','','docsReference','Remarks',1,NULL,'==',NULL,'field_update',34,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('shipment_no',NULL,'text','','shippingDetails','Shipment No',1,'==','==','false',NULL,35,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('shipper_name',NULL,'text','','shippingDetails','Shipper',8,'==','==','true','field_update',36,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('sku',NULL,'text','','shippingDetails','SKU',15,NULL,'==',NULL,'field_update',37,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('status','Incident Report','select','Incident Report,Incident,Claim,Closed (No Incident),Closed (No Insurance),Pending Recovery,Outstanding Claim,Claim (Recovered),Settled (Closed),Closed (Claim Repudiated)','incidentDetails','Current Status',4,'==','==','true','field_update',38,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('title','','text','','incidentDetails','Title',1,NULL,'==',NULL,'field_update',39,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('tt_id',NULL,'text',NULL,'shippingDetails','TT ID',14,NULL,NULL,'false',NULL,40,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('type_of_transport',NULL,'text','Air,Sea,Road','shippingDetails','Mode of Transport',7,'==','==','true','field_update',41,NULL,NULL,'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('type_of_transportation','','multiselect','','transportMode','Mode of Transport',1,NULL,NULL,'true',NULL,42,NULL,'select distinct(type_of_transportation) from tvn_tt','jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('alarm_triggered','','multiselect','Alarm,No Alarm','status','Alarm Status',1,NULL,NULL,'true',NULL,43,NULL,NULL,'jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('mission_status','','multiselect','Started,Pending,Downloaded','status','Mission Status',2,NULL,NULL,'true',NULL,44,NULL,NULL,'jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('country_of_origin','','multiselect','','origin','Country of origin',2,NULL,NULL,'true',NULL,45,NULL,'select distinct(country_of_origin) from tvn_tt','jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('destination','','multiselect','','destination','Destination',1,NULL,NULL,'true',NULL,46,NULL,'select distinct (destination) from tvn_tt order by destination','jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('country_of_destination','','multiselect','','destination','Country of destination',2,NULL,NULL,'true',NULL,47,NULL,'select distinct(country_of_destination) from tvn_tt order by country_of_destination','jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('freight_forwarder','','multiselect','','freightForwarder','Freight forwarder for route',1,NULL,NULL,'true',NULL,48,NULL,'select distinct(freight_forwarder) from tvn_tt','jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('mission_ended',NULL,'date',NULL,'missionDate','End date',2,NULL,NULL,'true',NULL,49,'',NULL,'jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('mission_started',NULL,'date',NULL,'missionDate','Start date',1,NULL,NULL,'true',NULL,50,'',NULL,'jreports')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`,`default_value`,`field_type`,`possible_values`,`section_name`,`ui_label`,`display_order`,`possible_operations`,`default_operator`,`show_on_ui`,`template_name`,`id`,`default_value_query`,`possible_values_query`,`app_context`) VALUES ('origin',NULL,'multiselect',NULL,'origin','Origin',3,NULL,NULL,'true',NULL,51,NULL,'select distinct(origin) from tvn_tt','jreports')");

        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`field_name`, `field_type`, `section_name`, `ui_label`, `display_order`, `show_on_ui`, `id`, `app_context`) VALUES ('tvn_id', 'text', 'knockedOff', 'Tvn Id', '23', 'false', '52', 'claims')");
        insertIntoTable("reports_incident_fields_info", "INSERT INTO `reports_incident_fields_info` (`id`, `app_context`, `field_name`, `field_type`, `ui_label`) VALUES ('53', 'claims', 'ship_company_name', 'text', 'Ship Company')");


        executeUpdate("update reports_incident_fields_info set possible_values_query = 'select distinct(origin) from tvn_tt where senders_tvn_id like :senders_tvn_id' where id = 51");
        executeUpdate("update reports_incident_fields_info set possible_values_query = 'select distinct(freight_forwarder) from tvn_tt where senders_tvn_id like :senders_tvn_id' where id = 48");
        executeUpdate("update reports_incident_fields_info set possible_values_query = 'select distinct(country_of_destination) from tvn_tt where senders_tvn_id like :senders_tvn_id order by country_of_destination ' where id = 47");
        executeUpdate("update reports_incident_fields_info set possible_values_query = 'select distinct (destination) from tvn_tt where senders_tvn_id like :senders_tvn_id order by destination ' where id = 46");
        executeUpdate("update reports_incident_fields_info set possible_values_query = 'select distinct(country_of_origin) from tvn_tt where senders_tvn_id like :senders_tvn_id' where id = 45");
        executeUpdate("update reports_incident_fields_info set possible_values_query = 'select distinct(type_of_transportation) from tvn_tt where senders_tvn_id like :senders_tvn_id' where id = 42");
    }


    private void updateReportsChartDrilldownConfiguration()
    {

        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('logger_status_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Downloaded','Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and mission_ended between \\':startDate\\' and \\':endDate\\'','select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and mission_ended between \\':startDate\\' and \\':endDate\\'',1,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered ,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and mission_ended between \\':startDate\\' and \\':endDate\\'','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('logger_status_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Started','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\'','select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\'',2,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered ,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\'','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('logger_status_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Pending','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and available_mission_length is null','select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and available_mission_length is null',3,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered ,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and available_mission_length is null','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('alarm_status_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Alarm','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and alarm_triggered','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and alarm_triggered',4,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and alarm_triggered','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('alarm_status_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','No Alarm','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and NOT alarm_triggered','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and NOT alarm_triggered',5,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and NOT alarm_triggered','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('transport_mode_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Air','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Air\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Air\\')',6,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Air\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('transport_mode_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Road','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Road\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Road\\')',7,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Road\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('transport_mode_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Sea','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Sea\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Sea\\')',8,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Sea\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('seasonal_performance',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Alarm','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\') ','select  count(*) from :viewName where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\') ','select count(*)  from tvn_tt where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')',9,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver from tvn_tt where alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('seasonal_performance',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','No Alarm','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where NOT alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\') ','select  count(*) from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\') ','select count(*)  from tvn_tt where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')',10,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver from tvn_tt where NOT alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('freight_forwarder_performance',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Alarm','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')','select  count(*) from :viewName where  alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\') ','select count(*)  from tvn_tt where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')',11,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver from tvn_tt where alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String',' ',' ','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('freight_forwarder_performance',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','No Alarm','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where NOT alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')','select  count(*) from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\') ','select count(*)  from tvn_tt where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')',12,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver from tvn_tt where NOT alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String',' ',' ','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('custom_reports',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause ','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause ','select count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause ','select count(*) from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause ',13,'tt_id','select tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,receiver  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause ','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String','','','ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");
        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`name`,`description`,`display_field_names`,`selected_partition`,`report_field_names`,`search_field_names`,`query_torun`,`query_torun_fromview`,`count_query_torun_fromview`,`count_query_torun`,`id`,`primary_key`,`jasper_report_query`,`jasper_report_query_columns`,`jasper_report_query_columns_type`,`jasper_report_query_values`,`jasper_report_query_values_type`,`jasper_report_column_names`) VALUES ('transport_mode_summary',NULL,'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','Train','Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver','ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Train\\')','select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ','select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ','select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Train\\')',14,'tt_id','select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Train\\')','tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver','Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String',NULL,NULL,'ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver')");

        insertIntoTable("reports_chart_drilldown_configuration", "INSERT INTO `reports_chart_drilldown_configuration` (`id`, `count_query_torun`, `count_query_torun_fromview`, `display_field_names`, `jasper_report_column_names`, `jasper_report_query`, `jasper_report_query_columns`, `jasper_report_query_columns_type`, `name`, `selected_partition`, `primary_key`, `query_torun`, `query_torun_fromview`, `report_field_names`, `search_field_names`) VALUES ('15', 'select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (type_of_transportation = \\'\\' or type_of_transportation is null)  and senders_tvn_id like :senders_tvn_id', 'select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   and senders_tvn_id like :senders_tvn_id', 'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver', 'ID,Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver', 'select  tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended, CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (type_of_transportation = \\'\\' or type_of_transportation is null)', 'tt_id,origin,country_of_origin,destination,country_of_destination,shipping_no,delivery_no,order_no,air_waybill_no,type_of_transportation,freight_forwarder,carrier,product,mission_started,mission_ended,alarm_triggered,receiver', 'Integer,String,String,String,String,String,String,String,String,String,String,String,String,Date,Date,String,String', 'transport_mode_summary', '', 'tt_id', 'select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (type_of_transportation = \\'\\' or type_of_transportation is null)  and senders_tvn_id like :senders_tvn_id', 'select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))    and senders_tvn_id like :senders_tvn_id', 'Origin,Country Of Origin,Destination,Country Of Destination,Shipping No,Delivery No,Order No,Airway Bill No,Type Of Transportation,Freight Forwarder,Carrier,Product,Mission Started,Mission Ended,Alarm Triggered,Receiver', 'ID,Shipping No,Product,Origin,Country Of Origin,Type Of Transportation,Destination,Country Of Destination,Alarm Triggered,Mission Started,Mission Ended,Freight Forwarder,Carrier,Delivery No,Order No,Airway Bill No,Receiver')");

        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and mission_ended between \\':startDate\\' and \\':endDate\\'  and senders_tvn_id like :senders_tvn_id' WHERE `id`='1'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\'  and senders_tvn_id like :senders_tvn_id' WHERE `id`='2'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and available_mission_length is null  and senders_tvn_id like :senders_tvn_id' WHERE `id`='3'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and alarm_triggered  and senders_tvn_id like :senders_tvn_id' WHERE `id`='4'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and NOT alarm_triggered  and senders_tvn_id like :senders_tvn_id' WHERE `id`='5'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Air\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='6'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Road\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='7'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Sea\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='8'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='9'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where NOT alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='10'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='11'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from tvn_tt where NOT alarm_triggered and(upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='12'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause  and senders_tvn_id like :senders_tvn_id' WHERE `id`='13'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun`='select  tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Train\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='14'");

        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and NOT alarm_triggered and senders_tvn_id like :senders_tvn_id' WHERE `id`='5'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Air\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='6'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Road\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='7'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Sea\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='8'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\') and senders_tvn_id like :senders_tvn_id' WHERE `id`='9'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='10'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='11'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='12'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause  and senders_tvn_id like :senders_tvn_id' WHERE `id`='13'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Train\\')  and senders_tvn_id like :senders_tvn_id' WHERE `id`='14'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and alarm_triggered   and senders_tvn_id like :senders_tvn_id' WHERE `id`='4'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and available_mission_length is null   and senders_tvn_id like :senders_tvn_id' WHERE `id`='3'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\'   and senders_tvn_id like :senders_tvn_id' WHERE `id`='2'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun`='select count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and mission_ended between \\':startDate\\' and \\':endDate\\'   and senders_tvn_id like :senders_tvn_id' WHERE `id`='1'");


        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')   ' WHERE `id`='11'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))    '  WHERE `id`='6'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))    '  WHERE `id`='7'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))    '  WHERE `id`='8'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))    '  WHERE `id`='14'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ' WHERE `id`='1'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))  ' WHERE `id`='2'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='3'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='4'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='5'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')  ' WHERE `id`='9'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')  ' WHERE `id`='10'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver,configured from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')  ' WHERE `id`='12'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause  ' WHERE `id`='13'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `query_torun_fromview`='select tt_id,shipping_no,product,origin,country_of_origin,type_of_transportation,destination,country_of_destination,CASE alarm_triggered WHEN 1 THEN \"Alarm\" WHEN 0 THEN \"No Alarm\" END as alarm_triggered,mission_started,mission_ended,freight_forwarder,carrier,delivery_no,order_no,air_waybill_no,receiver from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='15'");


        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) ' WHERE `id`='4'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where  alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')    ' WHERE `id`='11'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='6'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='7'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='8'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))    ' WHERE `id`='14'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   ' WHERE `id`='5'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))    '");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   '");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\'))   '");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')    ' WHERE `id`='10'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where NOT alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (freight_forwarder = \\':xAxisValue\\')    ' WHERE `id`='12'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where alarm_triggered and (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or upper(alarm_triggered) like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and (concat(MONTHNAME(mission_ended),\\',\\',YEAR(mission_ended)) = \\':xAxisValue\\')   ' WHERE `id`='9'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*) from :viewName where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) :whereClause   ' WHERE `id`='13'");
        executeUpdate("UPDATE `tempvianet`.`reports_chart_drilldown_configuration` SET `count_query_torun_fromview`='select  count(*)  from tvn_tt where (upper(DATE_FORMAT(mission_started, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(DATE_FORMAT(mission_ended, \\'%b %e %Y %h:%i %p\\')) like upper(\\'%:searchString%\\') or upper(origin) like upper(\\'%:searchString%\\') or upper(country_of_origin) like upper(\\'%:searchString%\\') or upper(destination) like upper(\\'%:searchString%\\')or upper(country_of_destination) like upper(\\'%:searchString%\\') or upper(shipping_no) like upper(\\'%:searchString%\\')or upper(delivery_no) like upper(\\'%:searchString%\\') or upper(order_no) like upper(\\'%:searchString%\\') or upper(air_waybill_no) like upper(\\'%:searchString%\\') or upper(type_of_transportation) like upper(\\'%:searchString%\\') or upper(freight_forwarder) like upper(\\'%:searchString%\\') or upper(carrier) like upper(\\'%:searchString%\\') or upper(product) like upper(\\'%:searchString%\\') or CASE alarm_triggered WHEN 1 THEN upper(\"Alarm\") WHEN 0 THEN upper(\"No Alarm\") END like upper(\\'%:searchString%\\') or upper(receiver) like upper(\\'%:searchString%\\')) and configured between \\':startDate\\' and \\':endDate\\' and upper(type_of_transportation) = upper(\\'Sea\\')   ' WHERE `id`='15'");



    }

    private void updateReportsIncidentsUITabInfo()
    {

        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (1,'incidentDetails','Incident Details',0,'claims_reporter','new','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (2,'shippingDetails','Shipping Details',1,'claims_reporter','new','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (3,'lossDetails','Loss Information',2,'claims_reporter','new','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (4,'docsReference','Docs & References',3,'claims_reporter','new','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (6,'incidentDetails','Incident Details',0,'claims_investigator','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (7,'shippingDetails','Shipping Details',1,'claims_investigator','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (8,'lossDetails','Loss Information',2,'claims_investigator','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (9,'docsReference','Docs & References',4,'claims_investigator','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (11,'incidentDetails','Incident Details',0,'claims','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (12,'shippingDetails','Shipping Details',1,'claims','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (13,'lossDetails','Loss Information',2,'claims','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (14,'docsReference','Docs & References',4,'claims','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (16,'financeDetails','Finance Details',3,'claims','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (17,'financeDetails','Finance Details',3,'claims_investigator','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (18,'incidentDetails','Incident Details',0,'claims_reporter','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (19,'shippingDetails','Shipping Details',1,'claims_reporter','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (20,'lossDetails','Loss Information',2,'claims_reporter','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (21,'docsReference','Docs & References',3,'claims_reporter','update','claims')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (22,'missionDate','Date Range',1,'jreports_admin','new','jreports')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (23,'destination','Destination',3,'jreports_admin','new','jreports')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (24,'freightForwarder','Freight Forwarder',4,'jreports_admin','new','jreports')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (25,'origin','Origin',2,'jreports_admin','new','jreports')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (26,'transportMode','Transport Mode',5,'jreports_admin','new','jreports')");
        insertIntoTable("reports_incident_ui_tab_info", "INSERT INTO `reports_incident_ui_tab_info` (`id`,`section_name`,`tab_title`,`display_order`,`role_name`,`operation`,`app_context`) VALUES (27,'status','Status',6,'jreports_admin','new','jreports')");

    }

    private void updateIncidentNavigationPanel()
    {
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (1,2,'icon-file','New Incident','add-Incident',0,'claims_reporter')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (2,1,'icon-edit','My Incidents','my-Incident',0,'claims_reporter')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (3,1,'icon-home','Dashboard','dashboard',0,'claims')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (4,2,'icon-file','All Incidents','incidents',0,'claims')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (5,3,'icon-edit','Process Incident','process-Incident',0,'claims')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (7,1,'icon-picture','Report 1','report1',11,'claims')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (8,1,'icon-edit','Process Incident','process-Incident',0,'claims_investigator')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (9,2,'icon-picture','Report 2','report2',11,'claims')");
        insertIntoTable("incident_nav_panel_access", "INSERT INTO `incident_nav_panel_access` (`user_access_map_id`,`display_order`,`icon_name`,`label_name`,`link_name`,`parent_id`,`role_id`) VALUES (11,4,'icon-picture','Custom Reports','report',0,'claims')");

    }

    private void updateIncidentFieldAccessRights()
    {

        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (1,'actual_recoveries_gbp','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (2,'actual_recoveries_gbp','claims_investigator','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (3,'attached_documents','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (4,'attached_documents','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (5,'attached_documents','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (6,'balance_outstanding_gbp','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (7,'balance_outstanding_gbp','claims_investigator','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (8,'business_unit_name','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (9,'business_unit_name','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (10,'business_unit_name','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 11  ,'claim_created_date','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 12  ,'claim_created_date','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 13  ,'claim_no','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 14  ,'claim_no','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 15  ,'claim_no','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 16  ,'consignee','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 17  ,'consignee','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 18  ,'consignee','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 19  ,'country_of_destination','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 20  ,'country_of_destination','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 21  ,'country_of_destination','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 22  ,'country_of_origin','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 23  ,'country_of_origin','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 24  ,'country_of_origin','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 25  ,'created_by','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 26  ,'created_by','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 27  ,'created_by','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 28  ,'currency_type','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 29  ,'currency_type','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 30  ,'currency_type','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 31  ,'deductibles_gbp','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 32  ,'deductibles_gbp','claims_investigator','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 33  ,'description','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 34  ,'description','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 35  ,'description','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 36  ,'freight_forwarder','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 37  ,'freight_forwarder','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 38  ,'freight_forwarder','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 39  ,'gross_reserve_gbp','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 40  ,'gross_reserve_gbp','claims_investigator','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 41  ,'insurer_reference','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 42  ,'insurer_reference','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 43  ,'landing_number','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 44  ,'landing_number','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 45  ,'landing_number','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 46  ,'loss_code','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 47  ,'loss_code','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 48  ,'loss_code','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 49  ,'loss_code_2','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 50  ,'loss_code_2','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 51  ,'loss_code_2','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 52  ,'loss_date','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 53  ,'loss_date','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 54  ,'loss_date','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 55  ,'loss_estimate','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 56  ,'loss_estimate','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 57  ,'loss_estimate','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 58  ,'net_absolute_gbp','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 59  ,'net_absolute_gbp','claims_investigator','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 60  ,'payments_gbp','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 61  ,'payments_gbp','claims_investigator','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 62  ,'product','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 63  ,'product','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 64  ,'product','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 65  ,'remarks','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES (  66 ,'remarks','claims_investigator','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 67  ,'remarks','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 68  ,'shipment_no','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 69  ,'shipment_no','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 70  ,'shipment_no','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 71  ,'shipper_name','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 72  ,'shipper_name','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 73  ,'shipper_name','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 74  ,'ship_company_name','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 75  ,'ship_company_name','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 76  ,'ship_company_name','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 77  ,'status','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 78  ,'status','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 79  ,'status','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 80  ,'title','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 81  ,'title','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 82  ,'title','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 83  ,'tt_id','claims','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 84  ,'tt_id','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 85  ,'tt_id','claims_reporter','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 86  ,'type_of_transport','claims','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 87  ,'type_of_transport','claims_investigator','read')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`,`user_role`,`permission`) VALUES ( 88  ,'type_of_transport','claims_reporter','write')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`, `permission`, `user_role`) VALUES ('89', 'claim_created_date', 'read', 'claims')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`, `permission`, `user_role`) VALUES ('90', 'claim_created_date', 'read', 'claims_investigator')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`, `permission`, `user_role`) VALUES ('91', 'claim_created_date', 'read', 'claims_reporter')");

        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`, `permission`, `user_role`) VALUES ('92', 'tvn_id', 'read', 'claims')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`, `permission`, `user_role`) VALUES ('93', 'tvn_id', 'read', 'claims_investigator')");
        insertIntoTable("incident_fields_access_rights", "INSERT INTO `incident_fields_access_rights` (`id`, `field_name`, `permission`, `user_role`) VALUES ('94', 'tvn_id', 'read', 'claims_reporter')");

    }

    private void updateIncidentLifeCycle()
    {

        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Claim','Claim','Update','claims',1)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Claim','Claim','Update','claims_reporter',2)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Claim','Outstanding Claim','Notify Insurance','claims',3)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Claim','Pending Recovery','Notify Recovery','claims',4)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident','Claim','Approve Claim','claims',5)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident','Closed (No Insurance)','No Insurance','claims',6)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident','Incident','Update','claims',7)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident','Incident','Update','claims_reporter',8)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident Report','Closed (No Incident)','Rejected','claims',9)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident Report','Incident','Verified','claims',10)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident Report','Incident Report','Update','claims',11)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Incident Report','Incident Report','Update','claims_reporter',12)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Outstanding Claim','Closed (Claim Repudiated)','Repudient','claims_investigator',13)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Outstanding Claim','Outstanding Claim','Update','claims',14)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Outstanding Claim','Outstanding Claim','Update','claims_investigator',15)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Outstanding Claim','Outstanding Claim','Update','claims_reporter',16)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Outstanding Claim','Settled (Closed)','Settled','claims_investigator',17)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Pending Recovery','Claim (Recovered)','Recovered','claims_investigator',18)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Pending Recovery','Pending Recovery','Update','claims',19)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Pending Recovery','Pending Recovery','Update','claims_investigator',20)");
        insertIntoTable("incident_life_cycle", "INSERT INTO `incident_life_cycle` (`from_state`,`to_state`,`action_name`,`role_name`,`id`) VALUES ('Pending Recovery','Pending Recovery','Update','claims_reporter',21)");

    }

    private void updateIncidentReportAvailableFields()
    {

        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('business_unit_name','Business Unit Name','==','==','true','text')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('claim_no','Claim number','>,==,<','==','false','number')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('consignee','Consignee','==','==','true','text')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('country_of_destination','Country Of Destination','==','==','true','text')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('country_of_origin','Country of Orgin','==','==','true','text')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('deductibles_gbp','Deductiables GBP','>,==,<','==','false','number')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('gross_reserve_gbp','Group Reserved GBP','>,==,<','==','false','number')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('insurer_reference','Insurer Reference','>,==,<','==','false','number')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('landing_number','Landing number Bill','>,==,<','==','false','number')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('loss_code','Loss Code','==','==','true','select')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('loss_date','Loss Date','>,==','==','false','date')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('shipment_no','Shipment number','>,==,<','==','false','TEXT')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('shipper_name','Shipper','==','==','true','text')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('ship_company_name','Ship Company','==','==','true','text')");
        insertIntoTable("incident_available_fields", "INSERT INTO `incident_available_fields` (`field_name`,`label`,`availableOperations`,`operator`,`disabled`,`type`) VALUES ('status','Current Status','==','==','true','select')");

    }

    private void insertIntoTable(String tableName, String sql) {
        System.out.println("Inserting into "+tableName);
        Statement st = null;
        try {
            st = conn.createStatement();
            st.executeUpdate(sql);
            System.out.println(tableName+" updated");
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        } finally {
            try {
                st.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private void updateColumnDefinitions(){
        System.out.println("Updating column definitions");
        try {
            Statement st = conn.createStatement();
            st.executeUpdate("ALTER TABLE ccis_user MODIFY password VARCHAR(50) NULL");
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        }
    }

    private void alterTables() {
        // Alter ccis_office to allow 500 characters
        System.out.println("Alter ccis_office.alarmMailAddress, ccis_office.launcherAlarmMailAddress ");
        String query = "alter table ccis_office modify alarmMailAddress VARCHAR(500), modify launcherAlarmMailAddress VARCHAR(500)";

        try {
            Statement st = conn.createStatement();
            st.executeUpdate(query);
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        }
    }

    private void updateTables() {
        System.out.println("update ccis_triptags.typeOfTransportation");
        executeUpdate("update ccis_triptags set tripTagId = 10 where label like 'typeOfTransportation';");
    }

    private void addToolTips() {
        insertToolTip("'REST'", "'Access to the rest services.'", "'Rest'");
        insertToolTip("'REST_GENERIC_LOGGER'", "'Access to the generic logger rest service.'", "'Generic Logger Service'");
        insertToolTip("'SINGLESIGNONUSER_SETTING'", "'This indicates if a user is using single sign on. It is used to control which functionality that should be enabled/disabled for a user. " +
                "The value is always set to true when a user logs in with single sign on.'", "'Single sign on'");
        insertToolTip("'JREPORT'", "'Access to reports services.'", "'Reports'");

        insertToolTip("'INCIDENT_MANAGEMENT'", "'Access to the Incident Management Solution service.'", "'Incident Management'");
        insertToolTip("'INCIDENT_MANAGER'", "'Access to dashboard, all incidents, verifying, rejecting, updating incidents and access to custom reports.'", "'Incident Manager'");
        insertToolTip("'INCIDENT_REPORTER'", "'Access to create and submit new incidents.'", "'Incident Reporter'");
        insertToolTip("'INCIDENT_INVESTIGATOR'", "'Access to review, verify, reject and update submitted incidents.'", "'Incident Investigator'");
        insertToolTip("'MULTIPLE_EMAILS'", "'Multiple email addresses can be entered, separated by commas.'", "'Multiple email recipients'");
    }

    private void updateProcedure(){
        String sql2="DROP TRIGGER IF EXISTS after_incident_update;";
        String sql3=afterIncidentUpdateTriggerDefinition();

        executeUpdate(sql2);
        executeUpdate(sql3);
    }
    private String afterIncidentUpdateTriggerDefinition(){
        return "\n" +
                "CREATE  TRIGGER after_incident_update \n" +
                "    AFTER UPDATE ON incident_data\n" +
                "    FOR EACH ROW BEGIN\n" +
                " IF (OLD.status != NEW.status) THEN\n" +
                "    INSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(OLD.claim_no,'status',OLD.status,NEW.status,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\t\t\t\t\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "IF (OLD.loss_date != NEW.loss_date) THEN\n" +
                "\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'loss_date',OLD.loss_date,NEW.loss_date,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\tEND IF;\n" +
                "\t\t\t\n" +
                "\t\t\tIF (OLD.title != NEW.title) THEN\n" +
                "\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'title',OLD.title,NEW.title,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\tEND IF;\n" +
                "\t\t\t\n" +
                "\t\t\tIF (OLD.description != NEW.description) THEN\n" +
                "\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'description',OLD.description,NEW.description,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\tEND IF;\n" +
                "\t\t\t\n" +
                "\t\tIF (OLD.attached_documents != NEW.attached_documents) THEN\n" +
                "\t\t\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'attached_documents',OLD.attached_documents,TRIM(LEADING ',' FROM TRIM(LEADING OLD.attached_documents FROM NEW.attached_documents)),current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.currency_type != NEW.currency_type) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'currency_type',OLD.currency_type,NEW.currency_type,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.loss_estimate != NEW.loss_estimate) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'loss_estimate',CAST(OLD.loss_estimate AS CHAR),CAST(NEW.loss_estimate AS CHAR),current_timestamp,NEW.updated_by,'number');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.gross_reserve_gbp != NEW.gross_reserve_gbp) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'gross_reserve_gbp',CAST(OLD.gross_reserve_gbp AS CHAR),CAST(NEW.gross_reserve_gbp AS CHAR),current_timestamp,NEW.updated_by,'number');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.deductibles_gbp != NEW.deductibles_gbp) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'deductibles_gbp',CAST(OLD.deductibles_gbp AS CHAR),CAST(NEW.deductibles_gbp AS CHAR),current_timestamp,NEW.updated_by,'number');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.balance_outstanding_gbp != NEW.balance_outstanding_gbp) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'balance_outstanding_gbp',CAST(OLD.balance_outstanding_gbp AS CHAR),CAST(NEW.balance_outstanding_gbp AS CHAR),current_timestamp,NEW.updated_by,'number');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.payments_gbp != NEW.payments_gbp) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'payments_gbp',CAST(OLD.payments_gbp AS CHAR),CAST(NEW.payments_gbp AS CHAR),current_timestamp,NEW.updated_by,'number');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.actual_recoveries_gbp != NEW.actual_recoveries_gbp) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'actual_recoveries_gbp',CAST(OLD.actual_recoveries_gbp AS CHAR),CAST(NEW.actual_recoveries_gbp AS CHAR),current_timestamp,NEW.updated_by,'number');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.net_absolute_gbp != NEW.net_absolute_gbp) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'net_absolute_gbp',CAST(OLD.net_absolute_gbp AS CHAR),CAST(NEW.net_absolute_gbp AS CHAR),current_timestamp,NEW.updated_by,'number');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.remarks != NEW.remarks) THEN\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'remarks',OLD.remarks,NEW.remarks,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.loss_code != NEW.loss_code) THEN\t\t\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'loss_code',OLD.loss_code,NEW.loss_code,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\t\t\t\t\tEND IF;\n" +
                "\t\t\t\t\t\t\n" +
                "\t\tIF (OLD.loss_code_2 != NEW.loss_code_2) THEN\n" +
                "\t\t\n" +
                "\t\t\tINSERT into incident_history(claim_no,field_name,prev_value,cur_value,changed_date,updated_by,data_type)\n" +
                "\t\t\t\t\t\tVALUES(NEW.claim_no,'loss_code_2',OLD.loss_code_2,NEW.loss_code_2,current_timestamp,NEW.updated_by,'text');\n" +
                "\t\tEND IF;\n" +
                "END;";
    }

    private void executeUpdate(String query) {
        try {
            Statement st = conn.createStatement();
            st.executeUpdate(query);
            System.out.println(query + " -- Success");
        } catch (SQLException ex) {
            System.out.println(query + " -- Failed");
            System.err.println(ex.getMessage());
        }
    }

    private void insertToolTip(String name, String body, String heading) {
        System.out.println("Inserting into ccis_tooltip");
        try {
            Statement st = conn.createStatement();
            st.executeUpdate("insert into `ccis_tooltip`(`name`, `body`, `heading`) values(" + name + "," + body + "," + heading + ")");
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        }
    }

    private void updateCountryTable() {
        insertIntoCountryTable("insert into `ccis_country`(`code`, `name`) values('PS', 'Palestine')");
    }
    /**
     * Insert data into ccis_access
     */
    private void updateAccessTable() {
        insertIntoAccessTable("insert into `ccis_access`(`accessId`, `name`, `description`, `parent`) values(400, 'rest', 'Rest', 1)");
        insertIntoAccessTable("insert into `ccis_access`(`accessId`, `name`, `description`, `parent`) values(401, 'rest_generic_logger', 'Generic Logger Service', 400)");
        insertIntoAccessTable("insert into `ccis_access`(`accessId`, `name`, `description`, `parent`) values(100, 'incident_management', 'Incident Management', 1)");
        insertIntoAccessTable("insert into `ccis_access`(`accessId`, `name`, `description`, `parent`) values(101, 'incident_manager', 'Incident Manager', 100)");
        insertIntoAccessTable("insert into `ccis_access`(`accessId`, `name`, `description`, `parent`) values(102, 'incident_reporter', 'Incident Reporter', 100)");
        insertIntoAccessTable("insert into `ccis_access`(`accessId`, `name`, `description`, `parent`) values(103, 'incident_investigator', 'Incident Investigator', 100)");
        insertIntoAccessTable("insert into `ccis_access`(`accessId`, `name`, `description`, `parent`) values(500, 'jreport', 'Reports', 1)");
    }

    private void insertIntoAccessTable(String sql) {
        System.out.println("Inserting into ccis_access");
        Statement st = null;
        try {
            st = conn.createStatement();
            st.executeUpdate(sql);
            System.out.println("ccis_access updated");
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        } finally {
            try {
                st.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private void insertIntoCountryTable(String sql) {
        System.out.println("Inserting into ccis_country");
        Statement st = null;
        try {
            st = conn.createStatement();
            st.executeUpdate(sql);
            System.out.println("ccis_country updated");
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        } finally {
            try {
                st.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private void updateCompanyTable() {
        System.out.println("Update ccis_company");
        String sql = "update ccis_company set helpdeskContactInfo = 'If you require Customer Service or Technical Support, please check the TSS Web site at www.tss.se or e-mail to: <EMAIL>'";
        Statement st = null;
        try {
            st = conn.createStatement();
            st.executeUpdate(sql);
            System.out.println("ccis_company updated");
        } catch (SQLException ex) {
            System.err.println(ex.getMessage());
        } finally {
            try {
                st.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private void deleteTooltips() {
        System.out.println("Delete ccis_tooltip");
        // CCIS Reports is no more
        String sql1 = "delete from ccis_tooltip where name like 'CCRMANAGER'";
        String sql2 = "delete from ccis_tooltip where name like 'CCRUSER'";
        executeUpdate(sql1);
        executeUpdate(sql2);
    }
    private void deleteAccess() {
        // CCIS Reports is no more

        //ccruser -> accessId = 19
        String sql = "delete from ccis_access_group_access where accessId=19";
        String sql2 = "delete from ccis_user_access where accessId=19";
        String sql3 = "delete from ccis_access where accessId=19";

        //ccrmanager -> accessId = 31
        String sql4 = "delete from ccis_access_group_access where accessId=31";
        String sql5 = "delete from ccis_user_access where accessId=31";
        String sql6 = "delete from ccis_access where accessId=31";

        System.out.println("Delete CCIS Reports from ccis_access_group_access, ccis_user_access and ccis_access");
        executeUpdate(sql);
        executeUpdate(sql2);
        executeUpdate(sql3);
        executeUpdate(sql4);
        executeUpdate(sql5);
        executeUpdate(sql6);
    }
}


