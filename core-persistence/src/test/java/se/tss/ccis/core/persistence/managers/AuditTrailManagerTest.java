/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.core.persistence.managers;

import org.hibernate.criterion.Order;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import se.tss.ccis.core.persistence.HibernateTest;
import se.tss.ccis.core.persistence.entities.AuditTrailEntity;
import se.tss.ccis.core.persistence.entities.AuditTrailEntity.Action;
import se.tss.ccis.core.persistence.entities.AuditTrailEntity.Status;
import se.tss.ccis.core.persistence.entities.AuditablePrimaryKeyModel;
import se.tss.ccis.core.persistence.entities.UserEntity;

import javax.persistence.*;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class AuditTrailManagerTest extends HibernateTest {

    private AuditTrailManager auditManager;

    private UserEntity defaultUser;

    @Before
    public void setup() {
        auditManager = new AuditTrailManager();
        auditManager.startTransaction();
        defaultUser = disableForeignKeyIntegrityConstraint(() -> createAndSaveUserEntity("username"));

    }

    @After
    public void tearDown() {
        clearManager(auditManager);
    }

    @Test
    public void testNewAudit() {
        ObjA a1 = new ObjA(Instant.now(), 5, "a1b", 2, 3);
        auditManager.save(a1);
        ObjB b = new ObjB("newB", 4);
        auditManager.save(b);
        final long auditId = auditManager.updateWithAudit(defaultUser, Status.SUCCESS, Action.SYSTEM,
                "Audit manager update test", a1, false,
                objA -> {
                    objA.setDate(Instant.now().plus(2, ChronoUnit.DAYS));
                    objA.addB(b);
                    objA.setB(b);
                });
        final AuditTrailEntity createdAuditTrail = auditManager.getAuditTrailById(defaultUser.getCompanyId(), auditId).get();
    }


    @Entity
    @Table(name = "ObjA")
    public static class ObjA extends AuditablePrimaryKeyModel {
        @Column
        private Date date;
        @OneToOne
        @JoinColumn(name = "b", referencedColumnName = "id")
        private ObjB b;
        @Transient
        private List<ObjB> bList;

        public ObjA() {}
        
        public ObjA(Instant time, int numLongs, String bSerial, int numBs, int numCperB) {
            this.date = Date.from(time);
            bList = new ArrayList<>();
            for (int i = 0; i < numBs; i++) {
                bList.add(new ObjB(bSerial + "#" + i, numCperB));
            }
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Instant time) {
            this.date = Date.from(time);
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public ObjB getB() {
            return b;
        }

        public void setB(ObjB b) {
            this.b = b;
        }

        public List<ObjB> getbList() {
            return bList;
        }

        public void addB(ObjB b) {
            bList.add(b);
        }

        public void setbList(List<ObjB> bList) {
            this.bList = bList;
        }

        @Override
        public String flatAuditString() {
            return String.format("A[%s] from %s", getId(), date.toString());
        }

    }

    @Entity
    @Table(name = "ObjB")
    public static class ObjB extends AuditablePrimaryKeyModel {
        private String serial;
        @Transient
        private List<ObjC> cList;

        public ObjB() {}
        
        public ObjB(String serial, int numCs) {
            this.serial = serial;
            cList = new ArrayList<>();
            for (int i = 0; i < numCs; i++) {
                cList.add(new ObjC(i));
            }
        }

        public String getSerial() {
            return serial;
        }

        public void setSerial(String serial) {
            this.serial = serial;
        }

        public List<ObjC> getcList() {
            return cList;
        }

        public void setcList(List<ObjC> cList) {
            this.cList = cList;
        }

        @Override
        public String flatAuditString() {
            return "B#" + serial + " with C:s " +
                    cList.stream().map(c -> c.getId().toString()).collect(Collectors.joining(","));
        }
    }

    @Entity
    @Table(name = "ObjC")
    public static class ObjC extends AuditablePrimaryKeyModel {
        
        public ObjC() {}
        
        public ObjC(long id) {
            this.setId(id);
        }

        @Override
        public String flatAuditString() {
            return "C#" + getId();
        }
    }

    @Test
    public void getAuditTrailWithOrders() {
        final Instant aTime = Instant.parse("2007-12-03T10:15:30.00Z");

        final int numEntries = 15;
        final List<AuditTrailEntity> auditEntries = IntStream.range(0, numEntries)
                .mapToObj(i -> {
                    final AuditTrailEntity at = auditManager.createAuditTrailWithSignature(defaultUser, new ObjC(1));
                    at.setDate(Date.from(aTime.plusSeconds(i)));
                    at.setAction(Action.SYSTEM);
                    at.setStatus(Status.SUCCESS);
                    auditManager.save(at);
                    return at;
                }).collect(Collectors.toList());


        defaultAuditSearch(defaultUser, "user.fullName");
        defaultAuditSearch(defaultUser, "office.name");
        defaultAuditSearch(defaultUser, "company.name");
    }

    private List<AuditTrailEntity> defaultAuditSearch(UserEntity user, String sortBy) {
        int pageSize = 10;
        final List<AuditTrailEntity> res = auditManager.getAuditTrailWithOrders(null, null, 0, pageSize, user.getCompanyId(),
                user, new Date(0), new Date(), false,
                Collections.singletonList(Order.asc(sortBy)), null, defaultUser.getLastname()); //part of Action.SYSTEM
        assertThat(res.size(), is(pageSize));
        return res;
    }

    private Order toOrderClause(String column, boolean desc) {
        Order order;
        if (column.equals("company") || column.equals("office")) {
            column += ".name";
        } else if (column.equals("user")) {
            column += ".fullName";
        }
        if (desc) {
            order = Order.desc(column);
        } else {
            order = Order.asc(column);
        }
        return order;
    }


    @Test
    public void auditDateFormatRoundsSeconds() {
        assertThat(AuditTrailManager.dateFormat(makeDate("2007-12-03T10:15:30.00Z")), is("2007-12-03 10:15:30"));
        assertThat(AuditTrailManager.dateFormat(makeDate("2007-12-03T10:15:30.49Z")), is("2007-12-03 10:15:30"));
        assertThat(AuditTrailManager.dateFormat(makeDate("2007-12-03T10:15:30.50Z")), is("2007-12-03 10:15:31"));
        assertThat(AuditTrailManager.dateFormat(makeDate("2007-12-03T10:15:30.51Z")), is("2007-12-03 10:15:31"));
    }

    //need to offset the date with the timezone offset, so that unit tests can be performed on machines not on UTC
    private Date makeDate(String dateString) {
        final Instant timestamp = Instant.parse(dateString);
        final Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timestamp.toEpochMilli());
        final long offsetSeconds = -(cal.get(Calendar.ZONE_OFFSET) + cal.get(Calendar.DST_OFFSET)) / (1000);
        return Date.from(timestamp.plusSeconds(offsetSeconds));
    }
}