@startuml Migration Flow
!theme plain
title Migration Steps - Simple Flow

start

:Phase 1: Infrastructure Services;
note right
  • Configuration Service
  • Audit Service
  • Notification Service
end note

:Phase 2: User Management;
note right
  • User Service
  • Authentication
  • Authorization
end note

:Phase 3: Business Services;
note right
  • Profile Service
  • Integration Service
  • Logger Service
end note

:Phase 4: Core Business;
note right
  • Trial Service
  • Shipment Service
  • Deviation Service
end note

stop

@enduml
