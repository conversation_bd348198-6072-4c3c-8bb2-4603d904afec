@startuml Phase 2 User Management
!theme plain
title Phase 2: User Management

rectangle "User Service" as User #yellow
rectangle "Authentication" as Auth #yellow
rectangle "Authorization" as Authz #yellow
rectangle "Integration" as Integration #yellow

User --> Auth : Complete
Auth --> Authz : Complete
Authz --> Integration : Complete

note bottom of User
  Extract: UserEntity
  Extract: CompanyEntity
  Create: User APIs
end note

note bottom of Auth
  Add: JWT tokens
  Add: OAuth2/OIDC
  Create: Login APIs
end note

note bottom of Authz
  Add: Role-based access
  Add: Permissions
  Update: All services
end note

note bottom of Integration
  Service-to-service auth
  Security testing
  Production migration
end note

@enduml
