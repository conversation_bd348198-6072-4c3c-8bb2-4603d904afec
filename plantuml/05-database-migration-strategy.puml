@startuml Database Migration Strategy
!theme plain
title Database Migration Strategy - Three Phases

package "Phase 1: Shared Database" {
  [Trial Service] as TrialSvc1
  [Logger Service] as LoggerSvc1
  [User Service] as UserSvc1
  [Shipment Service] as ShipmentSvc1
  
  database "Shared MySQL Database" as SharedDB {
    [Trial Tables] as TrialTables1
    [Logger Tables] as LoggerTables1
    [User Tables] as UserTables1
    [Shipment Tables] as ShipmentTables1
    [Audit Tables] as AuditTables1
  }
  
  TrialSvc1 --> SharedDB
  LoggerSvc1 --> SharedDB
  UserSvc1 --> SharedDB
  ShipmentSvc1 --> SharedDB
  
  note right of SharedDB
    Benefits:
    - Low risk migration
    - Existing transactions work
    - Data consistency maintained
    
    Drawbacks:
    - Still coupled through data
    - Schema changes affect all services
  end note
}

package "Phase 2: Database per Service" {
  [Trial Service] as TrialSvc2
  [Logger Service] as LoggerSvc2
  [User Service] as UserSvc2
  [Shipment Service] as ShipmentSvc2
  
  database "Trial DB" as TrialDB
  database "Logger DB" as LoggerDB
  database "User DB" as UserDB
  database "Shipment DB" as ShipmentDB
  
  TrialSvc2 --> TrialDB
  LoggerSvc2 --> LoggerDB
  UserSvc2 --> UserDB
  ShipmentSvc2 --> ShipmentDB
  
  note right of TrialDB
    Benefits:
    - True service autonomy
    - Independent schema evolution
    - Technology diversity possible
    
    Challenges:
    - Cross-service queries complex
    - Distributed transactions needed
  end note
}

package "Phase 3: Event-Driven with CQRS" {
  [Trial Service] as TrialSvc3
  [Logger Service] as LoggerSvc3
  [User Service] as UserSvc3
  [Shipment Service] as ShipmentSvc3
  
  database "Trial Write DB" as TrialWriteDB
  database "Trial Read DB" as TrialReadDB
  database "Logger Write DB" as LoggerWriteDB
  database "Logger Read DB" as LoggerReadDB
  
  [Event Store] as EventStore
  [Event Bus] as EventBus
  
  TrialSvc3 --> TrialWriteDB
  TrialSvc3 --> TrialReadDB
  LoggerSvc3 --> LoggerWriteDB
  LoggerSvc3 --> LoggerReadDB
  
  TrialSvc3 --> EventBus
  LoggerSvc3 --> EventBus
  UserSvc3 --> EventBus
  ShipmentSvc3 --> EventBus
  
  EventBus --> EventStore
  EventStore --> TrialReadDB
  EventStore --> LoggerReadDB
  
  note right of EventStore
    Benefits:
    - Complete audit trail
    - Time-travel queries
    - Event replay capability
    - Optimized read models
  end note
}

@enduml

@startuml Data Ownership Matrix
!theme plain
title Data Ownership and Access Patterns

package "Data Ownership Matrix" {
  
  class "Trial Data" {
    Primary Owner: Trial Service
    --
    Tables:
    - trial
    - trial_version
    - site
    - trial_unit
    --
    Secondary Access:
    - Audit Service (read-only)
    - Shipment Service (trial context)
    - Logger Service (trial assignments)
  }
  
  class "User Data" {
    Primary Owner: User Service
    --
    Tables:
    - users
    - companies
    - offices
    - access_groups
    --
    Secondary Access:
    - All Services (authentication)
    - Audit Service (user context)
  }
  
  class "Logger Data" {
    Primary Owner: Logger Service
    --
    Tables:
    - clinical_logger
    - sensor_logger
    - logger_models
    - sensor_events
    --
    Secondary Access:
    - Trial Service (logger assignments)
    - Deviation Service (sensor data)
  }
  
  class "Shipment Data" {
    Primary Owner: Shipment Service
    --
    Tables:
    - shipments
    - dispensing_units
    - batches
    - kit_status
    --
    Secondary Access:
    - Trial Service (shipment context)
    - Deviation Service (temperature data)
  }
  
  class "Audit Data" {
    Primary Owner: Audit Service
    --
    Tables:
    - audit_trail
    - audit_value_changes
    - electronic_signatures
    --
    Secondary Access:
    - All Services (compliance queries)
  }
}

@enduml

@startuml Database Migration Steps
!theme plain
title Database Migration Implementation Steps

participant "Migration Team" as Team
participant "Current Monolith" as Monolith
participant "New Service" as Service
participant "Shared Database" as SharedDB
participant "New Database" as NewDB
participant "Event Bus" as EventBus

== Step 1: Extract Service with Shared DB ==
Team -> Service : Create new service
Service -> SharedDB : Access existing tables
Team -> Monolith : Update to call service APIs
note right : Dual-write pattern for safety

== Step 2: Data Migration ==
Team -> NewDB : Create new database schema
Team -> Team : Run data migration scripts
SharedDB -> NewDB : Copy relevant data
Team -> Service : Update service to use new DB
note right : Parallel running for validation

== Step 3: Event-Driven Updates ==
Service -> EventBus : Publish data change events
EventBus -> Service : Subscribe to relevant events
Team -> Monolith : Remove direct DB access
note right : Event sourcing for audit trail

== Step 4: Cleanup ==
Team -> SharedDB : Remove unused tables/columns
Team -> Monolith : Remove obsolete code
Team -> Team : Validate data consistency
note right : Complete service autonomy

note over Team, EventBus
  Migration Principles:
  1. Zero-downtime migration
  2. Rollback capability at each step
  3. Data consistency validation
  4. Performance monitoring
  5. Gradual traffic shifting
end note

@enduml
