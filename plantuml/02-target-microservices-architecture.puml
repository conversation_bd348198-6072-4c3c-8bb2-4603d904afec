@startuml Target Microservices Architecture
!theme plain
title Clinical Module - Target Microservices Architecture

package "Frontend Layer" {
  [Web Application] as WebApp
  [Mobile App] as Mobile
  [External API Clients] as ExtClients
}

package "API Gateway Layer" {
  [Load Balancer] as LB
  [API Gateway] as Gateway
  note right of Gateway
    - Rate limiting
    - Authentication
    - Request routing
    - Circuit breakers
  end note
}

package "Core Business Microservices" {
  package "Trial Management Service" {
    [Trial Service] as TrialSvc
    database "Trial DB" as TrialDB
  }
  
  package "Logger Management Service" {
    [Logger Service] as LoggerSvc
    database "Logger DB" as LoggerDB
  }
  
  package "Shipment Management Service" {
    [Shipment Service] as ShipmentSvc
    database "Shipment DB" as ShipmentDB
  }
  
  package "User Management Service" {
    [User Service] as UserSvc
    database "User DB" as UserDB
  }
  
  package "Deviation Management Service" {
    [Deviation Service] as DeviationSvc
    database "Deviation DB" as DeviationDB
  }
  
  package "Integration Service" {
    [Integration Service] as IntegrationSvc
    database "Integration DB" as IntegrationDB
  }
  
  package "Profile Management Service" {
    [Profile Service] as ProfileSvc
    database "Profile DB" as ProfileDB
  }
}

package "Shared Infrastructure Services" {
  package "Audit Service" {
    [Audit Service] as AuditSvc
    database "Audit DB" as AuditDB
  }
  
  package "Notification Service" {
    [Notification Service] as NotificationSvc
    database "Notification DB" as NotificationDB
  }
  
  package "Configuration Service" {
    [Config Service] as ConfigSvc
    database "Config DB" as ConfigDB
  }
}

package "Message Bus" {
  [Apache Kafka] as Kafka
  [Azure Service Bus] as ASB
}

package "External Systems" {
  [IRT System] as IRT
  [KAA System] as KAA
  [Email Service] as Email
}

package "Infrastructure" {
  [Redis Cache] as Redis
  [Monitoring] as Monitor
  [Logging] as Logging
}

' Frontend to Gateway
WebApp --> LB
Mobile --> LB
ExtClients --> LB
LB --> Gateway

' Gateway to Services
Gateway --> TrialSvc
Gateway --> LoggerSvc
Gateway --> ShipmentSvc
Gateway --> UserSvc
Gateway --> DeviationSvc
Gateway --> IntegrationSvc
Gateway --> ProfileSvc
Gateway --> AuditSvc
Gateway --> NotificationSvc
Gateway --> ConfigSvc

' Services to Databases
TrialSvc --> TrialDB
LoggerSvc --> LoggerDB
ShipmentSvc --> ShipmentDB
UserSvc --> UserDB
DeviationSvc --> DeviationDB
IntegrationSvc --> IntegrationDB
ProfileSvc --> ProfileDB
AuditSvc --> AuditDB
NotificationSvc --> NotificationDB
ConfigSvc --> ConfigDB

' Event-driven communication
TrialSvc --> Kafka
LoggerSvc --> Kafka
ShipmentSvc --> Kafka
UserSvc --> Kafka
DeviationSvc --> Kafka
IntegrationSvc --> Kafka
ProfileSvc --> Kafka

Kafka --> AuditSvc
Kafka --> NotificationSvc

' External integrations
IntegrationSvc --> IRT
LoggerSvc --> KAA
NotificationSvc --> ASB
NotificationSvc --> Email

' Infrastructure connections
TrialSvc --> Redis
LoggerSvc --> Redis
ShipmentSvc --> Redis
UserSvc --> Redis

TrialSvc --> Monitor
LoggerSvc --> Monitor
ShipmentSvc --> Monitor
UserSvc --> Monitor
DeviationSvc --> Monitor
IntegrationSvc --> Monitor
ProfileSvc --> Monitor
AuditSvc --> Monitor
NotificationSvc --> Monitor
ConfigSvc --> Monitor

note bottom of "Core Business Microservices"
  - Independent deployment
  - Database per service
  - Domain-driven boundaries
  - Autonomous teams
end note

note bottom of "Message Bus"
  - Asynchronous communication
  - Event-driven architecture
  - Loose coupling
  - Eventual consistency
end note

@enduml
