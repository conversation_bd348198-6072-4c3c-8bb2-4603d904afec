@startuml Service Communication Patterns
!theme plain
title Service Communication Patterns

package "Synchronous Communication" {

  participant "API Gateway" as Gateway
  participant "Trial Service" as Trial
  participant "User Service" as User
  participant "Profile Service" as Profile
  
  Gateway -> Trial : GET /trials/{id}
  activate Trial
  Trial -> User : GET /users/{userId}
  activate User
  User --> Trial : UserDto
  deactivate User
  Trial -> Profile : GET /profiles/{profileId}
  activate Profile
  Profile --> Trial : ProfileDto
  deactivate Profile
  Trial --> Gateway : TrialDto
  deactivate Trial
  
  note right
    Use for:
    - Real-time queries
    - User authentication
    - Configuration lookups
    - Critical data retrieval
  end note
}

@enduml

@startuml Event-Driven Communication
!theme plain
title Event-Driven Communication Flow

participant "Trial Service" as Trial
participant "Event Bus" as EventBus
participant "Logger Service" as Logger
participant "Audit Service" as Audit
participant "Notification Service" as Notify

Trial -> EventBus : Publish TrialCreated Event
activate EventBus

EventBus -> Logger : TrialCreated Event
activate Logger
Logger -> Logger : Initialize Trial Loggers
Logger -> EventBus : Publish LoggersInitialized Event
deactivate Logger

EventBus -> Audit : TrialCreated Event
activate Audit
Audit -> Audit : Log Audit Trail
deactivate Audit

EventBus -> Notify : TrialCreated Event
activate Notify
Notify -> Notify : Send Notifications
deactivate Notify

deactivate EventBus

note right of EventBus
  Event Types:
  - TrialCreated
  - TrialUpdated
  - LoggerAssigned
  - ShipmentReceived
  - DeviationDetected
  - UserAuthenticated
end note

@enduml

@startuml Data Flow Patterns
!theme plain
title Data Flow and Consistency Patterns

package "Strong Consistency" {
  participant "Client" as Client1
  participant "Trial Service" as Trial1
  participant "Database" as DB1
  
  Client1 -> Trial1 : Create Trial
  activate Trial1
  Trial1 -> DB1 : BEGIN TRANSACTION
  Trial1 -> DB1 : INSERT Trial
  Trial1 -> DB1 : INSERT Sites
  Trial1 -> DB1 : COMMIT
  Trial1 --> Client1 : Success
  deactivate Trial1
  
  note right
    Use for:
    - Critical business operations
    - Financial transactions
    - Regulatory compliance
  end note
}

package "Eventual Consistency" {
  participant "Trial Service" as Trial2
  participant "Event Bus" as EventBus2
  participant "Logger Service" as Logger2
  participant "Audit Service" as Audit2
  
  Trial2 -> EventBus2 : TrialCreated Event
  EventBus2 -> Logger2 : Process Event
  activate Logger2
  Logger2 -> Logger2 : Update Logger Assignments
  Logger2 --> EventBus2 : LoggersUpdated Event
  deactivate Logger2
  
  EventBus2 -> Audit2 : Process Event
  activate Audit2
  Audit2 -> Audit2 : Create Audit Record
  deactivate Audit2
  
  note right
    Use for:
    - Non-critical updates
    - Analytics data
    - Notification sending
    - Audit logging
  end note
}

@enduml

@startuml API Gateway Patterns
!theme plain
title API Gateway Routing and Security

package "API Gateway Layer" {
  [Load Balancer] as LB
  [API Gateway] as Gateway
  [Authentication Service] as Auth
  [Rate Limiter] as RateLimit
  [Circuit Breaker] as CircuitBreaker
}

package "Microservices" {
  [Trial Service] as Trial
  [User Service] as User
  [Logger Service] as Logger
  [Shipment Service] as Shipment
}

actor "Client" as Client

Client -> LB : HTTPS Request
LB -> Gateway : Route Request

Gateway -> Auth : Validate JWT Token
Auth --> Gateway : User Context

Gateway -> RateLimit : Check Rate Limits
RateLimit --> Gateway : Allow/Deny

Gateway -> CircuitBreaker : Check Service Health
CircuitBreaker --> Gateway : Route/Fallback

alt Service Available
  Gateway -> Trial : Forward Request
  Trial --> Gateway : Response
  Gateway --> Client : Success Response
else Service Unavailable
  Gateway --> Client : Fallback Response
end

note right of Gateway
  Gateway Responsibilities:
  - Request routing
  - Authentication/Authorization
  - Rate limiting
  - Circuit breaking
  - Request/Response transformation
  - Logging and monitoring
end note

note right of Auth
  Authentication Methods:
  - JWT tokens
  - OAuth2/OIDC
  - API keys
  - mTLS certificates
end note

@enduml
