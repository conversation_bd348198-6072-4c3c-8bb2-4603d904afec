@startuml Migration Overview
!theme plain
title Migration Overview - All Phases

package "Phase 1\nInfrastructure" #lightgreen {
  [Configuration Service]
  [Audit Service]
  [Notification Service]
}

package "Phase 2\nUser Management" #yellow {
  [User Service]
  [Authentication]
  [Authorization]
}

package "Phase 3\nBusiness Services" #orange {
  [Profile Service]
  [Integration Service]
  [Logger Service]
}

package "Phase 4\nCore Business" #lightcoral {
  [Trial Service]
  [Shipment Service]
  [Deviation Service]
}

"Phase 1\nInfrastructure" --> "Phase 2\nUser Management"
"Phase 2\nUser Management" --> "Phase 3\nBusiness Services"
"Phase 3\nBusiness Services" --> "Phase 4\nCore Business"

note right of "Phase 1\nInfrastructure"
  Risk: LOW
  Foundation services
  14 steps total
end note

note right of "Phase 2\nUser Management"
  Risk: MEDIUM
  Central authentication
  12 steps total
end note

note right of "Phase 3\nBusiness Services"
  Risk: MEDIUM
  Business logic
  16 steps total
end note

note right of "Phase 4\nCore Business"
  Risk: HIGH
  Core workflows
  17 steps total
end note

@enduml
