@startuml Phase 3 Business Services
!theme plain
title Phase 3: Business Services

rectangle "Profile Service" as Profile #orange
rectangle "Integration Service" as IntSvc #orange
rectangle "Logger Service" as Logger #orange
rectangle "Event Architecture" as Events #orange

Profile --> IntSvc : Complete
IntSvc --> Logger : Complete
Logger --> Events : Complete

note bottom of Profile
  Extract: ProfileEntity
  Add: Profile APIs
  Add: Versioning
end note

note bottom of IntSvc
  Extract: IRT integration
  Extract: KAA integration
  Add: Message routing
end note

note bottom of Logger
  Extract: ClinicalLoggerEntity
  Add: Sensor APIs
  Add: Live data streams
end note

note bottom of Events
  Event-driven communication
  Distributed tracing
  Performance testing
end note

@enduml
