@startuml Phase 4 Core Business Services
!theme plain
title Phase 4: Core Business Services

rectangle "Trial Service" as Trial #lightcoral
rectangle "Shipment Service" as Shipment #lightcoral
rectangle "Deviation Service" as Deviation #lightcoral
rectangle "Go Live" as GoLive #lightcoral

Trial --> Shipment : Complete
Shipment --> Deviation : Complete
Deviation --> GoLive : Complete

note bottom of Trial
  Extract: TrialEntity
  Add: Trial workflows
  Add: Site management
end note

note bottom of Shipment
  Extract: ShipmentEntity
  Add: Kit tracking
  Add: Inventory APIs
end note

note bottom of Deviation
  Extract: DeviationEntity
  Add: Detection logic
  Add: Adjustment workflows
end note

note bottom of GoLive
  Final integration
  Production deployment
  Legacy cleanup
end note

@enduml
