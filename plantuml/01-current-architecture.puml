@startuml Current Monolithic Architecture
!theme plain
title Clinical Module - Current Monolithic Architecture

package "Frontend Layer" {
  [Web UI] as WebUI
  [Mobile App] as Mobile
  [External API Clients] as ExtClients
}

package "API Layer" {
  [Jersey REST API] as API
  [Gateway Module] as Gateway
}

package "Business Logic Layer" {
  package "Clinical Module" {
    [Trial Service] as TS
    [Logger Service] as LS
    [Shipment Service] as SS
    [Deviation Service] as DS
    [Profile Service] as PS
  }
  
  package "Core Services" {
    [User Service] as US
    [Integration Service] as IS
    [Audit Service] as AS
    [Notification Service] as NS
    [Config Service] as CS
  }
}

package "Data Access Layer" {
  package "Managers" {
    [Trial Manager] as TM
    [Logger Manager] as LM
    [Shipment Manager] as SM
    [User Manager] as UM
    [Deviation Manager] as DM
    [Integration Manager] as IM
    [Audit Manager] as AM
    [Notification Manager] as NM
    [Config Manager] as CM
  }
}

package "Data Layer" {
  database "MySQL Database" as DB {
    [Trial Tables] as TrialTables
    [Logger Tables] as LoggerTables
    [Shipment Tables] as ShipmentTables
    [User Tables] as UserTables
    [Deviation Tables] as DeviationTables
    [Audit Tables] as AuditTables
    [Config Tables] as ConfigTables
  }
}

package "External Systems" {
  [IRT System] as IRT
  [KAA System] as KAA
  [Azure Service Bus] as ASB
  [Email Service] as Email
}

' Frontend connections
WebUI --> Gateway
Mobile --> Gateway
ExtClients --> Gateway

' API connections
Gateway --> API
API --> TS
API --> LS
API --> SS
API --> DS
API --> PS
API --> US
API --> IS
API --> AS
API --> NS
API --> CS

' Service to Manager connections
TS --> TM
LS --> LM
SS --> SM
US --> UM
DS --> DM
IS --> IM
AS --> AM
NS --> NM
CS --> CM

' Manager to Database connections
TM --> TrialTables
LM --> LoggerTables
SM --> ShipmentTables
UM --> UserTables
DM --> DeviationTables
AM --> AuditTables
CM --> ConfigTables

' External integrations
IS --> IRT
IS --> KAA
NS --> ASB
NS --> Email

note right of DB
  Single shared database
  All modules access same schema
  Tight coupling through data
end note

note right of "Business Logic Layer"
  Monolithic deployment
  All services in single WAR
  Shared dependencies
  Single point of failure
end note

@enduml
