@startuml All Migration Steps
!theme plain
title Clinical Module - All Migration Steps

' Phase 1: Infrastructure Services
rectangle "1. Configuration Service\nExtract ConfigManager" as Step1 #lightgreen
rectangle "2. Audit Service\nExtract AuditTrailManager" as Step2 #lightgreen
rectangle "3. Notification Service\nExtract NotificationManager" as Step3 #lightgreen
rectangle "4. Infrastructure Testing\nEnd-to-end validation" as Step4 #lightgreen

' Phase 2: User Management
rectangle "5. User Service\nExtract UserEntity" as Step5 #yellow
rectangle "6. Authentication\nJWT + OAuth2" as Step6 #yellow
rectangle "7. Authorization\nRole-based access" as Step7 #yellow
rectangle "8. User Integration\nUpdate all services" as Step8 #yellow

' Phase 3: Business Services
rectangle "9. Profile Service\nExtract ProfileEntity" as Step9 #orange
rectangle "10. Integration Service\nIRT + KAA integration" as Step10 #orange
rectangle "11. Logger Service\nExtract ClinicalLoggerEntity" as Step11 #orange
rectangle "12. Event Architecture\nKafka + distributed tracing" as Step12 #orange

' Phase 4: Core Business
rectangle "13. Trial Service\nExtract TrialEntity" as Step13 #lightcoral
rectangle "14. Shipment Service\nExtract ShipmentEntity" as Step14 #lightcoral
rectangle "15. Deviation Service\nExtract DeviationEntity" as Step15 #lightcoral
rectangle "16. Production Go-Live\nFinal deployment" as Step16 #lightcoral

' Flow connections
Step1 --> Step2
Step2 --> Step3
Step3 --> Step4
Step4 --> Step5
Step5 --> Step6
Step6 --> Step7
Step7 --> Step8
Step8 --> Step9
Step9 --> Step10
Step10 --> Step11
Step11 --> Step12
Step12 --> Step13
Step13 --> Step14
Step14 --> Step15
Step15 --> Step16

' Phase labels
note top of Step1
  **Phase 1: Infrastructure Services**
  Risk: LOW | Foundation services
end note

note top of Step5
  **Phase 2: User Management**
  Risk: MEDIUM | Central authentication
end note

note top of Step9
  **Phase 3: Business Services**
  Risk: MEDIUM | Business logic
end note

note top of Step13
  **Phase 4: Core Business Services**
  Risk: HIGH | Core workflows
end note

@enduml
