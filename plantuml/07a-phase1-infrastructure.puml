@startuml Phase 1 Infrastructure Services
!theme plain
title Phase 1: Infrastructure Services

rectangle "Configuration Service" as Config #lightgreen
rectangle "Audit Service" as Audit #lightgreen  
rectangle "Notification Service" as Notify #lightgreen
rectangle "Testing & Deployment" as Test #lightgreen

Config --> Audit : Complete
Audit --> Notify : Complete
Notify --> Test : Complete

note bottom of Config
  Extract: ConfigManager
  Create: REST APIs
  Add: Redis caching
end note

note bottom of Audit
  Extract: AuditTrailManager
  Add: Kafka events
  Create: Audit APIs
end note

note bottom of Notify
  Extract: NotificationManager
  Add: Message queues
  Create: Email service
end note

note bottom of Test
  End-to-end testing
  Production deployment
  Monitoring setup
end note

@enduml
