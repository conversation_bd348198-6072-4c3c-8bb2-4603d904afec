@startuml Deployment Architecture
!theme plain
title Microservices Deployment Architecture

package "Load Balancer Tier" {
  [External Load Balancer] as ExtLB
  [Internal Load Balancer] as IntLB
}

package "API Gateway Tier" {
  [API Gateway 1] as GW1
  [API Gateway 2] as GW2
  [API Gateway 3] as GW3
}

package "Microservices Tier" {
  package "Trial Service Cluster" {
    [Trial Service 1] as TS1
    [Trial Service 2] as TS2
    [Trial Service 3] as TS3
  }
  
  package "User Service Cluster" {
    [User Service 1] as US1
    [User Service 2] as US2
  }
  
  package "Logger Service Cluster" {
    [Logger Service 1] as LS1
    [Logger Service 2] as LS2
  }
  
  package "Shipment Service Cluster" {
    [Shipment Service 1] as SS1
    [Shipment Service 2] as SS2
  }
}

package "Data Tier" {
  database "Trial DB\n(Master/Slave)" as TrialDB
  database "User DB\n(Master/Slave)" as UserDB
  database "Logger DB\n(Master/Slave)" as LoggerDB
  database "Shipment DB\n(Master/Slave)" as ShipmentDB
}

package "Infrastructure Services" {
  [Redis Cluster] as Redis
  [Kafka Cluster] as Kafka
  [Monitoring Stack] as Monitor
  [Logging Stack] as Logging
}

ExtLB --> GW1
ExtLB --> GW2
ExtLB --> GW3

GW1 --> IntLB
GW2 --> IntLB
GW3 --> IntLB

IntLB --> TS1
IntLB --> TS2
IntLB --> TS3
IntLB --> US1
IntLB --> US2
IntLB --> LS1
IntLB --> LS2
IntLB --> SS1
IntLB --> SS2

TS1 --> TrialDB
TS2 --> TrialDB
TS3 --> TrialDB

US1 --> UserDB
US2 --> UserDB

LS1 --> LoggerDB
LS2 --> LoggerDB

SS1 --> ShipmentDB
SS2 --> ShipmentDB

TS1 --> Redis
TS1 --> Kafka
US1 --> Redis
US1 --> Kafka
LS1 --> Kafka
SS1 --> Kafka

note right of "Microservices Tier"
  Deployment Strategy:
  - Blue-Green deployments
  - Rolling updates
  - Canary releases
  - Auto-scaling based on metrics
end note

@enduml

@startuml Container Orchestration
!theme plain
title Kubernetes Deployment Strategy

package "Kubernetes Cluster" {
  
  package "Namespace: clinical-prod" {
    
    package "Trial Service" {
      [Trial Pod 1] as TP1
      [Trial Pod 2] as TP2
      [Trial Pod 3] as TP3
      [Trial Service] as TSvc
      [Trial ConfigMap] as TCM
      [Trial Secret] as TSec
    }
    
    package "User Service" {
      [User Pod 1] as UP1
      [User Pod 2] as UP2
      [User Service] as USvc
      [User ConfigMap] as UCM
      [User Secret] as USec
    }
    
    package "Infrastructure" {
      [Ingress Controller] as Ingress
      [Service Mesh] as Mesh
      [Monitoring] as Mon
    }
  }
  
  package "Namespace: clinical-staging" {
    [Staging Services] as Staging
  }
  
  package "Namespace: clinical-dev" {
    [Development Services] as Dev
  }
}

package "External Resources" {
  [External Database] as ExtDB
  [External Cache] as ExtCache
  [External Message Queue] as ExtMQ
}

Ingress --> TSvc
Ingress --> USvc

TSvc --> TP1
TSvc --> TP2
TSvc --> TP3

USvc --> UP1
USvc --> UP2

TP1 --> TCM
TP1 --> TSec
UP1 --> UCM
UP1 --> USec

TP1 --> ExtDB
UP1 --> ExtDB
TP1 --> ExtCache
UP1 --> ExtCache
TP1 --> ExtMQ
UP1 --> ExtMQ

note right of "Namespace: clinical-prod"
  Production Features:
  - Resource quotas
  - Network policies
  - Pod security policies
  - Horizontal pod autoscaling
  - Persistent volumes
end note

@enduml

@startuml CI/CD Pipeline
!theme plain
title CI/CD Pipeline for Microservices

participant "Developer" as Dev
participant "Git Repository" as Git
participant "CI/CD Pipeline" as Pipeline
participant "Container Registry" as Registry
participant "Kubernetes" as K8s
participant "Monitoring" as Monitor

Dev -> Git : Push code changes
Git -> Pipeline : Trigger build

activate Pipeline

Pipeline -> Pipeline : Run unit tests
Pipeline -> Pipeline : Run integration tests
Pipeline -> Pipeline : Security scanning
Pipeline -> Pipeline : Build Docker image
Pipeline -> Registry : Push image

alt Deployment to Staging
  Pipeline -> K8s : Deploy to staging namespace
  Pipeline -> Pipeline : Run E2E tests
  Pipeline -> Pipeline : Performance tests
end

alt Deployment to Production
  Pipeline -> K8s : Blue-Green deployment
  Pipeline -> Monitor : Health checks
  
  alt Health checks pass
    Pipeline -> K8s : Switch traffic to green
    Pipeline -> K8s : Terminate blue environment
  else Health checks fail
    Pipeline -> K8s : Rollback to blue
    Pipeline -> Monitor : Alert operations team
  end
end

deactivate Pipeline

note right of Pipeline
  Pipeline Stages:
  1. Code quality checks
  2. Unit tests (80% coverage)
  3. Integration tests
  4. Security scanning
  5. Container build
  6. Staging deployment
  7. E2E tests
  8. Production deployment
  9. Health validation
  10. Monitoring setup
end note

@enduml

@startuml Monitoring and Observability
!theme plain
title Monitoring and Observability Stack

package "Application Layer" {
  [Trial Service] as TS
  [User Service] as US
  [Logger Service] as LS
  [Shipment Service] as SS
}

package "Metrics Collection" {
  [Prometheus] as Prom
  [Micrometer] as Micro
  [Custom Metrics] as Custom
}

package "Logging" {
  [Logback] as Logback
  [Elasticsearch] as ES
  [Logstash] as Logstash
  [Kibana] as Kibana
}

package "Tracing" {
  [Jaeger] as Jaeger
  [Zipkin] as Zipkin
  [OpenTelemetry] as OTel
}

package "Visualization" {
  [Grafana] as Grafana
  [Dashboards] as Dash
  [Alerts] as Alerts
}

package "Health Checks" {
  [Spring Actuator] as Actuator
  [Kubernetes Probes] as Probes
  [External Health] as ExtHealth
}

TS -> Micro : Metrics
US -> Micro : Metrics
LS -> Micro : Metrics
SS -> Micro : Metrics

Micro -> Prom : Expose metrics
Custom -> Prom : Custom metrics

TS -> Logback : Application logs
US -> Logback : Application logs
LS -> Logback : Application logs
SS -> Logback : Application logs

Logback -> Logstash : Ship logs
Logstash -> ES : Index logs
ES -> Kibana : Query logs

TS -> OTel : Trace data
US -> OTel : Trace data
LS -> OTel : Trace data
SS -> OTel : Trace data

OTel -> Jaeger : Distributed traces
OTel -> Zipkin : Backup traces

Prom -> Grafana : Metrics data
Grafana -> Dash : Visualizations
Grafana -> Alerts : Alert rules

TS -> Actuator : Health endpoints
US -> Actuator : Health endpoints
LS -> Actuator : Health endpoints
SS -> Actuator : Health endpoints

Actuator -> Probes : K8s health checks
Probes -> ExtHealth : External monitoring

note bottom
  Key Metrics to Monitor:
  - Request rate and latency
  - Error rates and types
  - Database connection pools
  - Memory and CPU usage
  - Business metrics (trials created, etc.)
  - Security events
end note

@enduml
