@startuml Migration Timeline Alternative
!theme plain
title Clinical Module - Migration Timeline (Alternative View)

skinparam timeline {
    BackgroundColor lightblue
}

robust "Phase 1: Infrastructure" as P1
robust "Phase 2: User Management" as P2
robust "Phase 3: Business Services" as P3
robust "Phase 4: Core Business" as P4

@0
P1 is Planning
P2 is Waiting
P3 is Waiting
P4 is Waiting

@2
P1 is "Config Service"
P2 is Waiting
P3 is Waiting
P4 is Waiting

@4
P1 is "Audit Service"
P2 is Waiting
P3 is Waiting
P4 is Waiting

@6
P1 is "Notification Service"
P2 is Waiting
P3 is Waiting
P4 is Waiting

@8
P1 is "Testing & Integration"
P2 is Planning
P3 is Waiting
P4 is Waiting

@10
P1 is Complete
P2 is "User Foundation"
P3 is Waiting
P4 is Waiting

@12
P1 is Complete
P2 is "Auth & Authorization"
P3 is Waiting
P4 is Waiting

@14
P1 is Complete
P2 is "Service Integration"
P3 is Waiting
P4 is Waiting

@16
P1 is Complete
P2 is "Testing & Migration"
P3 is Planning
P4 is Waiting

@20
P1 is Complete
P2 is Complete
P3 is "Profile Service"
P4 is Waiting

@24
P1 is Complete
P2 is Complete
P3 is "Integration Service"
P4 is Waiting

@28
P1 is Complete
P2 is Complete
P3 is "Logger Service"
P4 is Waiting

@32
P1 is Complete
P2 is Complete
P3 is "Service Integration"
P4 is Planning

@36
P1 is Complete
P2 is Complete
P3 is Complete
P4 is "Trial Service"

@40
P1 is Complete
P2 is Complete
P3 is Complete
P4 is "Shipment Service"

@44
P1 is Complete
P2 is Complete
P3 is Complete
P4 is "Deviation Service"

@48
P1 is Complete
P2 is Complete
P3 is Complete
P4 is "Final Integration"

@52
P1 is Complete
P2 is Complete
P3 is Complete
P4 is Complete

@enduml

@startuml Simple Timeline View
!theme plain
title Migration Phases - Simple Timeline View

scale 1.5

!define MONTH_WIDTH 100

rectangle "Migration Timeline" {
  
  rectangle "Months 1-2" as M12 #lightgreen {
    rectangle "Week 1-2" as W12 #palegreen
    rectangle "Week 3-4" as W34 #palegreen
    rectangle "Week 5-6" as W56 #palegreen
    rectangle "Week 7-8" as W78 #palegreen
  }
  
  rectangle "Months 3-4" as M34 #lightyellow {
    rectangle "Week 9-10" as W910 #palegoldenrod
    rectangle "Week 11-12" as W1112 #palegoldenrod
    rectangle "Week 13-14" as W1314 #palegoldenrod
    rectangle "Week 15-16" as W1516 #palegoldenrod
  }
  
  rectangle "Months 5-8" as M58 #lightblue {
    rectangle "Month 5" as M5 #lightcyan
    rectangle "Month 6" as M6 #lightcyan
    rectangle "Month 7" as M7 #lightcyan
    rectangle "Month 8" as M8 #lightcyan
  }
  
  rectangle "Months 9-12" as M912 #lightcoral {
    rectangle "Month 9" as M9 #mistyrose
    rectangle "Month 10" as M10 #mistyrose
    rectangle "Month 11" as M11 #mistyrose
    rectangle "Month 12" as M12_final #mistyrose
  }
}

note top of M12
  **Phase 1: Infrastructure Services**
  Risk: LOW
  - Configuration Service
  - Audit Service  
  - Notification Service
  - Integration Testing
end note

note top of M34
  **Phase 2: User Management**
  Risk: MEDIUM
  - User Service Foundation
  - Authentication & Authorization
  - Service Integration
  - Migration & Testing
end note

note top of M58
  **Phase 3: Business Services**
  Risk: MEDIUM
  - Profile Management Service
  - Integration Service
  - Logger Management Service
  - Service Integration
end note

note top of M912
  **Phase 4: Core Business**
  Risk: HIGH
  - Trial Management Service
  - Shipment Management Service
  - Deviation Management Service
  - Final Integration & Go-Live
end note

@enduml

@startuml Milestone Timeline
!theme plain
title Migration Milestones and Deliverables

!procedure $milestone($name, $date, $color)
  rectangle "$name\n$date" as $name $color
!endprocedure

!procedure $deliverable($name, $desc)
  note as $name
    **$name**
    $desc
  end note
!endprocedure

$milestone("M1", "Month 2", "#90EE90")
$milestone("M2", "Month 4", "#FFE4B5") 
$milestone("M3", "Month 8", "#87CEEB")
$milestone("M4", "Month 12", "#F0A0A0")

M1 -right-> M2
M2 -right-> M3  
M3 -right-> M4

$deliverable("D1", "Infrastructure services deployed\nConfiguration, Audit, Notification\nMonitoring infrastructure ready")
$deliverable("D2", "User management centralized\nSSO implemented\nSecurity standardized")
$deliverable("D3", "Business services extracted\nEvent-driven architecture\nIndependent deployments")
$deliverable("D4", "Full microservices architecture\nCore business services migrated\nProduction ready")

M1 .. D1
M2 .. D2
M3 .. D3
M4 .. D4

@enduml
