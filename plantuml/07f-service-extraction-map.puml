@startuml Service Extraction Map
!theme plain
title What Gets Extracted in Each Phase

map "Phase 1: Infrastructure" as phase1 {
  Configuration Service => ConfigManager class
  Audit Service => AuditTrailManager class
  Notification Service => NotificationManager class
}

map "Phase 2: User Management" as phase2 {
  User Service => UserEntity, CompanyEntity
  Authentication => JWT, OAuth2 setup
  Authorization => Role-based access control
}

map "Phase 3: Business Services" as phase3 {
  Profile Service => ProfileEntity, ProfileVersionEntity
  Integration Service => IrtIntegrationDetailsEntity
  Logger Service => ClinicalLoggerEntity, SensorLoggerEntity
}

map "Phase 4: Core Business" as phase4 {
  Trial Service => TrialEntity, TrialVersionEntity, SiteEntity
  Shipment Service => ShipmentEntity, DispensingUnitEntity
  Deviation Service => DeviationEntity, AdjustmentSetEntity
}

phase1 --> phase2
phase2 --> phase3
phase3 --> phase4

@enduml
