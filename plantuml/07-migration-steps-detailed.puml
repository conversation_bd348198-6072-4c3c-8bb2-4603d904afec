@startuml Migration Steps Detailed
!theme plain
title Clinical Module - Detailed Migration Steps

!define PHASE1_COLOR #E8F5E8
!define PHASE2_COLOR #FFF2CC
!define PHASE3_COLOR #FFE6CC
!define PHASE4_COLOR #F8CECC

package "Phase 1: Infrastructure Services" PHASE1_COLOR {

  package "Configuration Service Migration" {
    rectangle "Step 1.1\nExtract ConfigManager" as Step11 {
      note bottom
        • Create Spring Boot project
        • Extract ConfigManager class
        • Extract configuration entities
        • Set up MySQL database schema
      end note
    }

    rectangle "Step 1.2\nImplement REST API" as Step12 {
      note bottom
        • Create ConfigurationController
        • Implement CRUD operations
        • Add validation and error handling
        • Set up API documentation
      end note
    }

    rectangle "Step 1.3\nAdd Caching Layer" as Step13 {
      note bottom
        • Integrate Redis cache
        • Implement cache strategies
        • Add cache invalidation
        • Performance optimization
      end note
    }

    rectangle "Step 1.4\nUpdate Monolith" as Step14 {
      note bottom
        • Replace direct DB calls
        • Use Configuration Service API
        • Implement fallback mechanisms
        • Gradual migration approach
      end note
    }
  }

  package "Audit Service Migration" {
    rectangle "Step 2.1\nExtract AuditTrailManager" as Step21 {
      note bottom
        • Create Audit Service project
        • Extract AuditTrailManager
        • Extract audit entities
        • Set up audit database
      end note
    }

    rectangle "Step 2.2\nImplement Event-Driven Logging" as Step22 {
      note bottom
        • Set up Kafka integration
        • Create audit event schemas
        • Implement event publishers
        • Add event consumers
      end note
    }

    rectangle "Step 2.3\nCreate Audit APIs" as Step23 {
      note bottom
        • Implement audit query APIs
        • Add compliance reporting
        • Create audit trail views
        • Add search capabilities
      end note
    }

    rectangle "Step 2.4\nMigrate Audit Data" as Step24 {
      note bottom
        • Data migration scripts
        • Validate data integrity
        • Update monolith integration
        • Performance testing
      end note
    }
  }

  package "Notification Service Migration" {
    rectangle "Step 3.1\nExtract NotificationManager" as Step31 {
      note bottom
        • Create Notification Service
        • Extract NotificationManager
        • Extract notification entities
        • Set up notification database
      end note
    }

    rectangle "Step 3.2\nImplement Message Queuing" as Step32 {
      note bottom
        • Azure Service Bus integration
        • Message queue setup
        • Dead letter queue handling
        • Retry mechanisms
      end note
    }

    rectangle "Step 3.3\nEmail Service Integration" as Step33 {
      note bottom
        • Email service setup
        • Template management
        • Email scheduling
        • Delivery tracking
      end note
    }

    rectangle "Step 3.4\nNotification APIs" as Step34 {
      note bottom
        • REST APIs for notifications
        • Webhook support
        • Real-time notifications
        • Notification preferences
      end note
    }
  }

  package "Integration Testing Phase 1" {
    rectangle "Step 4.1\nEnd-to-End Testing" as Step41 {
      note bottom
        • Test all infrastructure services
        • Integration test scenarios
        • Performance benchmarking
        • Load testing
      end note
    }

    rectangle "Step 4.2\nProduction Deployment" as Step42 {
      note bottom
        • Blue-green deployment
        • Monitoring setup
        • Health checks
        • Rollback procedures
      end note
    }
  }
}

Step11 --> Step12
Step12 --> Step13
Step13 --> Step14
Step14 --> Step21

Step21 --> Step22
Step22 --> Step23
Step23 --> Step24
Step24 --> Step31

Step31 --> Step32
Step32 --> Step33
Step33 --> Step34
Step34 --> Step41

Step41 --> Step42

@enduml

@startuml Phase 2 User Management Steps
!theme plain
title Phase 2: User Management Service Migration Steps

!define PHASE2_COLOR #FFF2CC

package "Phase 2: User Management Service" PHASE2_COLOR {

  package "User Service Foundation" {
    rectangle "Step 1\nExtract User Entities" as UserStep1 {
      note bottom
        • Extract UserEntity
        • Extract CompanyEntity
        • Extract OfficeEntity
        • Extract AccessGroupEntity
      end note
    }

    rectangle "Step 2\nCreate User Database" as UserStep2 {
      note bottom
        • Set up dedicated user database
        • Create database schema
        • Set up connection pooling
        • Database migration scripts
      end note
    }

    rectangle "Step 3\nImplement User CRUD" as UserStep3 {
      note bottom
        • User management APIs
        • Company management APIs
        • Office management APIs
        • Access group management
      end note
    }

    rectangle "Step 4\nData Migration" as UserStep4 {
      note bottom
        • Migrate user data
        • Validate data integrity
        • Set up data synchronization
        • Rollback procedures
      end note
    }
  }

  package "Authentication & Authorization" {
    rectangle "Step 5\nJWT Token Service" as UserStep5 {
      note bottom
        • Implement JWT generation
        • Token validation
        • Token refresh mechanism
        • Security key management
      end note
    }

    rectangle "Step 6\nOAuth2/OIDC Integration" as UserStep6 {
      note bottom
        • OAuth2 provider setup
        • OIDC integration
        • SSO configuration
        • External identity providers
      end note
    }

    rectangle "Step 7\nRole-Based Access Control" as UserStep7 {
      note bottom
        • RBAC implementation
        • Permission management
        • Role hierarchies
        • Access control policies
      end note
    }

    rectangle "Step 8\nSession Management" as UserStep8 {
      note bottom
        • Session store setup
        • Session validation
        • Session timeout handling
        • Concurrent session management
      end note
    }
  }

  package "Service Integration" {
    rectangle "Step 9\nUpdate All Services" as UserStep9 {
      note bottom
        • Update monolith authentication
        • Service-to-service auth
        • API security implementation
        • Security headers setup
      end note
    }

    rectangle "Step 10\nUser Context Propagation" as UserStep10 {
      note bottom
        • Request context setup
        • User context passing
        • Audit trail integration
        • Security context management
      end note
    }

    rectangle "Step 11\nSecurity Testing" as UserStep11 {
      note bottom
        • Security penetration testing
        • Authentication testing
        • Authorization testing
        • Vulnerability assessment
      end note
    }

    rectangle "Step 12\nProduction Migration" as UserStep12 {
      note bottom
        • Gradual user migration
        • SSO rollout
        • Monitoring and alerting
        • Performance validation
      end note
    }
  }
}

UserStep1 --> UserStep2
UserStep2 --> UserStep3
UserStep3 --> UserStep4
UserStep4 --> UserStep5

UserStep5 --> UserStep6
UserStep6 --> UserStep7
UserStep7 --> UserStep8
UserStep8 --> UserStep9

UserStep9 --> UserStep10
UserStep10 --> UserStep11
UserStep11 --> UserStep12

@enduml

@startuml Phase 3 Business Services Steps
!theme plain
title Phase 3: Business Services Migration Steps

!define PHASE3_COLOR #FFE6CC

package "Phase 3: Business Services" PHASE3_COLOR {

  package "Profile Management Service" {
    rectangle "Step 1\nExtract Profile Entities" as ProfileStep1 {
      note bottom
        • Extract ProfileEntity
        • Extract ProfileVersionEntity
        • Extract StabilityConfigurationEntity
        • Extract related entities
      end note
    }

    rectangle "Step 2\nProfile Service APIs" as ProfileStep2 {
      note bottom
        • Profile CRUD operations
        • Profile versioning APIs
        • Configuration management
        • Template operations
      end note
    }

    rectangle "Step 3\nProfile Workflows" as ProfileStep3 {
      note bottom
        • Profile approval workflows
        • Version management
        • Blinding operations
        • Configuration validation
      end note
    }

    rectangle "Step 4\nProfile Data Migration" as ProfileStep4 {
      note bottom
        • Migrate profile data
        • Validate relationships
        • Update references
        • Performance optimization
      end note
    }
  }

  package "Integration Service" {
    rectangle "Step 5\nExtract Integration Logic" as IntStep1 {
      note bottom
        • Extract IrtIntegrationDetailsEntity
        • Extract IntegrationEntity
        • Extract integration managers
        • Set up integration database
      end note
    }

    rectangle "Step 6\nIRT Integration" as IntStep2 {
      note bottom
        • IRT client implementation
        • Message transformation
        • Error handling
        • Retry mechanisms
      end note
    }

    rectangle "Step 7\nKAA Integration" as IntStep3 {
      note bottom
        • KAA client implementation
        • Endpoint management
        • Event processing
        • Data synchronization
      end note
    }

    rectangle "Step 8\nMessage Routing" as IntStep4 {
      note bottom
        • Message routing logic
        • Protocol translation
        • Queue management
        • Integration monitoring
      end note
    }
  }

  package "Logger Management Service" {
    rectangle "Step 9\nExtract Logger Entities" as LoggerStep1 {
      note bottom
        • Extract ClinicalLoggerEntity
        • Extract SensorLoggerEntity
        • Extract LoggerModelEntity
        • Extract sensor entities
      end note
    }

    rectangle "Step 10\nLogger APIs" as LoggerStep2 {
      note bottom
        • Logger management APIs
        • Sensor data collection
        • Logger registration
        • Status monitoring
      end note
    }

    rectangle "Step 11\nLive Data Subscriptions" as LoggerStep3 {
      note bottom
        • Real-time data streaming
        • Subscription management
        • Data validation
        • Alert generation
      end note
    }

    rectangle "Step 12\nLogger Data Migration" as LoggerStep4 {
      note bottom
        • Migrate logger data
        • Sensor data migration
        • Validate data integrity
        • Performance tuning
      end note
    }
  }

  package "Service Integration Phase 3" {
    rectangle "Step 13\nEvent-Driven Communication" as EventStep1 {
      note bottom
        • Event schema design
        • Event publishers
        • Event consumers
        • Event ordering
      end note
    }

    rectangle "Step 14\nDistributed Tracing" as EventStep2 {
      note bottom
        • Tracing setup
        • Correlation IDs
        • Performance monitoring
        • Debugging tools
      end note
    }

    rectangle "Step 15\nPerformance Testing" as EventStep3 {
      note bottom
        • Load testing
        • Stress testing
        • Performance optimization
        • Capacity planning
      end note
    }

    rectangle "Step 16\nProduction Deployment" as EventStep4 {
      note bottom
        • Service deployment
        • Monitoring setup
        • Health checks
        • Rollback procedures
      end note
    }
  }
}

ProfileStep1 --> ProfileStep2
ProfileStep2 --> ProfileStep3
ProfileStep3 --> ProfileStep4
ProfileStep4 --> IntStep1

IntStep1 --> IntStep2
IntStep2 --> IntStep3
IntStep3 --> IntStep4
IntStep4 --> LoggerStep1

LoggerStep1 --> LoggerStep2
LoggerStep2 --> LoggerStep3
LoggerStep3 --> LoggerStep4
LoggerStep4 --> EventStep1

EventStep1 --> EventStep2
EventStep2 --> EventStep3
EventStep3 --> EventStep4

@enduml

@startuml Phase 4 Core Business Services Steps
!theme plain
title Phase 4: Core Business Services Migration Steps

!define PHASE4_COLOR #F8CECC

package "Phase 4: Core Business Services" PHASE4_COLOR {

  package "Trial Management Service" {
    rectangle "Step 1\nExtract Trial Entities" as TrialStep1 {
      note bottom
        • Extract TrialEntity
        • Extract TrialVersionEntity
        • Extract SiteEntity
        • Extract TrialUnitEntity
      end note
    }

    rectangle "Step 2\nTrial Service APIs" as TrialStep2 {
      note bottom
        • Trial CRUD operations
        • Site management APIs
        • Trial unit management
        • Version control APIs
      end note
    }

    rectangle "Step 3\nTrial Workflows" as TrialStep3 {
      note bottom
        • Trial creation workflows
        • Approval processes
        • Status management
        • Site assignment logic
      end note
    }

    rectangle "Step 4\nSaga Pattern Implementation" as TrialStep4 {
      note bottom
        • Distributed transaction handling
        • Compensation logic
        • Workflow orchestration
        • Error recovery
      end note
    }
  }

  package "Shipment Management Service" {
    rectangle "Step 5\nExtract Shipment Entities" as ShipmentStep1 {
      note bottom
        • Extract ShipmentEntity
        • Extract DispensingUnitEntity
        • Extract BatchEntity
        • Extract kit status entities
      end note
    }

    rectangle "Step 6\nShipment APIs" as ShipmentStep2 {
      note bottom
        • Shipment tracking APIs
        • Kit management APIs
        • Batch operations
        • Status update APIs
      end note
    }

    rectangle "Step 7\nInventory Management" as ShipmentStep3 {
      note bottom
        • Inventory tracking
        • Stock management
        • Allocation logic
        • Expiry management
      end note
    }

    rectangle "Step 8\nIRT Integration" as ShipmentStep4 {
      note bottom
        • Kit status updates to IRT
        • Shipment notifications
        • Data synchronization
        • Error handling
      end note
    }
  }

  package "Deviation Management Service" {
    rectangle "Step 9\nExtract Deviation Entities" as DeviationStep1 {
      note bottom
        • Extract DeviationEntity
        • Extract AdjustmentSetEntity
        • Extract ManualAdjustmentEntity
        • Extract related entities
      end note
    }

    rectangle "Step 10\nDeviation Detection" as DeviationStep2 {
      note bottom
        • Temperature deviation logic
        • Threshold monitoring
        • Alert generation
        • Automated detection
      end note
    }

    rectangle "Step 11\nAdjustment Workflows" as DeviationStep3 {
      note bottom
        • Manual adjustment processes
        • Approval workflows
        • Batch adjustments
        • Validation logic
      end note
    }

    rectangle "Step 12\nCompliance Reporting" as DeviationStep4 {
      note bottom
        • Regulatory reporting
        • Audit trail integration
        • Compliance validation
        • Report generation
      end note
    }
  }

  package "Final Integration & Go-Live" {
    rectangle "Step 13\nEnd-to-End Testing" as FinalStep1 {
      note bottom
        • Complete workflow testing
        • Integration testing
        • Performance validation
        • User acceptance testing
      end note
    }

    rectangle "Step 14\nData Consistency Validation" as FinalStep2 {
      note bottom
        • Cross-service data validation
        • Eventual consistency checks
        • Data integrity verification
        • Reconciliation processes
      end note
    }

    rectangle "Step 15\nProduction Deployment" as FinalStep3 {
      note bottom
        • Blue-green deployment
        • Traffic switching
        • Monitoring activation
        • Performance monitoring
      end note
    }

    rectangle "Step 16\nMonitoring & Alerting" as FinalStep4 {
      note bottom
        • Comprehensive monitoring
        • Alert configuration
        • Dashboard setup
        • On-call procedures
      end note
    }

    rectangle "Step 17\nLegacy System Decommission" as FinalStep5 {
      note bottom
        • Monolith cleanup
        • Database cleanup
        • Infrastructure cleanup
        • Documentation update
      end note
    }
  }
}

TrialStep1 --> TrialStep2
TrialStep2 --> TrialStep3
TrialStep3 --> TrialStep4
TrialStep4 --> ShipmentStep1

ShipmentStep1 --> ShipmentStep2
ShipmentStep2 --> ShipmentStep3
ShipmentStep3 --> ShipmentStep4
ShipmentStep4 --> DeviationStep1

DeviationStep1 --> DeviationStep2
DeviationStep2 --> DeviationStep3
DeviationStep3 --> DeviationStep4
DeviationStep4 --> FinalStep1

FinalStep1 --> FinalStep2
FinalStep2 --> FinalStep3
FinalStep3 --> FinalStep4
FinalStep4 --> FinalStep5

@enduml

@startuml Migration Steps Overview
!theme plain
title Migration Steps Overview - All Phases

!define PHASE1_COLOR #E8F5E8
!define PHASE2_COLOR #FFF2CC
!define PHASE3_COLOR #FFE6CC
!define PHASE4_COLOR #F8CECC

package "Migration Steps Summary" {

  rectangle "Phase 1: Infrastructure Services" as P1 PHASE1_COLOR {
    rectangle "Configuration Service\n4 Steps" as Config
    rectangle "Audit Service\n4 Steps" as Audit
    rectangle "Notification Service\n4 Steps" as Notify
    rectangle "Integration Testing\n2 Steps" as Test1
  }

  rectangle "Phase 2: User Management" as P2 PHASE2_COLOR {
    rectangle "User Service Foundation\n4 Steps" as UserFound
    rectangle "Authentication & Authorization\n4 Steps" as Auth
    rectangle "Service Integration\n4 Steps" as UserInt
  }

  rectangle "Phase 3: Business Services" as P3 PHASE3_COLOR {
    rectangle "Profile Management Service\n4 Steps" as Profile
    rectangle "Integration Service\n4 Steps" as Integration
    rectangle "Logger Management Service\n4 Steps" as Logger
    rectangle "Service Integration\n4 Steps" as Test3
  }

  rectangle "Phase 4: Core Business Services" as P4 PHASE4_COLOR {
    rectangle "Trial Management Service\n4 Steps" as Trial
    rectangle "Shipment Management Service\n4 Steps" as Shipment
    rectangle "Deviation Management Service\n4 Steps" as Deviation
    rectangle "Final Integration & Go-Live\n5 Steps" as Final
  }
}

Config --> Audit
Audit --> Notify
Notify --> Test1
Test1 --> UserFound

UserFound --> Auth
Auth --> UserInt
UserInt --> Profile

Profile --> Integration
Integration --> Logger
Logger --> Test3
Test3 --> Trial

Trial --> Shipment
Shipment --> Deviation
Deviation --> Final

note bottom of P1
  Total: 14 Steps
  Focus: Infrastructure foundation
  Risk: LOW
end note

note bottom of P2
  Total: 12 Steps
  Focus: Authentication & authorization
  Risk: MEDIUM
end note

note bottom of P3
  Total: 16 Steps
  Focus: Business logic extraction
  Risk: MEDIUM
end note

note bottom of P4
  Total: 17 Steps
  Focus: Core business workflows
  Risk: HIGH
end note

@enduml
