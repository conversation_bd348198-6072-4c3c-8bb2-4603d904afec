@startuml Phase 1 Simple
!theme plain
title Phase 1: Infrastructure Services

rectangle "Configuration Service" as Config #lightgreen
rectangle "Audit Service" as Audit #lightgreen  
rectangle "Notification Service" as Notify #lightgreen
rectangle "Testing & Deployment" as Test #lightgreen

Config --> Audit : Complete
Audit --> Notify : Complete
Notify --> Test : Complete

note bottom of Config
  Extract: ConfigManager
  Create: REST APIs
  Add: Redis caching
end note

note bottom of Audit
  Extract: AuditTrailManager
  Add: Kafka events
  Create: Audit APIs
end note

note bottom of Notify
  Extract: NotificationManager
  Add: Message queues
  Create: Email service
end note

@enduml

@startuml Phase 2 Simple
!theme plain
title Phase 2: User Management

rectangle "User Service" as User #yellow
rectangle "Authentication" as Auth #yellow
rectangle "Authorization" as Authz #yellow
rectangle "Integration" as Integration #yellow

User --> Auth : Complete
Auth --> Authz : Complete
Authz --> Integration : Complete

note bottom of User
  Extract: UserEntity
  Extract: CompanyEntity
  Create: User APIs
end note

note bottom of Auth
  Add: JWT tokens
  Add: OAuth2/OIDC
  Create: Login APIs
end note

note bottom of Authz
  Add: Role-based access
  Add: Permissions
  Update: All services
end note

@enduml

@startuml Phase 3 Simple
!theme plain
title Phase 3: Business Services

rectangle "Profile Service" as Profile #orange
rectangle "Integration Service" as IntSvc #orange
rectangle "Logger Service" as Logger #orange
rectangle "Event Architecture" as Events #orange

Profile --> IntSvc : Complete
IntSvc --> Logger : Complete
Logger --> Events : Complete

note bottom of Profile
  Extract: ProfileEntity
  Add: Profile APIs
  Add: Versioning
end note

note bottom of IntSvc
  Extract: IRT integration
  Extract: KAA integration
  Add: Message routing
end note

note bottom of Logger
  Extract: ClinicalLoggerEntity
  Add: Sensor APIs
  Add: Live data streams
end note

@enduml

@startuml Phase 4 Simple
!theme plain
title Phase 4: Core Business Services

rectangle "Trial Service" as Trial #lightcoral
rectangle "Shipment Service" as Shipment #lightcoral
rectangle "Deviation Service" as Deviation #lightcoral
rectangle "Go Live" as GoLive #lightcoral

Trial --> Shipment : Complete
Shipment --> Deviation : Complete
Deviation --> GoLive : Complete

note bottom of Trial
  Extract: TrialEntity
  Add: Trial workflows
  Add: Site management
end note

note bottom of Shipment
  Extract: ShipmentEntity
  Add: Kit tracking
  Add: Inventory APIs
end note

note bottom of Deviation
  Extract: DeviationEntity
  Add: Detection logic
  Add: Adjustment workflows
end note

@enduml

@startuml All Phases Overview
!theme plain
title Migration Overview - All Phases

package "Phase 1\nInfrastructure" #lightgreen {
  [Configuration Service]
  [Audit Service]
  [Notification Service]
}

package "Phase 2\nUser Management" #yellow {
  [User Service]
  [Authentication]
  [Authorization]
}

package "Phase 3\nBusiness Services" #orange {
  [Profile Service]
  [Integration Service]
  [Logger Service]
}

package "Phase 4\nCore Business" #lightcoral {
  [Trial Service]
  [Shipment Service]
  [Deviation Service]
}

"Phase 1\nInfrastructure" --> "Phase 2\nUser Management"
"Phase 2\nUser Management" --> "Phase 3\nBusiness Services"
"Phase 3\nBusiness Services" --> "Phase 4\nCore Business"

note right of "Phase 1\nInfrastructure"
  Risk: LOW
  Foundation services
  14 steps total
end note

note right of "Phase 2\nUser Management"
  Risk: MEDIUM
  Central authentication
  12 steps total
end note

note right of "Phase 3\nBusiness Services"
  Risk: MEDIUM
  Business logic
  16 steps total
end note

note right of "Phase 4\nCore Business"
  Risk: HIGH
  Core workflows
  17 steps total
end note

@enduml

@startuml Service Extraction Map
!theme plain
title What Gets Extracted in Each Phase

map "Phase 1: Infrastructure" as phase1 {
  Configuration Service => ConfigManager class
  Audit Service => AuditTrailManager class
  Notification Service => NotificationManager class
}

map "Phase 2: User Management" as phase2 {
  User Service => UserEntity, CompanyEntity
  Authentication => JWT, OAuth2 setup
  Authorization => Role-based access control
}

map "Phase 3: Business Services" as phase3 {
  Profile Service => ProfileEntity, ProfileVersionEntity
  Integration Service => IrtIntegrationDetailsEntity
  Logger Service => ClinicalLoggerEntity, SensorLoggerEntity
}

map "Phase 4: Core Business" as phase4 {
  Trial Service => TrialEntity, TrialVersionEntity, SiteEntity
  Shipment Service => ShipmentEntity, DispensingUnitEntity
  Deviation Service => DeviationEntity, AdjustmentSetEntity
}

phase1 --> phase2
phase2 --> phase3
phase3 --> phase4

@enduml

@startuml Migration Steps Summary
!theme plain
title Migration Steps - Simple View

start

:Phase 1: Infrastructure Services;
note right
  • Configuration Service
  • Audit Service
  • Notification Service
end note

:Phase 2: User Management;
note right
  • User Service
  • Authentication
  • Authorization
end note

:Phase 3: Business Services;
note right
  • Profile Service
  • Integration Service
  • Logger Service
end note

:Phase 4: Core Business;
note right
  • Trial Service
  • Shipment Service
  • Deviation Service
end note

stop

@enduml
