@startuml Migration Phases Timeline
!theme plain
title Clinical Module - Migration Phases Timeline (12 Months)

!define PHASE1_COLOR #E8F5E8
!define PHASE2_COLOR #FFF2CC
!define PHASE3_COLOR #FFE6CC
!define PHASE4_COLOR #F8CECC

package "Timeline Overview" {
  rectangle "Month 1-2" as M12 PHASE1_COLOR
  rectangle "Month 3-4" as M34 PHASE2_COLOR
  rectangle "Month 5-8" as M58 PHASE3_COLOR
  rectangle "Month 9-12" as M912 PHASE4_COLOR

  M12 -right-> M34
  M34 -right-> M58
  M58 -right-> M912
}

package "Phase 1: Infrastructure Services (Months 1-2)" PHASE1_COLOR {
  rectangle "Week 1-2\nConfiguration Service" as W12
  rectangle "Week 3-4\nAudit Service" as W34
  rectangle "Week 5-6\nNotification Service" as W56
  rectangle "Week 7-8\nIntegration Testing" as W78

  W12 -down-> W34
  W34 -down-> W56
  W56 -down-> W78

  note right of W12
    Risk: LOW
    - Minimal business logic
    - Clear boundaries
    - Foundation for other services
  end note
}

package "Phase 2: User Management (Months 3-4)" PHASE2_COLOR {
  rectangle "Week 9-10\nUser Service Foundation" as W910
  rectangle "Week 11-12\nAuth & Authorization" as W1112
  rectangle "Week 13-14\nService Integration" as W1314
  rectangle "Week 15-16\nMigration & Testing" as W1516

  W910 -down-> W1112
  W1112 -down-> W1314
  W1314 -down-> W1516

  note right of W910
    Risk: MEDIUM
    - Central dependency
    - Security implications
    - Required by all services
  end note
}

package "Phase 3: Business Services (Months 5-8)" PHASE3_COLOR {
  rectangle "Month 5\nProfile Management" as M5
  rectangle "Month 6\nIntegration Service" as M6
  rectangle "Month 7\nLogger Management" as M7
  rectangle "Month 8\nService Integration" as M8

  M5 -down-> M6
  M6 -down-> M7
  M7 -down-> M8

  note right of M5
    Risk: MEDIUM
    - Moderate complexity
    - Event-driven patterns
    - Cross-service dependencies
  end note
}

package "Phase 4: Core Business (Months 9-12)" PHASE4_COLOR {
  rectangle "Month 9\nTrial Management" as M9
  rectangle "Month 10\nShipment Management" as M10
  rectangle "Month 11\nDeviation Management" as M11
  rectangle "Month 12\nFinal Integration" as M12_final

  M9 -down-> M10
  M10 -down-> M11
  M11 -down-> M12_final

  note right of M9
    Risk: HIGH
    - Complex workflows
    - Core business logic
    - Multiple dependencies
  end note
}

' Phase dependencies
W78 ..> W910 : Foundation Ready
W1516 ..> M5 : Auth Available
M8 ..> M9 : Services Ready

@enduml

@startuml Migration Risk Assessment
!theme plain
title Migration Risk Assessment by Phase

package "Phase 1: Infrastructure Services" #lightgreen {
  [Configuration Service] as Config
  [Audit Service] as Audit
  [Notification Service] as Notify

  note right of Config
    Risk: LOW
    - Minimal business logic
    - Clear boundaries
    - Low coupling
  end note
}

package "Phase 2: User Management" #yellow {
  [User Management Service] as User

  note right of User
    Risk: MEDIUM
    - Central dependency
    - Security implications
    - Moderate complexity
  end note
}

package "Phase 3: Business Services" #orange {
  [Profile Service] as Profile
  [Integration Service] as Integration
  [Logger Service] as Logger

  note right of Profile
    Risk: MEDIUM
    - Some cross-dependencies
    - Business logic complexity
    - Event-driven patterns
  end note
}

package "Phase 4: Core Business" #lightcoral {
  [Trial Service] as Trial
  [Shipment Service] as Shipment
  [Deviation Service] as Deviation

  note right of Trial
    Risk: HIGH
    - Complex workflows
    - Multiple dependencies
    - Core business logic
  end note
}

Config --> User : Foundation
Audit --> User : Logging
Notify --> User : Alerts

User --> Profile : Authentication
User --> Integration : Authorization
User --> Logger : User Context

Profile --> Trial : Configurations
Integration --> Trial : External Data
Logger --> Trial : Logger Assignment

Trial --> Shipment : Trial Context
Trial --> Deviation : Trial Data
Shipment --> Deviation : Shipment Data

@enduml

@startuml Service Dependencies
!theme plain
title Service Dependencies and Migration Order

!define INFRASTRUCTURE_COLOR #E8F5E8
!define USER_COLOR #FFF2CC
!define BUSINESS_COLOR #FFE6CC
!define CORE_COLOR #F8CECC

package "Infrastructure Services" INFRASTRUCTURE_COLOR {
  [Configuration Service] as Config
  [Audit Service] as Audit
  [Notification Service] as Notify
}

package "User Management" USER_COLOR {
  [User Service] as User
}

package "Business Services" BUSINESS_COLOR {
  [Profile Service] as Profile
  [Integration Service] as Integration
  [Logger Service] as Logger
}

package "Core Business Services" CORE_COLOR {
  [Trial Service] as Trial
  [Shipment Service] as Shipment
  [Deviation Service] as Deviation
}

' Dependencies
User ..> Config : uses
User ..> Audit : logs to
User ..> Notify : sends via

Profile ..> User : authenticates with
Profile ..> Config : reads from
Profile ..> Audit : logs to

Integration ..> User : authenticates with
Integration ..> Config : reads from
Integration ..> Audit : logs to

Logger ..> User : authenticates with
Logger ..> Config : reads from
Logger ..> Audit : logs to

Trial ..> User : authenticates with
Trial ..> Profile : uses profiles
Trial ..> Integration : integrates via
Trial ..> Audit : logs to
Trial ..> Notify : sends notifications

Shipment ..> User : authenticates with
Shipment ..> Trial : belongs to
Shipment ..> Integration : integrates via
Shipment ..> Audit : logs to

Deviation ..> User : authenticates with
Deviation ..> Trial : affects
Deviation ..> Shipment : affects
Deviation ..> Audit : logs to
Deviation ..> Notify : sends alerts

note bottom
  Migration Order:
  1. Infrastructure (no dependencies)
  2. User Management (depends on infrastructure)
  3. Business Services (depends on user + infrastructure)
  4. Core Business (depends on all previous)
end note

@enduml
