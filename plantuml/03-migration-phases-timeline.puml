@startuml Migration Phases Timeline
!theme plain
title Clinical Module - Migration Phases Timeline

gantt
project starts 2024-01-01

section Phase 1: Infrastructure Services
Extract Configuration Service    :done, config, 2024-01-01, 2w
Extract Audit Service           :done, audit, after config, 2w
Extract Notification Service    :done, notify, after audit, 2w
Integration Testing P1          :done, test1, after notify, 2w

section Phase 2: User Management
User Service Foundation         :active, user-found, after test1, 2w
Authentication & Authorization  :user-auth, after user-found, 2w
Service Integration            :user-int, after user-auth, 2w
Migration & Testing P2         :test2, after user-int, 2w

section Phase 3: Business Services
Profile Management Service     :profile, after test2, 4w
Integration Service           :integration, after profile, 4w
Logger Management Service     :logger, after integration, 4w
Service Integration P3        :test3, after logger, 4w

section Phase 4: Core Business
Trial Management Service      :trial, after test3, 4w
Shipment Management Service   :shipment, after trial, 4w
Deviation Management Service  :deviation, after shipment, 4w
Final Integration & Go-Live   :final, after deviation, 4w
@enduml

@startuml Migration Risk Assessment
!theme plain
title Migration Risk Assessment by Phase

package "Phase 1: Infrastructure Services" #lightgreen {
  [Configuration Service] as Config
  [Audit Service] as Audit
  [Notification Service] as Notify
  
  note right of Config
    Risk: LOW
    - Minimal business logic
    - Clear boundaries
    - Low coupling
  end note
}

package "Phase 2: User Management" #yellow {
  [User Management Service] as User
  
  note right of User
    Risk: MEDIUM
    - Central dependency
    - Security implications
    - Moderate complexity
  end note
}

package "Phase 3: Business Services" #orange {
  [Profile Service] as Profile
  [Integration Service] as Integration
  [Logger Service] as Logger
  
  note right of Profile
    Risk: MEDIUM
    - Some cross-dependencies
    - Business logic complexity
    - Event-driven patterns
  end note
}

package "Phase 4: Core Business" #lightcoral {
  [Trial Service] as Trial
  [Shipment Service] as Shipment
  [Deviation Service] as Deviation
  
  note right of Trial
    Risk: HIGH
    - Complex workflows
    - Multiple dependencies
    - Core business logic
  end note
}

Config --> User : Foundation
Audit --> User : Logging
Notify --> User : Alerts

User --> Profile : Authentication
User --> Integration : Authorization
User --> Logger : User Context

Profile --> Trial : Configurations
Integration --> Trial : External Data
Logger --> Trial : Logger Assignment

Trial --> Shipment : Trial Context
Trial --> Deviation : Trial Data
Shipment --> Deviation : Shipment Data

@enduml

@startuml Service Dependencies
!theme plain
title Service Dependencies and Migration Order

!define INFRASTRUCTURE_COLOR #E8F5E8
!define USER_COLOR #FFF2CC
!define BUSINESS_COLOR #FFE6CC
!define CORE_COLOR #F8CECC

package "Infrastructure Services" INFRASTRUCTURE_COLOR {
  [Configuration Service] as Config
  [Audit Service] as Audit
  [Notification Service] as Notify
}

package "User Management" USER_COLOR {
  [User Service] as User
}

package "Business Services" BUSINESS_COLOR {
  [Profile Service] as Profile
  [Integration Service] as Integration
  [Logger Service] as Logger
}

package "Core Business Services" CORE_COLOR {
  [Trial Service] as Trial
  [Shipment Service] as Shipment
  [Deviation Service] as Deviation
}

' Dependencies
User ..> Config : uses
User ..> Audit : logs to
User ..> Notify : sends via

Profile ..> User : authenticates with
Profile ..> Config : reads from
Profile ..> Audit : logs to

Integration ..> User : authenticates with
Integration ..> Config : reads from
Integration ..> Audit : logs to

Logger ..> User : authenticates with
Logger ..> Config : reads from
Logger ..> Audit : logs to

Trial ..> User : authenticates with
Trial ..> Profile : uses profiles
Trial ..> Integration : integrates via
Trial ..> Audit : logs to
Trial ..> Notify : sends notifications

Shipment ..> User : authenticates with
Shipment ..> Trial : belongs to
Shipment ..> Integration : integrates via
Shipment ..> Audit : logs to

Deviation ..> User : authenticates with
Deviation ..> Trial : affects
Deviation ..> Shipment : affects
Deviation ..> Audit : logs to
Deviation ..> Notify : sends alerts

note bottom
  Migration Order:
  1. Infrastructure (no dependencies)
  2. User Management (depends on infrastructure)
  3. Business Services (depends on user + infrastructure)
  4. Core Business (depends on all previous)
end note

@enduml
