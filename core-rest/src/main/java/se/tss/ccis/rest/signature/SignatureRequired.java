/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.rest.signature;

import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface SignatureRequired {
    /** Description text, i.e. "approve %s for %s"*/
    String descFormat();
    /** body.x = x property of html post body
     * entity.x = x property of hibernate entity
     * path.x = path parameter x
     * query.x = query parameter x
     * **/
    SourcedValue[] sourcedValues() default {};

    /**
     * Where the hibernate entity id can be found. e.g. path.id, query.newId etc.
     */
    SourcedValue defaultEntityId() default @SourcedValue(source = ArgSource.Path, valuePath = ""); //default to make it not required

}