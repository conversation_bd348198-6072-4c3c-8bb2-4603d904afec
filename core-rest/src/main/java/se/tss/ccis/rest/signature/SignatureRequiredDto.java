/*
 * ALL RIGHTS RESERVED. The content of this document is protected by copyright and constitutes trade secrets.
 * The content hereof may also be protected by patents, trademarks, design rights and other intellectual property rights.
 * The rights contained herein are owned or licensed by Temperature Sensitive Solutions Systems Sweden AB,
 * reg. no. 556278-0816, Sweden, ("TSS"), and may not be used, licensed, assigned, transferred, reproduced, copied,
 * disclosed, disseminated, modified or amended in any way not expressly and specifically granted by TSS in writing.
 *
 * TSS and TSS' suppliers reserves any and all rights not expressly granted to you in writing, including the right to take
 * any and all actions deemed appropriate to safeguard its rights in and to the content of this document.
 */

package se.tss.ccis.rest.signature;

import javax.ws.rs.core.Response;

public class SignatureRequiredDto {
    private String signatureDescription;

    public SignatureRequiredDto() {
    }

    public SignatureRequiredDto(String signatureDescription) {
        this.signatureDescription = signatureDescription;
    }

    public String getSignatureDescription() {
        return signatureDescription;
    }

    public void setSignatureDescription(String signatureDescription) {
        this.signatureDescription = signatureDescription;
    }

    public static Response response(String signatureDescription, boolean isSingleSignOn) {
        return Response.status(Response.Status.FORBIDDEN)
                .entity(new SignatureRequiredDto(signatureDescription))
                .header(isSingleSignOn ? "x-sso-signature-required" : "x-signature-required", "signature required")
                .build();
    }
}
