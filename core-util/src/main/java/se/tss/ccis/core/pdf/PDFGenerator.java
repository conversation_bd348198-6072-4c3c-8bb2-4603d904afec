package se.tss.ccis.core.pdf;

import com.lowagie.text.DocumentException;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfWriter;
import org.apache.commons.io.IOUtils;
import org.w3c.dom.Document;
import org.w3c.tidy.Configuration;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * A utility class for easy rendering of PDF documents from XHTML
 * 
 * <AUTHOR> AB
 * 
 */
public class PDFGenerator {
    
    private PDFGenerator() {}
    
	/**
	 * Create a PDF document from an URL
	 * 
	 * @param url The URL to PDF
	 * @return The PDF document as a byte array
	 * @throws IOException
	 * @throws PDFGenerationException 
	 */
	public static byte[] createPDF(String url) throws IOException, PDFGenerationException {
		return createPDF(null, url);
	}

    public static byte[] createPDF(byte[] data, String url) throws IOException, PDFGenerationException {
        
        try (ByteArrayOutputStream pdf = new ByteArrayOutputStream()) {
            if (data == null) {
                data = IOUtils.toByteArray(URI.create(url));
            }
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocument(parseHtml(data), url);
            renderer.layout();
            renderer.createPDF(pdf);
            return pdf.toByteArray();
        } catch (DocumentException e) {
            throw new PDFGenerationException(e);
        } 
    }
    
	/**
	 * Create a PDF document from an already grabbed web site
	 * 
	 * @param buf The byte data
	 * @param url The base URL of the web page
	 * @return The PDF document as a byte array
	 * @throws IOException
	 * @throws PDFGenerationException 
	 */
    public static byte[] createPDF(String content, byte[] buf, String url) throws IOException, PDFGenerationException {
        try (ByteArrayOutputStream baos = createByteArrayOutputStreamPDF(content, buf, url)) {
            return baos.toByteArray();
        }
	}
    /**
     * Create a PDF document from an already grabbed web site
     *
     * @param buf The byte data
     * @param url The base URL of the web page
     * @return The PDF document as a byte array
     * @throws IOException
     * @throws PDFGenerationException 
     */
    public static ByteArrayOutputStream createByteArrayOutputStreamPDF(String content, byte[] buf, String url)
            throws IOException, PDFGenerationException {

        try {
            ByteArrayOutputStream pdf = new ByteArrayOutputStream();
            ITextRenderer renderer = new ITextRenderer();
            if (buf != null) {
                renderer.getSharedContext().setReplacedElementFactory(new MediaReplacedElementFactory(renderer.getSharedContext().getReplacedElementFactory(), buf));
            }
            renderer.setDocument(parseHtml(content.getBytes(StandardCharsets.UTF_8)), url);
            renderer.layout();
            renderer.createPDF(pdf);
            return pdf;
        } catch (DocumentException e) {
            throw new PDFGenerationException(e);
        }
    }

    private static byte[] tidyHtml(byte[] data) throws IOException {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            Tidy tidy = new Tidy();
            tidy.setCharEncoding(Configuration.UTF8);
            tidy.setXHTML(true);
            tidy.parse(new ByteArrayInputStream(data), out);
            return out.toByteArray();
        }
    }
    
    private static Document parseHtml(byte[] data) throws IOException, PDFGenerationException {
        try {
            DocumentBuilder db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            db.setEntityResolver(new DTDHandler());
            return db.parse(new ByteArrayInputStream(tidyHtml(data)));
        } catch (ParserConfigurationException | SAXException e) {
            throw new PDFGenerationException(e);
        }
    }

    public static byte[] appendFiles(List<byte[]> sources) throws IOException, PDFGenerationException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            com.lowagie.text.Document document = new com.lowagie.text.Document();
            PdfWriter writer = PdfWriter.getInstance(document, baos);
            document.open();
            try {
                for(byte[] src : sources) {
                    PdfReader reader = new PdfReader(src);
                    int n = reader.getNumberOfPages();
                    PdfContentByte cb = writer.getDirectContent();
                    PdfImportedPage page;
                    int i = 0;
                    while (i < n) {
                        i++;
                        document.setPageSize(reader.getPageSizeWithRotation(i));
                        document.newPage();
                        page = writer.getImportedPage(reader, i);
                        cb.addTemplate(page,0,0);
                    }
                }
            } finally {
                document.close();
            }
            return baos.toByteArray();
        } catch (DocumentException e) {
            throw new PDFGenerationException(e);
        }
    }
    
    public static void writePdfToFile(String contents, String filename) throws IOException, PDFGenerationException {
        try (FileOutputStream fos = new FileOutputStream(filename)) {
            byte[] bytes = createPDF(contents, null, null);
            fos.write(bytes);
        }
    }
}
